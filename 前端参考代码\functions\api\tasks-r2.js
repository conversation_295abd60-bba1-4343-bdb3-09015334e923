/**
 * 基于R2的任务状态查询API
 * 支持向前兼容，能够读取KV中的历史数据
 */

import { R2TaskStatusManager } from '../utils/r2-task-status-manager.js';

export async function onRequestGet({ request, env }) {
    const url = new URL(request.url);
    const email = url.searchParams.get('email');
    const taskId = url.searchParams.get('taskId');
    const limit = parseInt(url.searchParams.get('limit')) || 50;
    const status = url.searchParams.get('status');
    const architecture = url.searchParams.get('architecture'); // 'r2', 'kv', 'both'

    if (!email) {
        return Response.json({ error: '邮箱参数不能为空' }, { status: 400 });
    }

    try {
        const statusManager = new R2TaskStatusManager(env.R2, env);
        
        if (taskId) {
            // 查询单个任务状态
            return await getSingleTaskStatus(taskId, email, statusManager, env, architecture);
        } else {
            // 查询用户所有任务
            return await getUserAllTasks(email, statusManager, env, { limit, status, architecture });
        }

    } catch (error) {
        console.error('R2任务查询失败:', error);
        return Response.json({ 
            error: '查询失败',
            details: error.message 
        }, { status: 500 });
    }
}

/**
 * 获取单个任务状态
 */
async function getSingleTaskStatus(taskId, email, statusManager, env, architecture = 'both') {
    let r2Status = null;
    let kvStatus = null;

    try {
        // 尝试从R2读取
        if (architecture === 'r2' || architecture === 'both') {
            r2Status = await statusManager.getTaskStatus(taskId, email);
        }

        // 尝试从KV读取（向前兼容）
        if (architecture === 'kv' || architecture === 'both') {
            const taskKey = `task:${taskId}`;
            const kvTask = await env.KV.get(taskKey, { type: 'json' });
            if (kvTask && kvTask.email === email) {
                kvStatus = R2TaskStatusManager.mapKvToR2Status(kvTask);
            }
        }

        // 优先返回R2状态，如果不存在则返回KV状态
        const finalStatus = r2Status || kvStatus;

        if (!finalStatus) {
            return Response.json({ error: '任务不存在' }, { status: 404 });
        }

        // 格式化用于前端显示
        const formattedStatus = R2TaskStatusManager.formatTaskForDisplay(finalStatus);

        return Response.json({
            success: true,
            task: formattedStatus,
            dataSource: r2Status ? 'R2' : 'KV',
            architecture: 'hybrid'
        });

    } catch (error) {
        console.error(`获取任务状态失败 ${taskId}:`, error);
        return Response.json({ 
            error: '获取任务状态失败',
            details: error.message 
        }, { status: 500 });
    }
}

/**
 * 获取用户所有任务
 */
async function getUserAllTasks(email, statusManager, env, options = {}) {
    const { limit = 50, status = null, architecture = 'both' } = options;
    
    let r2Tasks = [];
    let kvTasks = [];

    try {
        // 从R2获取任务
        if (architecture === 'r2' || architecture === 'both') {
            try {
                r2Tasks = await statusManager.getUserTasks(email, { limit, status });
                console.log(`📋 从R2获取到 ${r2Tasks.length} 个任务`);
            } catch (error) {
                console.warn('从R2获取任务失败:', error);
            }
        }

        // 从KV获取任务（向前兼容）
        if (architecture === 'kv' || architecture === 'both') {
            try {
                kvTasks = await getKvTasks(email, env, { limit, status });
                console.log(`📋 从KV获取到 ${kvTasks.length} 个任务`);
            } catch (error) {
                console.warn('从KV获取任务失败:', error);
            }
        }

        // 合并和去重任务
        const allTasks = mergeAndDeduplicateTasks(r2Tasks, kvTasks);
        
        // 按状态过滤
        let filteredTasks = allTasks;
        if (status) {
            filteredTasks = allTasks.filter(task => task.status === status);
        }

        // 限制数量
        const limitedTasks = filteredTasks.slice(0, limit);

        // 格式化用于前端显示
        const formattedTasks = limitedTasks.map(task => 
            R2TaskStatusManager.formatTaskForDisplay(task)
        );

        // 统计信息
        const statistics = {
            total: formattedTasks.length,
            r2Count: r2Tasks.length,
            kvCount: kvTasks.length,
            statusBreakdown: getStatusBreakdown(formattedTasks)
        };

        return Response.json({
            success: true,
            tasks: formattedTasks,
            statistics: statistics,
            architecture: 'hybrid',
            message: `获取到 ${formattedTasks.length} 个任务`
        });

    } catch (error) {
        console.error('获取用户任务失败:', error);
        return Response.json({ 
            error: '获取任务列表失败',
            details: error.message 
        }, { status: 500 });
    }
}

/**
 * 从KV获取任务（兼容性方法）
 */
async function getKvTasks(email, env, options = {}) {
    const { limit = 50, status = null } = options;
    
    try {
        const { list } = await env.KV.list({ prefix: 'task:' });
        const tasks = [];

        for (const key of list.keys) {
            if (tasks.length >= limit * 2) break; // 预留空间

            try {
                const task = await env.KV.get(key.name, { type: 'json' });
                if (task && task.email === email) {
                    // 跳过已经迁移到R2的任务（避免重复）
                    if (task.useR2Status) {
                        continue;
                    }
                    
                    const r2Status = R2TaskStatusManager.mapKvToR2Status(task);
                    if (!status || r2Status.status === status) {
                        tasks.push(r2Status);
                    }
                }
            } catch (error) {
                console.warn(`跳过无效任务: ${key.name}`, error);
            }
        }

        // 按创建时间排序
        tasks.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
        
        return tasks.slice(0, limit);
    } catch (error) {
        console.error('从KV获取任务失败:', error);
        return [];
    }
}

/**
 * 合并和去重任务
 */
function mergeAndDeduplicateTasks(r2Tasks, kvTasks) {
    const taskMap = new Map();
    
    // 添加R2任务（优先级更高）
    r2Tasks.forEach(task => {
        taskMap.set(task.id, { ...task, dataSource: 'R2' });
    });
    
    // 添加KV任务（如果不存在相同ID的R2任务）
    kvTasks.forEach(task => {
        if (!taskMap.has(task.id)) {
            taskMap.set(task.id, { ...task, dataSource: 'KV' });
        }
    });
    
    // 转换为数组并按创建时间排序
    const allTasks = Array.from(taskMap.values());
    allTasks.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    
    return allTasks;
}

/**
 * 获取状态统计
 */
function getStatusBreakdown(tasks) {
    const breakdown = {
        PENDING: 0,
        PROCESSING: 0,
        SUCCESS: 0,
        FAILED: 0
    };
    
    tasks.forEach(task => {
        if (breakdown.hasOwnProperty(task.status)) {
            breakdown[task.status]++;
        }
    });
    
    return breakdown;
}

/**
 * 批量状态查询
 */
export async function onRequestPost({ request, env }) {
    try {
        const { email, taskIds, architecture = 'both' } = await request.json();
        
        if (!email || !taskIds || !Array.isArray(taskIds)) {
            return Response.json({ error: '邮箱和任务ID列表不能为空' }, { status: 400 });
        }

        const statusManager = new R2TaskStatusManager(env.R2, env);
        const results = [];

        for (const taskId of taskIds) {
            try {
                let status = null;
                
                // 尝试从R2读取
                if (architecture === 'r2' || architecture === 'both') {
                    status = await statusManager.getTaskStatus(taskId, email);
                }
                
                // 如果R2中没有，尝试从KV读取
                if (!status && (architecture === 'kv' || architecture === 'both')) {
                    const taskKey = `task:${taskId}`;
                    const kvTask = await env.KV.get(taskKey, { type: 'json' });
                    if (kvTask && kvTask.email === email) {
                        status = R2TaskStatusManager.mapKvToR2Status(kvTask);
                    }
                }
                
                if (status) {
                    results.push({
                        taskId: taskId,
                        status: status,
                        dataSource: status.version ? 'R2' : 'KV'
                    });
                } else {
                    results.push({
                        taskId: taskId,
                        status: null,
                        error: '任务不存在'
                    });
                }
            } catch (error) {
                results.push({
                    taskId: taskId,
                    status: null,
                    error: error.message
                });
            }
        }

        return Response.json({
            success: true,
            results: results,
            total: results.length,
            architecture: 'hybrid'
        });

    } catch (error) {
        console.error('批量状态查询失败:', error);
        return Response.json({ 
            error: '批量查询失败',
            details: error.message 
        }, { status: 500 });
    }
}
