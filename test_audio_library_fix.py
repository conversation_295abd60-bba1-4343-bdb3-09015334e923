#!/usr/bin/env python3
"""
测试音频库API修复的脚本
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ.setdefault('PYTHONPATH', str(project_root))

def test_duration_formatting():
    """测试时长格式化功能"""
    print("测试时长格式化功能...")
    
    # 模拟不同类型的duration值
    test_cases = [
        (120, "02:00"),           # 整数
        (120.5, "02:00"),         # float
        (3661.25, "01:01:01"),    # 超过1小时的float
        (59.9, "00:59"),          # 不到1分钟
        (0, "00:00"),             # 零值
        (None, None),             # 空值
    ]
    
    for duration, expected in test_cases:
        try:
            # 模拟API中的格式化逻辑
            duration_formatted = None
            if duration is not None:
                # 确保duration是数字类型，并转换为整数秒数进行格式化
                total_seconds = int(float(duration))
                hours = total_seconds // 3600
                minutes = (total_seconds % 3600) // 60
                seconds = total_seconds % 60
                if hours > 0:
                    duration_formatted = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
                else:
                    duration_formatted = f"{minutes:02d}:{seconds:02d}"
            
            print(f"  输入: {duration} -> 输出: {duration_formatted} (期望: {expected})")
            
            if duration_formatted == expected:
                print("    ✅ 通过")
            else:
                print("    ❌ 失败")
                return False
                
        except Exception as e:
            print(f"    ❌ 错误: {str(e)}")
            return False
    
    print("✅ 时长格式化测试通过")
    return True

def test_audio_files_processing():
    """测试音频文件处理逻辑"""
    print("\n测试音频文件处理逻辑...")
    
    # 测试用例：新的合并音频格式
    new_format_audio_files = [
        {
            "filename": "merged_audio.mp3",
            "url": "/files/users/1/tasks/123/merged_audio.mp3",
            "title": "测试音频",
            "duration": 120.5,
            "size": 482000,
            "format": "mp3",
            "segments_merged": 3
        }
    ]
    
    # 测试用例：旧的分段音频格式
    old_format_audio_files = [
        {
            "filename": "segment_0001.mp3",
            "url": "/files/users/1/tasks/123/segment_0001.mp3",
            "duration": 60,
            "size": 240000
        },
        {
            "filename": "segment_0002.mp3",
            "url": "/files/users/1/tasks/123/segment_0002.mp3",
            "duration": 60,
            "size": 240000
        }
    ]
    
    test_cases = [
        (new_format_audio_files, 3, "新格式合并音频"),
        (old_format_audio_files, 2, "旧格式分段音频"),
        ([], 1, "空音频文件列表"),
        (None, 1, "None音频文件"),
    ]
    
    for audio_files, expected_chapters, description in test_cases:
        try:
            print(f"  测试: {description}")
            
            # 模拟API中的章节数计算逻辑
            total_chapters = 1  # 默认为1
            if audio_files:
                if isinstance(audio_files, list):
                    # 新格式：单个合并音频文件的列表，或旧格式的多个分段文件
                    if len(audio_files) == 1 and isinstance(audio_files[0], dict):
                        # 检查是否是合并音频文件（包含segments_merged字段）
                        merged_segments = audio_files[0].get('segments_merged')
                        if merged_segments:
                            total_chapters = merged_segments  # 使用原始分段数
                        else:
                            total_chapters = len(audio_files)  # 旧格式
                    else:
                        total_chapters = len(audio_files)  # 多个分段文件
                else:
                    total_chapters = 1
            
            print(f"    输入: {len(audio_files) if audio_files else 0} 个文件 -> 输出: {total_chapters} 章节 (期望: {expected_chapters})")
            
            if total_chapters == expected_chapters:
                print("    ✅ 通过")
            else:
                print("    ❌ 失败")
                return False
                
        except Exception as e:
            print(f"    ❌ 错误: {str(e)}")
            return False
    
    print("✅ 音频文件处理测试通过")
    return True

def test_task_processing_compatibility():
    """测试任务处理服务兼容性"""
    print("\n测试任务处理服务兼容性...")
    
    # 测试新格式转换结果
    new_conversion_result = {
        "audio_file": {
            "filename": "merged_audio.mp3",
            "url": "/files/users/1/tasks/123/merged_audio.mp3",
            "duration": 120.5,
            "segments_merged": 3
        },
        "total_duration": 120.5,
        "word_count": 500,
        "title": "测试音频"
    }
    
    # 测试旧格式转换结果
    old_conversion_result = {
        "audio_files": [
            {"filename": "segment_0001.mp3", "url": "/files/segment_0001.mp3"},
            {"filename": "segment_0002.mp3", "url": "/files/segment_0002.mp3"}
        ],
        "playlist_url": "/files/playlist.json",
        "total_duration": 120,
        "word_count": 500,
        "title": "测试音频"
    }
    
    test_cases = [
        (new_conversion_result, "新格式"),
        (old_conversion_result, "旧格式")
    ]
    
    for conversion_result, description in test_cases:
        try:
            print(f"  测试: {description}")
            
            # 模拟task_processing_service中的逻辑
            if "audio_file" in conversion_result:
                # 新的合并音频格式：单个音频文件
                audio_file = conversion_result["audio_file"]
                task_audio_files = [audio_file]  # 转换为列表格式以保持兼容性
                task_playlist_url = None  # 不再使用播放列表
                print("    ✅ 检测到新格式")
            else:
                # 兼容旧格式（如果存在）
                task_audio_files = conversion_result.get("audio_files", [])
                task_playlist_url = conversion_result.get("playlist_url")
                print("    ✅ 使用旧格式兼容模式")
            
            task_total_duration = float(conversion_result["total_duration"]) if conversion_result["total_duration"] else None
            
            print(f"    处理结果:")
            print(f"      audio_files: {len(task_audio_files)} 个文件")
            print(f"      playlist_url: {task_playlist_url}")
            print(f"      total_duration: {task_total_duration} ({type(task_total_duration).__name__})")
            
        except Exception as e:
            print(f"    ❌ 错误: {str(e)}")
            return False
    
    print("✅ 任务处理服务兼容性测试通过")
    return True

if __name__ == "__main__":
    print("音频库API修复测试")
    print("=" * 50)
    
    # 运行所有测试
    tests = [
        test_duration_formatting,
        test_audio_files_processing,
        test_task_processing_compatibility
    ]
    
    all_passed = True
    for test in tests:
        if not test():
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有测试通过！音频库API修复成功。")
        print("\n修复内容:")
        print("✅ 支持float类型的duration格式化")
        print("✅ 正确处理新的音频合并格式")
        print("✅ 保持对旧格式的兼容性")
        print("✅ 任务处理服务数据类型兼容")
    else:
        print("\n❌ 部分测试失败！请检查修复代码。")
        sys.exit(1)
