// 修复播放列表 API
import { EnhancedR2StorageManager } from '../../utils/enhanced-r2-storage.js';

export async function onRequestPost({ request, env }) {
    try {
        const { email, taskId } = await request.json();
        
        if (!email || !taskId) {
            return Response.json({ 
                success: false, 
                error: '缺少必要参数：email 和 taskId' 
            }, { status: 400 });
        }

        console.log(`修复播放列表: 开始处理 - 邮箱: ${email}, 任务: ${taskId}`);

        // 初始化存储管理器
        const r2Storage = new EnhancedR2StorageManager(env.R2);
        const emailHash = r2Storage.hashEmail(email);
        const basePath = `users/${emailHash}/audiobooks/${taskId}`;

        console.log(`修复播放列表: 基础路径 = ${basePath}`);

        // 检查R2存储中的文件
        const listResult = await env.R2.list({ prefix: basePath });
        console.log(`修复播放列表: 找到 ${listResult.objects.length} 个文件`);

        if (listResult.objects.length === 0) {
            return Response.json({
                success: false,
                error: '未找到任何相关文件',
                debug: { basePath, emailHash }
            }, { status: 404 });
        }

        // 列出所有文件
        const files = listResult.objects.map(obj => ({
            key: obj.key,
            size: obj.size,
            lastModified: obj.uploaded
        }));

        console.log('修复播放列表: 文件列表:');
        files.forEach(file => {
            console.log(`  - ${file.key} (${Math.round(file.size / 1024)} KB)`);
        });

        // 检查是否有元数据文件
        const metadataFile = files.find(f => f.key.endsWith('/metadata.json'));
        if (!metadataFile) {
            return Response.json({
                success: false,
                error: '未找到元数据文件',
                files: files.map(f => f.key)
            }, { status: 404 });
        }

        // 读取元数据
        const metadataObj = await env.R2.get(metadataFile.key);
        const metadata = JSON.parse(await metadataObj.text());
        
        console.log(`修复播放列表: 元数据 - 标题: ${metadata.title}, 章节数: ${metadata.chapters?.length || 0}`);

        if (!metadata.chapters || metadata.chapters.length === 0) {
            return Response.json({
                success: false,
                error: '元数据中没有章节信息',
                metadata: metadata
            }, { status: 400 });
        }

        // 检查章节音频文件是否存在
        const chapterFiles = files.filter(f => f.key.includes('/chapter_') && f.key.endsWith('.mp3'));
        console.log(`修复播放列表: 找到 ${chapterFiles.length} 个章节音频文件`);

        if (chapterFiles.length === 0) {
            return Response.json({
                success: false,
                error: '未找到章节音频文件',
                files: files.map(f => f.key)
            }, { status: 404 });
        }

        // 重新生成播放列表
        const jsonPlaylist = {
            title: metadata.title || '未知标题',
            totalChapters: metadata.chapters.length,
            totalDuration: metadata.totalDuration || 0,
            chapters: metadata.chapters.map((chapter, index) => ({
                index: index + 1,
                title: chapter.title || `第${index + 1}章`,
                duration: chapter.duration || 0,
                url: `/api/audio/chapter/${taskId}/${index + 1}.mp3`,
                wordCount: chapter.wordCount || 0,
                isPartial: chapter.isPartial || false,
                originalTitle: chapter.originalTitle || chapter.title,
                partIndex: chapter.partIndex || 1,
                totalParts: chapter.totalParts || 1
            }))
        };

        // 保存JSON播放列表
        const jsonPlaylistPath = `${basePath}/playlist.json`;
        await env.R2.put(jsonPlaylistPath, JSON.stringify(jsonPlaylist, null, 2), {
            httpMetadata: {
                contentType: 'application/json'
            }
        });

        // 生成M3U8播放列表
        const m3u8Content = [
            '#EXTM3U',
            '#EXT-X-VERSION:3',
            '#EXT-X-PLAYLIST-TYPE:VOD',
            ''
        ];

        metadata.chapters.forEach((chapter, index) => {
            m3u8Content.push(`#EXTINF:${chapter.duration || 0},${chapter.title || `第${index + 1}章`}`);
            m3u8Content.push(`/api/audio/chapter/${taskId}/${index + 1}.mp3`);
        });

        const m3u8PlaylistPath = `${basePath}/playlist.m3u8`;
        await env.R2.put(m3u8PlaylistPath, m3u8Content.join('\n'), {
            httpMetadata: {
                contentType: 'application/vnd.apple.mpegurl'
            }
        });

        console.log(`修复播放列表: 播放列表重新生成成功`);

        // 验证播放列表是否可以正常读取
        const verifyPlaylist = await r2Storage.getBookPlaylist(email, taskId);
        const playlistWorking = !!verifyPlaylist;

        return Response.json({
            success: true,
            message: '播放列表修复成功',
            result: {
                taskId,
                email,
                emailHash,
                basePath,
                totalFiles: files.length,
                chapterFiles: chapterFiles.length,
                totalChapters: metadata.chapters.length,
                playlistGenerated: true,
                playlistWorking,
                generatedFiles: [
                    jsonPlaylistPath,
                    m3u8PlaylistPath
                ]
            }
        });

    } catch (error) {
        console.error('修复播放列表失败:', error);
        return Response.json({
            success: false,
            error: '服务器内部错误',
            details: error.message
        }, { status: 500 });
    }
}

export async function onRequestOptions() {
    return new Response(null, {
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type',
            'Access-Control-Max-Age': '86400'
        }
    });
} 