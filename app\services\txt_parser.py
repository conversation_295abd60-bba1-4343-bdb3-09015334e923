"""
TXT文件解析服务
提取TXT文件的章节结构和文本内容，与EPUB解析器保持接口一致
基于原始参考代码完整迁移，保持所有算法逻辑不变
"""

import os
import tempfile
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
import httpx
import chardet

from app.core.logging import get_logger

logger = get_logger(__name__)


@dataclass
class TxtMetadata:
    """TXT元数据模型"""
    
    title: str
    author: str = "未知作者"
    language: str = "zh-CN"
    identifier: str = ""
    publisher: Optional[str] = None
    description: Optional[str] = None
    total_chapters: int = 0
    total_words: int = 0


class TxtParser:
    """TXT文件解析器"""
    
    def __init__(self):
        self.content = ""
        self.chapters: List = []
        self.metadata: Optional[TxtMetadata] = None
    
    async def parse_from_file(self, file_path: str) -> Tuple[TxtMetadata, List]:
        """从文件路径解析TXT"""
        try:
            logger.info(f"开始解析TXT文件: {file_path}")
            
            # 读取TXT文件，自动检测编码
            self.content = self._read_file_with_encoding(file_path)
            
            # 生成元数据
            self.metadata = self._extract_metadata(file_path)
            
            # 解析章节内容
            self.chapters = self._extract_chapters()
            
            # 计算统计信息
            self._calculate_statistics()
            
            logger.info(
                f"TXT解析完成: {len(self.chapters)}个章节, 共{self.metadata.total_words}字"
            )
            
            return self.metadata, self.chapters
            
        except Exception as e:
            logger.error(f"TXT解析失败: {str(e)}")
            raise Exception(f"TXT文件解析失败: {str(e)}")
    
    async def parse_from_url(self, file_url: str) -> Tuple[TxtMetadata, List]:
        """从URL下载并解析TXT"""
        try:
            logger.info(f"从URL下载TXT文件: {file_url}")
            
            # 下载文件到临时目录
            async with httpx.AsyncClient() as client:
                response = await client.get(file_url)
                response.raise_for_status()
                
                # 创建临时文件
                with tempfile.NamedTemporaryFile(
                    suffix=".txt", delete=False
                ) as temp_file:
                    temp_file.write(response.content)
                    temp_path = temp_file.name
            
            try:
                # 解析临时文件
                return await self.parse_from_file(temp_path)
            finally:
                # 清理临时文件
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
                    
        except Exception as e:
            logger.error(f"从URL解析TXT失败: {str(e)}")
            raise Exception(f"从URL下载TXT文件失败: {str(e)}")
    
    def _read_file_with_encoding(self, file_path: str) -> str:
        """读取文件并自动检测编码"""
        try:
            # 首先尝试检测编码
            with open(file_path, 'rb') as f:
                raw_data = f.read()
                result = chardet.detect(raw_data)
                encoding = result['encoding'] or 'utf-8'
            
            # 使用检测到的编码读取文件
            with open(file_path, 'r', encoding=encoding) as f:
                content = f.read()
                
            logger.info(f"文件编码检测结果: {encoding}")
            return content
            
        except Exception as e:
            # 如果检测失败，尝试常见编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16', 'ascii']
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read()
                    logger.info(f"使用编码 {encoding} 成功读取文件")
                    return content
                except UnicodeDecodeError:
                    continue
            
            raise Exception(f"无法读取文件，尝试了多种编码格式: {str(e)}")
    
    def _extract_metadata(self, file_path: str) -> TxtMetadata:
        """提取TXT元数据"""
        try:
            # 从文件名提取标题
            filename = os.path.basename(file_path)
            title = os.path.splitext(filename)[0]
            
            # 尝试从内容开头提取更好的标题
            lines = self.content.split('\n')[:10]  # 检查前10行
            for line in lines:
                line = line.strip()
                if line and len(line) < 100:
                    # 如果包含书名相关关键词，可能是标题
                    if any(keyword in line for keyword in ['书名', '标题', '题目', '作品']):
                        title = line.replace('书名:', '').replace('标题:', '').replace('题目:', '').replace('作品:', '').strip()
                        break
                    # 如果是第一个非空行且看起来像标题
                    elif line == lines[0].strip() and not line.startswith('第') and len(line) > 2:
                        title = line
                        break
            
            return TxtMetadata(
                title=title,
                author="未知作者",
                language="zh-CN",
                identifier=f"txt_{hash(self.content)}",
            )
            
        except Exception as e:
            logger.warning(f"提取元数据时出现警告: {str(e)}")
            return TxtMetadata(
                title="未知标题",
                author="未知作者", 
                language="zh-CN",
                identifier=""
            )
    
    def _extract_chapters(self) -> List:
        """提取章节内容 - TXT文件始终作为单一完整文档处理"""
        from app.services.epub_parser import Chapter  # 重用Chapter类

        chapters = []

        try:
            # TXT文件始终作为单个章节处理，不进行任何分割
            if self.content.strip():
                # 使用文件标题作为章节标题，如果没有则使用默认标题
                chapter_title = self.metadata.title if self.metadata else "完整文档"

                chapter = Chapter(
                    id="txt_full_document",
                    title=chapter_title,
                    content=self.content.strip(),
                    order=1,
                    level=1,
                    word_count=len(self.content.strip())
                )
                chapters.append(chapter)

            return chapters

        except Exception as e:
            logger.error(f"提取章节内容失败: {str(e)}")
            return []
    
    # 注释：TXT文件不再需要章节分割功能，以下方法已废弃
    # def _split_by_chapters(self) -> List[Tuple[str, str]]:
    #     """按章节标记分割文本 - 已废弃，TXT文件始终作为单一文档处理"""
    #     pass
    
    # def _split_by_paragraphs_or_length(self) -> List[Tuple[str, str]]:
    #     """按段落或长度分割文本 - 已废弃，TXT文件始终作为单一文档处理"""
    #     pass
    
    def _calculate_statistics(self):
        """计算统计信息"""
        if self.metadata:
            self.metadata.total_chapters = len(self.chapters)
            self.metadata.total_words = sum(
                chapter.word_count for chapter in self.chapters
            )
    
    def get_chapter_hierarchy(self) -> Dict:
        """获取章节层级结构"""
        hierarchy = {
            "title": self.metadata.title if self.metadata else "未知标题",
            "chapters": [],
        }
        
        # 构建层级结构
        for chapter in self.chapters:
            chapter_data = {
                "id": chapter.id,
                "title": chapter.title,
                "order": chapter.order,
                "level": chapter.level,
                "word_count": chapter.word_count,
                "children": [],
            }
            hierarchy["chapters"].append(chapter_data)
        
        return hierarchy
