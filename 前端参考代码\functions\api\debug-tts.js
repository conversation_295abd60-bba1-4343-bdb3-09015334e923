// TTS调试工具 - 诊断TTS API问题
export async function onRequestPost({ request, env }) {
    try {
        const { testText = "这是一个测试文本，用于检查TTS API是否正常工作。" } = await request.json();
        
        console.log('=== TTS调试开始 ===');
        console.log(`测试文本: ${testText}`);
        
        const debugResults = {
            timestamp: new Date().toISOString(),
            testText: testText,
            steps: []
        };

        // 步骤1: 测试API连接性
        debugResults.steps.push({
            step: 1,
            name: '测试API连接性',
            status: 'running'
        });

        const apiUrl = "https://otts.api.zwei.de.eu.org/v1/audio/speech";
        const apiKey = "sk-Zwei";

        try {
            // 简单的连接测试
            const testResponse = await fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model: "zh-CN-XiaochenMultilingualNeural",
                    input: "测试",
                    voice_settings: {
                        speed: 0,
                        pitch: 0,
                        output_format: "audio-24khz-160kbitrate-mono-mp3"
                    }
                })
            });

            debugResults.steps[0].status = 'completed';
            debugResults.steps[0].responseStatus = testResponse.status;
            debugResults.steps[0].responseStatusText = testResponse.statusText;
            debugResults.steps[0].responseHeaders = Object.fromEntries(testResponse.headers.entries());

            if (!testResponse.ok) {
                const errorBody = await testResponse.text();
                debugResults.steps[0].errorBody = errorBody;
                debugResults.steps[0].error = `API返回错误: ${testResponse.status} ${testResponse.statusText}`;
            } else {
                const audioBuffer = await testResponse.arrayBuffer();
                debugResults.steps[0].audioSize = audioBuffer.byteLength;
                debugResults.steps[0].success = true;
            }

        } catch (error) {
            debugResults.steps[0].status = 'failed';
            debugResults.steps[0].error = error.message;
            debugResults.steps[0].stack = error.stack;
        }

        // 步骤2: 测试文本清理
        debugResults.steps.push({
            step: 2,
            name: '测试文本清理',
            status: 'running'
        });

        try {
            const cleanedText = testText.trim()
                .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // 移除控制字符
                .replace(/\s+/g, ' '); // 规范化空白字符

            debugResults.steps[1].status = 'completed';
            debugResults.steps[1].originalLength = testText.length;
            debugResults.steps[1].cleanedLength = cleanedText.length;
            debugResults.steps[1].cleanedText = cleanedText.substring(0, 200);
            debugResults.steps[1].hasControlChars = testText !== cleanedText;
            debugResults.steps[1].success = true;

        } catch (error) {
            debugResults.steps[1].status = 'failed';
            debugResults.steps[1].error = error.message;
        }

        // 步骤3: 测试不同的文本内容
        debugResults.steps.push({
            step: 3,
            name: '测试不同文本内容',
            status: 'running'
        });

        const testTexts = [
            "你好",
            "Hello World",
            "这是一个中文测试。",
            "This is an English test.",
            "混合语言测试 Mixed language test 123",
            testText.substring(0, 100) // 用户提供的文本的前100字符
        ];

        const textTestResults = [];

        for (let i = 0; i < testTexts.length; i++) {
            const currentText = testTexts[i];
            try {
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        model: "zh-CN-XiaochenMultilingualNeural",
                        input: currentText,
                        voice_settings: {
                            speed: 0,
                            pitch: 0,
                            output_format: "audio-24khz-160kbitrate-mono-mp3"
                        }
                    })
                });

                const result = {
                    text: currentText,
                    status: response.status,
                    statusText: response.statusText,
                    success: response.ok
                };

                if (!response.ok) {
                    const errorBody = await response.text();
                    result.errorBody = errorBody;
                } else {
                    const audioBuffer = await response.arrayBuffer();
                    result.audioSize = audioBuffer.byteLength;
                }

                textTestResults.push(result);

            } catch (error) {
                textTestResults.push({
                    text: currentText,
                    error: error.message,
                    success: false
                });
            }

            // 添加延迟避免API限制
            if (i < testTexts.length - 1) {
                await new Promise(resolve => setTimeout(resolve, 200));
            }
        }

        debugResults.steps[2].status = 'completed';
        debugResults.steps[2].testResults = textTestResults;
        debugResults.steps[2].success = true;

        // 步骤4: 检查API限制和配额
        debugResults.steps.push({
            step: 4,
            name: '检查API状态',
            status: 'running'
        });

        try {
            // 尝试快速连续调用来测试限制
            const rapidCalls = [];
            for (let i = 0; i < 3; i++) {
                rapidCalls.push(
                    fetch(apiUrl, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${apiKey}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            model: "zh-CN-XiaochenMultilingualNeural",
                            input: `快速测试${i + 1}`,
                            voice_settings: {
                                speed: 0,
                                pitch: 0,
                                output_format: "audio-24khz-160kbitrate-mono-mp3"
                            }
                        })
                    })
                );
            }

            const rapidResults = await Promise.allSettled(rapidCalls);
            const rapidTestResults = rapidResults.map((result, index) => {
                if (result.status === 'fulfilled') {
                    return {
                        call: index + 1,
                        status: result.value.status,
                        statusText: result.value.statusText,
                        success: result.value.ok
                    };
                } else {
                    return {
                        call: index + 1,
                        error: result.reason.message,
                        success: false
                    };
                }
            });

            debugResults.steps[3].status = 'completed';
            debugResults.steps[3].rapidTestResults = rapidTestResults;
            debugResults.steps[3].success = true;

        } catch (error) {
            debugResults.steps[3].status = 'failed';
            debugResults.steps[3].error = error.message;
        }

        // 生成诊断报告
        const diagnosis = generateDiagnosis(debugResults);
        debugResults.diagnosis = diagnosis;

        console.log('=== TTS调试完成 ===');
        console.log('诊断结果:', diagnosis);

        return Response.json(debugResults, {
            headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            }
        });

    } catch (error) {
        console.error('TTS调试失败:', error);
        return Response.json({
            error: error.message,
            stack: error.stack,
            timestamp: new Date().toISOString()
        }, { 
            status: 500,
            headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            }
        });
    }
}

// 生成诊断报告
function generateDiagnosis(debugResults) {
    const diagnosis = {
        overall: 'unknown',
        issues: [],
        recommendations: []
    };

    // 检查API连接
    const apiTest = debugResults.steps[0];
    if (apiTest.status === 'failed') {
        diagnosis.overall = 'critical';
        diagnosis.issues.push('API连接失败');
        diagnosis.recommendations.push('检查网络连接和API地址');
    } else if (apiTest.responseStatus === 500) {
        diagnosis.overall = 'critical';
        diagnosis.issues.push('API服务器内部错误');
        diagnosis.recommendations.push('API服务可能暂时不可用，请稍后重试');
    } else if (apiTest.responseStatus === 401) {
        diagnosis.overall = 'critical';
        diagnosis.issues.push('API密钥无效');
        diagnosis.recommendations.push('检查API密钥是否正确');
    } else if (apiTest.responseStatus === 429) {
        diagnosis.overall = 'warning';
        diagnosis.issues.push('API调用频率限制');
        diagnosis.recommendations.push('减少调用频率，添加延迟');
    } else if (apiTest.success) {
        diagnosis.overall = 'good';
    }

    // 检查文本测试结果
    const textTest = debugResults.steps[2];
    if (textTest.testResults) {
        const failedTests = textTest.testResults.filter(t => !t.success);
        if (failedTests.length > 0) {
            diagnosis.issues.push(`${failedTests.length}个文本测试失败`);
            
            // 分析失败原因
            const error500Count = failedTests.filter(t => t.status === 500).length;
            if (error500Count > 0) {
                diagnosis.issues.push('多个文本都返回500错误');
                diagnosis.recommendations.push('API服务可能存在问题，建议联系服务提供商');
            }
        }
    }

    // 检查快速调用测试
    const rapidTest = debugResults.steps[3];
    if (rapidTest.rapidTestResults) {
        const rapidFailures = rapidTest.rapidTestResults.filter(t => !t.success);
        if (rapidFailures.length > 0) {
            diagnosis.issues.push('快速连续调用失败');
            diagnosis.recommendations.push('增加API调用之间的延迟');
        }
    }

    return diagnosis;
}

// GET请求处理 - 返回调试页面
export async function onRequestGet({ request, env }) {
    const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TTS调试工具</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .step { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 3px; }
        pre { background: #f1f1f1; padding: 10px; border-radius: 3px; overflow-x: auto; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        textarea { width: 100%; height: 100px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>TTS API调试工具</h1>
    
    <div class="debug-section">
        <h2>测试TTS API</h2>
        <p>这个工具可以帮助诊断TTS API的问题，特别是500内部服务器错误。</p>
        
        <label for="testText">测试文本:</label>
        <textarea id="testText" placeholder="输入要测试的文本...">这是一个测试文本，用于检查TTS API是否正常工作。</textarea>
        
        <button onclick="runDebug()">开始调试</button>
    </div>
    
    <div id="results"></div>

    <script>
        async function runDebug() {
            const testText = document.getElementById('testText').value;
            const resultsDiv = document.getElementById('results');
            
            resultsDiv.innerHTML = '<div class="debug-section">正在运行调试测试...</div>';
            
            try {
                const response = await fetch('/api/debug-tts', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ testText })
                });
                
                const results = await response.json();
                displayResults(results);
                
            } catch (error) {
                resultsDiv.innerHTML = \`<div class="debug-section error">
                    <h3>调试失败</h3>
                    <p>错误: \${error.message}</p>
                </div>\`;
            }
        }
        
        function displayResults(results) {
            const resultsDiv = document.getElementById('results');
            
            let html = \`<div class="debug-section">
                <h2>调试结果</h2>
                <p><strong>时间:</strong> \${results.timestamp}</p>
            </div>\`;
            
            // 显示诊断结果
            if (results.diagnosis) {
                const diagClass = results.diagnosis.overall === 'good' ? 'success' : 
                                results.diagnosis.overall === 'warning' ? 'warning' : 'error';
                
                html += \`<div class="debug-section \${diagClass}">
                    <h3>诊断结果</h3>
                    <p><strong>总体状态:</strong> \${results.diagnosis.overall}</p>\`;
                
                if (results.diagnosis.issues.length > 0) {
                    html += '<p><strong>发现的问题:</strong></p><ul>';
                    results.diagnosis.issues.forEach(issue => {
                        html += \`<li>\${issue}</li>\`;
                    });
                    html += '</ul>';
                }
                
                if (results.diagnosis.recommendations.length > 0) {
                    html += '<p><strong>建议:</strong></p><ul>';
                    results.diagnosis.recommendations.forEach(rec => {
                        html += \`<li>\${rec}</li>\`;
                    });
                    html += '</ul>';
                }
                
                html += '</div>';
            }
            
            // 显示详细步骤
            html += '<div class="debug-section"><h3>详细测试步骤</h3>';
            results.steps.forEach(step => {
                const stepClass = step.status === 'completed' && step.success ? 'success' : 
                                step.status === 'failed' ? 'error' : '';
                
                html += \`<div class="step \${stepClass}">
                    <h4>步骤 \${step.step}: \${step.name}</h4>
                    <p><strong>状态:</strong> \${step.status}</p>\`;
                
                if (step.error) {
                    html += \`<p><strong>错误:</strong> \${step.error}</p>\`;
                }
                
                if (step.responseStatus) {
                    html += \`<p><strong>HTTP状态:</strong> \${step.responseStatus} \${step.responseStatusText}</p>\`;
                }
                
                if (step.errorBody) {
                    html += \`<p><strong>错误详情:</strong></p><pre>\${step.errorBody}</pre>\`;
                }
                
                if (step.testResults) {
                    html += '<p><strong>文本测试结果:</strong></p>';
                    step.testResults.forEach(result => {
                        html += \`<div style="margin: 5px 0; padding: 5px; background: \${result.success ? '#d4edda' : '#f8d7da'};">
                            文本: "\${result.text}" - 状态: \${result.status || 'N/A'} - \${result.success ? '成功' : '失败'}
                            \${result.errorBody ? '<br>错误: ' + result.errorBody : ''}
                        </div>\`;
                    });
                }
                
                html += '</div>';
            });
            html += '</div>';
            
            resultsDiv.innerHTML = html;
        }
    </script>
</body>
</html>`;

    return new Response(html, {
        headers: {
            'Content-Type': 'text/html; charset=utf-8'
        }
    });
} 