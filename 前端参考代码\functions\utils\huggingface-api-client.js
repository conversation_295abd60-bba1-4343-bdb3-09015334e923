// Hugging Face API 客户端 - 处理与外部API的所有通信
export class HuggingFaceApiClient {
    constructor() {
        this.baseUrl = 'https://fromozu-audiobooks-backend.hf.space';
        this.timeout = 30000; // 30秒超时
    }

    /**
     * 提交文件转换任务到 Hugging Face
     */
    async submitConversionTask(fileUrl, options = {}) {
        try {
            const payload = {
                file_url: fileUrl,
                voice_settings: {
                    language: options.language || "zh-CN",
                    voice_name: options.voiceName || "zh-CN-XiaochenMultilingualNeural",
                    speed: options.speed || 1.0,
                    pitch: options.pitch || 0,
                    volume: options.volume || 1.0
                },
                output_formats: options.outputFormats || ["mp3"],
                audio_quality: options.audioQuality || "high",
                split_by_chapter: options.splitByChapter !== false, // 默认启用章节分割
                max_segment_length: options.maxSegmentLength || 300
            };

            console.log('向HF提交转换任务:', {
                url: `${this.baseUrl}/api/convert`,
                payload: {
                    ...payload,
                    file_url: '[URL隐藏]' // 日志中隐藏真实URL
                }
            });

            const response = await this.fetchWithTimeout(`${this.baseUrl}/api/convert`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(`HF API错误 (${response.status}): ${errorData.error?.message || response.statusText}`);
            }

            const result = await response.json();
            console.log('HF任务提交成功:', {
                taskId: result.task_id,
                status: result.status,
                estimatedDuration: result.estimated_duration
            });

            return {
                hfTaskId: result.task_id,
                status: result.status,
                message: result.message,
                estimatedDuration: result.estimated_duration,
                queuePosition: result.queue_position
            };

        } catch (error) {
            console.error('提交HF转换任务失败:', error);
            throw new Error(`提交转换任务失败: ${error.message}`);
        }
    }

    /**
     * 获取任务状态
     */
    async getTaskStatus(hfTaskId) {
        try {
            const response = await this.fetchWithTimeout(`${this.baseUrl}/api/queue/status/${hfTaskId}`);
            
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(`获取状态失败 (${response.status}): ${errorData.error?.message || response.statusText}`);
            }

            const data = await response.json();
            
            return {
                taskId: data.task_id,
                status: data.status, // queued, processing, completed, failed, cancelled
                progress: data.progress || 0,
                currentStage: data.current_stage,
                createdAt: data.created_at,
                updatedAt: data.updated_at,
                metadata: data.metadata,
                processingDetails: data.processing_details
            };

        } catch (error) {
            console.error(`获取HF任务状态失败 (${hfTaskId}):`, error);
            throw new Error(`获取任务状态失败: ${error.message}`);
        }
    }

    /**
     * 获取转换结果
     */
    async getTaskResult(hfTaskId) {
        try {
            const response = await this.fetchWithTimeout(`${this.baseUrl}/api/queue/result/${hfTaskId}`);
            
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(`获取结果失败 (${response.status}): ${errorData.error?.message || response.statusText}`);
            }

            const data = await response.json();
            
            return {
                taskId: data.task_id,
                status: data.status,
                audioFiles: data.audio_files,
                metadata: data.metadata
            };

        } catch (error) {
            console.error(`获取HF任务结果失败 (${hfTaskId}):`, error);
            throw new Error(`获取任务结果失败: ${error.message}`);
        }
    }

    /**
     * 取消任务
     */
    async cancelTask(hfTaskId) {
        try {
            const response = await this.fetchWithTimeout(`${this.baseUrl}/api/queue/cancel/${hfTaskId}`, {
                method: 'DELETE'
            });
            
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(`取消任务失败 (${response.status}): ${errorData.error?.message || response.statusText}`);
            }

            const data = await response.json();
            console.log(`HF任务已取消: ${hfTaskId}`);
            
            return {
                message: data.message,
                taskId: data.task_id
            };

        } catch (error) {
            console.error(`取消HF任务失败 (${hfTaskId}):`, error);
            throw new Error(`取消任务失败: ${error.message}`);
        }
    }

    /**
     * 轮询任务直到完成
     */
    async pollTaskUntilComplete(hfTaskId, options = {}) {
        const pollInterval = options.pollInterval || 5000; // 5秒轮询一次
        const maxPolls = options.maxPolls || 720; // 最多轮询1小时
        const onProgress = options.onProgress; // 进度回调

        console.log(`开始轮询HF任务: ${hfTaskId}, 间隔: ${pollInterval}ms`);

        for (let i = 0; i < maxPolls; i++) {
            try {
                const status = await this.getTaskStatus(hfTaskId);
                
                // 调用进度回调
                if (onProgress && typeof onProgress === 'function') {
                    await onProgress(status);
                }

                console.log(`轮询 ${i + 1}/${maxPolls}: ${status.status} (${status.progress}%) - ${status.currentStage}`);

                // 任务完成
                if (status.status === 'completed') {
                    console.log(`HF任务完成: ${hfTaskId}`);
                    return await this.getTaskResult(hfTaskId);
                }

                // 任务失败
                if (status.status === 'failed' || status.status === 'cancelled') {
                    throw new Error(`任务${status.status}: ${status.currentStage || '未知错误'}`);
                }

                // 等待下次轮询
                await this.sleep(pollInterval);

            } catch (error) {
                console.error(`轮询第${i + 1}次失败:`, error);
                
                // 如果是网络错误，继续重试
                if (error.message.includes('fetch') || error.message.includes('网络')) {
                    console.log('网络错误，继续重试...');
                    await this.sleep(pollInterval);
                    continue;
                }
                
                // 其他错误直接抛出
                throw error;
            }
        }

        throw new Error(`任务轮询超时: ${hfTaskId} (超过 ${maxPolls * pollInterval / 1000} 秒)`);
    }

    /**
     * 带超时的 fetch
     */
    async fetchWithTimeout(url, options = {}) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.timeout);

        try {
            const response = await fetch(url, {
                ...options,
                signal: controller.signal
            });
            clearTimeout(timeoutId);
            return response;
        } catch (error) {
            clearTimeout(timeoutId);
            if (error.name === 'AbortError') {
                throw new Error(`请求超时 (${this.timeout}ms): ${url}`);
            }
            throw error;
        }
    }

    /**
     * 睡眠函数
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 检查 Hugging Face 服务状态
     */
    async checkServiceHealth() {
        try {
            const response = await this.fetchWithTimeout(`${this.baseUrl}/health`);
            
            if (!response.ok) {
                throw new Error(`健康检查失败: ${response.status}`);
            }

            const data = await response.json();
            return {
                status: data.status,
                timestamp: data.timestamp,
                version: data.version
            };

        } catch (error) {
            console.error('HF服务健康检查失败:', error);
            throw new Error(`服务不可用: ${error.message}`);
        }
    }

    /**
     * 获取支持的语音列表
     */
    async getAvailableVoices() {
        try {
            const response = await this.fetchWithTimeout(`${this.baseUrl}/api/voices/list`);
            
            if (!response.ok) {
                throw new Error(`获取语音列表失败: ${response.status}`);
            }

            const data = await response.json();
            return data.voices;

        } catch (error) {
            console.error('获取语音列表失败:', error);
            // 返回默认语音列表作为fallback
            return [
                {
                    name: "zh-CN-XiaochenMultilingualNeural",
                    language: "zh-CN",
                    gender: "female",
                    description: "中文女声"
                }
            ];
        }
    }
} 