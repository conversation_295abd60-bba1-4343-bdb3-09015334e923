// 测试用户ID解析器的API端点
import { UserIdResolver } from '../utils/user-id-resolver.js';

export async function onRequestPost({ request, env }) {
    try {
        const { email, taskId, action } = await request.json();
        
        if (!email) {
            return Response.json({ error: '邮箱参数不能为空' }, { status: 400 });
        }

        const resolver = new UserIdResolver(env);
        const result = {
            email: email,
            taskId: taskId,
            action: action,
            timestamp: new Date().toISOString(),
            resolver: null,
            tests: {}
        };

        switch (action) {
            case 'resolve':
                // 解析用户ID
                result.resolver = await resolver.resolveUserId(email, taskId);
                break;

            case 'test_hash':
                // 测试邮箱哈希
                result.tests.hashTest = resolver.testEmailHash(email);
                break;

            case 'find_75aifw':
                // 尝试找出产生75aifw哈希的邮箱
                const possibleEmails = [
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    // 可以添加更多可能的邮箱
                ];
                
                result.tests.hashSearch = {
                    targetHash: '75aifw',
                    possibleEmails: possibleEmails,
                    matchingEmails: resolver.findEmailForHash('75aifw', possibleEmails),
                    hashResults: possibleEmails.map(email => resolver.testEmailHash(email))
                };
                break;

            case 'comprehensive':
                // 综合测试
                result.resolver = await resolver.resolveUserId(email, taskId);
                result.tests.hashTest = resolver.testEmailHash(email);
                
                // 如果有AUDIO_BUCKET，测试R2验证
                if (taskId && env.AUDIO_BUCKET && result.resolver.userId) {
                    result.tests.r2Verification = await resolver.verifyUserIdInR2(result.resolver.userId, taskId);
                }
                
                // 获取统计信息
                result.tests.stats = resolver.getStats();
                break;

            case 'reverse_engineer':
                // 反向工程：尝试找出所有可能的用户ID映射
                const knownHashes = ['75aifw', '3235976']; // 已知的哈希值
                const commonEmailPatterns = [
                    // QQ邮箱模式
                    ...knownHashes.map(hash => `${hash}@qq.com`),
                    // Gmail模式
                    ...knownHashes.map(hash => `${hash}@gmail.com`),
                    // 163邮箱模式
                    ...knownHashes.map(hash => `${hash}@163.com`),
                    // 其他常见模式
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>'
                ];

                result.tests.reverseEngineering = {
                    knownHashes: knownHashes,
                    testedEmails: commonEmailPatterns,
                    hashMappings: {},
                    exactMatches: []
                };

                for (const testEmail of commonEmailPatterns) {
                    const hashResult = resolver.testEmailHash(testEmail);
                    if (hashResult.success) {
                        result.tests.reverseEngineering.hashMappings[testEmail] = hashResult.hash;
                        
                        if (knownHashes.includes(hashResult.hash)) {
                            result.tests.reverseEngineering.exactMatches.push({
                                email: testEmail,
                                hash: hashResult.hash
                            });
                        }
                    }
                }
                break;

            default:
                // 默认行为：解析用户ID
                result.resolver = await resolver.resolveUserId(email, taskId);
                result.tests.hashTest = resolver.testEmailHash(email);
                break;
        }

        return Response.json({
            success: true,
            result: result
        });

    } catch (error) {
        console.error('用户ID解析器测试失败:', error);
        return Response.json({ 
            error: '测试失败: ' + error.message,
            stack: error.stack
        }, { status: 500 });
    }
}

export async function onRequestGet({ request, env }) {
    try {
        const url = new URL(request.url);
        const email = url.searchParams.get('email');
        const taskId = url.searchParams.get('taskId');
        const action = url.searchParams.get('action') || 'resolve';
        
        if (!email) {
            return Response.json({ error: '缺少email参数' }, { status: 400 });
        }

        // 转发到POST处理
        return await exports.onRequestPost({
            request: {
                json: async () => ({ email, taskId, action })
            },
            env
        });

    } catch (error) {
        console.error('GET请求处理失败:', error);
        return Response.json({ 
            error: 'GET请求失败: ' + error.message 
        }, { status: 500 });
    }
} 