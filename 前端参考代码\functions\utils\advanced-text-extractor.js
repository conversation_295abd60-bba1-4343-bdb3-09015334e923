// 高级文档文本提取器 - 使用专业库的完整实现
// 这个文件展示了如何集成专业库来处理PDF和Word文档
// 使用前需要安装相关依赖：
// npm install pdf-parse mammoth office-text-extractor

// 注意：这些库可能需要额外的配置才能在Cloudflare Workers环境中运行
// 建议在本地Node.js环境或传统服务器环境中使用

/**
 * 高级文档文本提取器类
 * 使用专业库提供更好的文档处理能力
 */
export class AdvancedTextExtractor {
    constructor() {
        this.supportedFormats = ['txt', 'epub', 'pdf', 'doc', 'docx'];
    }

    /**
     * 从文件内容提取纯文本（使用专业库）
     * @param {string|ArrayBuffer} content - 文件内容
     * @param {string} filename - 文件名
     * @returns {Promise<string>} - 提取的纯文本
     */
    async extractTextFromContent(content, filename) {
        const ext = filename.toLowerCase().split('.').pop();
        
        switch (ext) {
            case 'txt':
                return this.extractTextFromTxt(content);
            case 'epub':
                return this.extractTextFromEpub(content);
            case 'pdf':
                return await this.extractTextFromPdfAdvanced(content);
            case 'doc':
            case 'docx':
                return await this.extractTextFromWordAdvanced(content);
            default:
                throw new Error(`不支持的文件格式: ${ext}`);
        }
    }

    /**
     * 提取TXT文件文本
     */
    extractTextFromTxt(content) {
        if (typeof content === 'string') {
            return this.cleanText(content);
        } else {
            const decoder = new TextDecoder('utf-8');
            const textContent = decoder.decode(content);
            return this.cleanText(textContent);
        }
    }

    /**
     * 提取EPUB文件文本
     */
    extractTextFromEpub(content) {
        // 这里可以集成更强大的EPUB处理库，如 epub2 或 epub-parser
        let epubContent;
        if (typeof content === 'string') {
            epubContent = content;
        } else {
            const decoder = new TextDecoder('utf-8');
            epubContent = decoder.decode(content);
        }
        
        // 移除HTML标签的简单方法
        let text = epubContent.replace(/<[^>]*>/g, ' ');
        // 解码HTML实体
        text = text.replace(/&nbsp;/g, ' ')
                  .replace(/&lt;/g, '<')
                  .replace(/&gt;/g, '>')
                  .replace(/&amp;/g, '&')
                  .replace(/&quot;/g, '"');
        
        return this.cleanText(text);
    }

    /**
     * 使用pdf-parse库提取PDF文本
     * @param {ArrayBuffer} content - PDF文件内容
     * @returns {Promise<string>} - 提取的文本
     */
    async extractTextFromPdfAdvanced(content) {
        try {
            // 使用pdf-parse库（需要先安装：npm install pdf-parse）
            const pdfParse = require('pdf-parse');
            
            // 将ArrayBuffer转换为Buffer（Node.js环境）
            const buffer = Buffer.from(content);
            
            // 解析PDF
            const data = await pdfParse(buffer);
            
            // pdf-parse返回的data.text包含所有文本内容
            const extractedText = data.text;
            
            if (!extractedText || extractedText.trim().length < 10) {
                throw new Error('PDF文件中没有找到可提取的文本内容');
            }
            
            console.log(`PDF解析成功: 共${data.numpages}页，提取文本长度${extractedText.length}字符`);
            
            return this.cleanText(extractedText);
            
        } catch (error) {
            console.error('PDF文本提取失败:', error);
            throw new Error(`PDF文档处理失败: ${error.message}`);
        }
    }

    /**
     * 使用mammoth.js提取Word文档文本
     * @param {ArrayBuffer} content - Word文档内容
     * @returns {Promise<string>} - 提取的文本
     */
    async extractTextFromWordAdvanced(content) {
        try {
            // 使用mammoth.js库（需要先安装：npm install mammoth）
            const mammoth = require('mammoth');
            
            // mammoth可以处理ArrayBuffer
            const result = await mammoth.extractRawText({ arrayBuffer: content });
            
            const extractedText = result.value;
            
            if (!extractedText || extractedText.trim().length < 10) {
                throw new Error('Word文档中没有找到可提取的文本内容');
            }
            
            // 输出转换过程中的消息（警告等）
            if (result.messages && result.messages.length > 0) {
                console.log('Word文档转换消息:', result.messages);
            }
            
            console.log(`Word文档解析成功，提取文本长度${extractedText.length}字符`);
            
            return this.cleanText(extractedText);
            
        } catch (error) {
            console.error('Word文档处理失败:', error);
            throw new Error(`Word文档处理失败: ${error.message}`);
        }
    }

    /**
     * 使用office-text-extractor库（万能处理器）
     * @param {ArrayBuffer} content - 文档内容
     * @param {string} filename - 文件名
     * @returns {Promise<string>} - 提取的文本
     */
    async extractTextWithOfficeExtractor(content, filename) {
        try {
            // 使用office-text-extractor库（需要先安装：npm install office-text-extractor）
            const { getTextExtractor } = require('office-text-extractor');
            
            const extractor = getTextExtractor();
            
            // 将ArrayBuffer转换为Buffer
            const buffer = Buffer.from(content);
            
            // 提取文本
            const text = await extractor.extractText({ 
                input: buffer, 
                type: 'buffer' 
            });
            
            if (!text || text.trim().length < 10) {
                throw new Error('文档中没有找到可提取的文本内容');
            }
            
            console.log(`文档解析成功，提取文本长度${text.length}字符`);
            
            return this.cleanText(text);
            
        } catch (error) {
            console.error('office-text-extractor处理失败:', error);
            throw new Error(`文档处理失败: ${error.message}`);
        }
    }

    /**
     * 清理文本内容
     * @param {string} text - 原始文本
     * @returns {string} - 清理后的文本
     */
    cleanText(text) {
        return text
            .replace(/\r\n/g, '\n')
            .replace(/\r/g, '\n')
            .replace(/\n{3,}/g, '\n\n')
            .replace(/[ \t]+/g, ' ')
            .trim();
    }
}

/**
 * 使用示例和集成指南
 */
export class TextExtractorIntegrationGuide {
    /**
     * 如何在现有TTS处理器中集成高级文本提取器
     */
    static getIntegrationExample() {
        return `
// 在 tts-processor.js 中集成高级文本提取器的示例：

import { AdvancedTextExtractor } from './advanced-text-extractor.js';

export class TTSProcessor {
    constructor() {
        this.apiKey = process.env.TTS_API_KEY;
        this.apiUrl = process.env.TTS_API_URL || 'https://api.openai.com/v1/audio/speech';
        
        // 初始化高级文本提取器
        this.advancedExtractor = new AdvancedTextExtractor();
    }

    /**
     * 增强版的文本提取方法
     */
    async extractTextFromContent(content, filename) {
        const ext = filename.toLowerCase().split('.').pop();
        
        // 对于PDF和Word文档，优先使用高级提取器
        if (['pdf', 'doc', 'docx'].includes(ext)) {
            try {
                return await this.advancedExtractor.extractTextFromContent(content, filename);
            } catch (error) {
                console.warn('高级提取器失败，回退到基础实现:', error.message);
                // 回退到基础实现
                return this.basicExtractTextFromContent(content, filename);
            }
        }
        
        // 其他格式使用原有方法
        return this.basicExtractTextFromContent(content, filename);
    }

    // 原有的基础实现方法重命名
    basicExtractTextFromContent(content, filename) {
        // ... 原有的基础实现代码
    }
}
        `;
    }

    /**
     * 获取专业库的安装和配置指南
     */
    static getInstallationGuide() {
        return {
            pdf: {
                library: 'pdf-parse',
                install: 'npm install pdf-parse',
                description: '强大的PDF文本提取库，支持复杂的PDF文档',
                pros: ['高准确率', '支持复杂格式', '提取元数据'],
                cons: ['体积较大', '依赖native模块']
            },
            word: {
                library: 'mammoth.js',
                install: 'npm install mammoth',
                description: 'Microsoft官方推荐的Word文档处理库',
                pros: ['官方支持', '格式保持好', '支持样式映射'],
                cons: ['仅支持DOCX', '学习曲线']
            },
            universal: {
                library: 'office-text-extractor',
                install: 'npm install office-text-extractor',
                description: '通用文档处理库，支持多种格式',
                pros: ['统一接口', '支持多格式', 'TypeScript支持'],
                cons: ['功能相对简单', '自定义选项少']
            }
        };
    }
}

// 使用示例
/*
const extractor = new AdvancedTextExtractor();

// 提取PDF文档
const pdfBuffer = fs.readFileSync('document.pdf');
const pdfText = await extractor.extractTextFromContent(pdfBuffer, 'document.pdf');

// 提取Word文档
const docxBuffer = fs.readFileSync('document.docx');
const docxText = await extractor.extractTextFromContent(docxBuffer, 'document.docx');

console.log('提取的文本:', pdfText);
*/ 