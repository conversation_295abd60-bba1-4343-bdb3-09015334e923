"""
应用配置管理 - 单体架构版本
移除云服务依赖，使用本地存储和数据库
"""

from pydantic_settings import BaseSettings
from pydantic import Field
from typing import Optional
import os
import secrets
from pathlib import Path


class Settings(BaseSettings):
    """应用配置类"""

    # 应用基础配置
    app_name: str = Field(default="电子书转音频服务", alias="APP_NAME")
    app_version: str = Field(default="1.0.0", alias="APP_VERSION")
    debug: bool = Field(default=False, alias="DEBUG")

    # 服务器配置
    host: str = Field(default="0.0.0.0", alias="APP_HOST")
    port: int = Field(default=8000, alias="APP_PORT")

    # 安全配置
    secret_key: str = Field(default_factory=lambda: secrets.token_urlsafe(32), alias="SECRET_KEY")
    jwt_algorithm: str = Field(default="HS256", alias="JWT_ALGORITHM")
    jwt_expire_minutes: int = Field(default=60 * 24 * 7, alias="JWT_EXPIRE_MINUTES")  # 7天

    # 数据库配置
    database_url: str = Field(default="sqlite:///data/database/app.db", alias="DATABASE_URL")
    database_echo: bool = Field(default=False, alias="DATABASE_ECHO")

    # 本地存储配置
    data_dir: str = Field(default="data", alias="DATA_DIR")
    upload_dir: str = Field(default="data/uploads", alias="UPLOAD_DIR")
    audio_dir: str = Field(default="data/audio", alias="AUDIO_DIR")
    temp_dir: str = Field(default="data/temp", alias="TEMP_DIR")

    # 文件处理配置
    max_file_size: int = Field(default=50 * 1024 * 1024, alias="MAX_FILE_SIZE")  # 50MB
    allowed_file_types: list = Field(default=[".epub", ".txt", ".pdf", ".doc", ".docx"])
    max_concurrent_tasks: int = Field(default=3, alias="MAX_CONCURRENT_TASKS")
    queue_max_size: int = Field(default=100, alias="QUEUE_MAX_SIZE")  # 队列最大大小
    task_timeout: int = Field(default=3600, alias="TASK_TIMEOUT")  # 1小时

    # TTS API配置（保留外部API支持）
    tts_api_url: str = Field(default="https://otts.api.zwei.de.eu.org/v1/audio/speech", alias="TTS_API_URL")
    tts_api_key: str = Field(default="sk-Zwei", alias="TTS_API_KEY")
    tts_model: str = Field(default="zh-CN-XiaochenMultilingualNeural", alias="TTS_MODEL")
    tts_speed: int = Field(default=0, alias="TTS_SPEED")
    tts_pitch: int = Field(default=0, alias="TTS_PITCH")
    tts_output_format: str = Field(default="audio-24khz-160kbitrate-mono-mp3", alias="TTS_OUTPUT_FORMAT")

    # 音频配置
    audio_quality: str = Field(default="high", alias="AUDIO_QUALITY")
    max_segment_length: int = Field(default=1000, alias="MAX_SEGMENT_LENGTH")  # 字符数
    supported_output_formats: list = Field(default=["mp3", "wav"])

    # 用户配置
    default_user_points: int = Field(default=100, alias="DEFAULT_USER_POINTS")
    points_per_conversion: int = Field(default=50, alias="POINTS_PER_CONVERSION")
    max_users: int = Field(default=1000, alias="MAX_USERS")  # 限制用户数量

    # 日志配置
    log_level: str = Field(default="INFO", alias="LOG_LEVEL")
    log_format: str = Field(default="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    log_file: str = Field(default="data/logs/app.log", alias="LOG_FILE")

    # 缓存配置
    enable_cache: bool = Field(default=True, alias="ENABLE_CACHE")
    cache_ttl: int = Field(default=3600, alias="CACHE_TTL")  # 1小时

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        extra = "ignore"  # 忽略额外的环境变量


# 创建全局配置实例
settings = Settings()


def ensure_directories():
    """确保必要的目录存在"""
    directories = [
        settings.data_dir,
        settings.upload_dir,
        settings.audio_dir,
        settings.temp_dir,
        os.path.dirname(settings.log_file),
        os.path.dirname(settings.database_url.replace("sqlite:///", "")),
    ]
    
    for directory in directories:
        if directory:
            os.makedirs(directory, exist_ok=True)
            # 设置目录权限
            try:
                os.chmod(directory, 0o755)
            except (OSError, PermissionError):
                pass  # 忽略权限设置错误


def get_database_path() -> str:
    """获取数据库文件路径"""
    if settings.database_url.startswith("sqlite:///"):
        return settings.database_url.replace("sqlite:///", "")
    return "data/database/app.db"


def get_upload_path(user_id: str, filename: str) -> str:
    """获取用户上传文件路径"""
    user_dir = os.path.join(settings.upload_dir, user_id)
    os.makedirs(user_dir, exist_ok=True)
    return os.path.join(user_dir, filename)


def get_audio_path(user_id: str, task_id: str, filename: str) -> str:
    """获取音频文件路径"""
    audio_dir = os.path.join(settings.audio_dir, user_id, task_id)
    os.makedirs(audio_dir, exist_ok=True)
    return os.path.join(audio_dir, filename)


def get_temp_path(filename: str) -> str:
    """获取临时文件路径"""
    return os.path.join(settings.temp_dir, filename)


# 初始化目录
ensure_directories()
