// 管理员API - 修复增强版任务
export async function onRequestPost({ request, env }) {
    try {
        const { action, email } = await request.json();
        
        if (!action || !email) {
            return Response.json({ error: '缺少必要参数' }, { status: 400 });
        }

        let result;
        
        switch (action) {
            case 'check':
                result = await checkEnhancedTasks(email, env);
                break;
            case 'fix':
                result = await fixEnhancedTasks(email, env);
                break;
            case 'fix-r2-paths':
                result = await fixR2Paths(email, env);
                break;
            default:
                return Response.json({ error: '无效的操作类型' }, { status: 400 });
        }

        return Response.json({
            success: true,
            result: result
        });

    } catch (error) {
        console.error('管理员API错误:', error);
        return Response.json({ 
            error: '服务器内部错误',
            message: error.message 
        }, { status: 500 });
    }
}

// 检查增强版任务
async function checkEnhancedTasks(email, env) {
    const tasks = await getUserTasks(email, env);
    const enhancedTasks = tasks.filter(task => 
        task.isEnhanced || 
        task.audioType === 'MULTI_CHAPTER' || 
        task.processingType === 'MULTI_CHAPTER' ||
        task.totalChapters > 1
    );

    const result = {
        totalTasks: tasks.length,
        enhancedTasks: enhancedTasks.length,
        tasks: enhancedTasks.map(task => ({
            id: task.id,
            filename: task.filename,
            status: task.status,
            totalChapters: task.totalChapters,
            hasAudioUrl: !!task.audioUrl,
            currentAudioUrl: task.audioUrl,
            needsFix: !task.audioUrl && task.status === 'SUCCESS',
            suggestedAudioUrl: !task.audioUrl ? `/api/audio/playlist/${task.id}` : null
        }))
    };

    return result;
}

// 修复增强版任务
async function fixEnhancedTasks(email, env) {
    const tasks = await getUserTasks(email, env);
    const enhancedTasks = tasks.filter(task => 
        task.isEnhanced || 
        task.audioType === 'MULTI_CHAPTER' || 
        task.processingType === 'MULTI_CHAPTER' ||
        task.totalChapters > 1
    );

    let fixedTasks = 0;
    const fixedTasksList = [];

    for (const task of enhancedTasks) {
        if (!task.audioUrl && task.status === 'SUCCESS') {
            // 修复audioUrl
            task.audioUrl = `/api/audio/playlist/${task.id}`;
            task.playlistUrl = `/api/audio/playlist/${task.id}`;
            task.metadataUrl = `/api/audio/metadata/${task.id}`;
            
            // 保存修复后的任务
            const taskKey = `task:${task.id}`;
            await env.KV.put(taskKey, JSON.stringify(task));
            
            fixedTasks++;
            fixedTasksList.push({
                id: task.id,
                filename: task.filename,
                newAudioUrl: task.audioUrl,
                fixed: true
            });
        }
    }

    return {
        totalTasks: tasks.length,
        enhancedTasks: enhancedTasks.length,
        fixedTasks: fixedTasks,
        tasks: fixedTasksList
    };
}

// 修复R2存储路径中的undefined问题
async function fixR2Paths(email, env) {
    console.log(`开始修复用户 ${email} 的R2存储路径问题`);
    
    const tasks = await getUserTasks(email, env);
    const enhancedTasks = tasks.filter(task => 
        task.isEnhanced || 
        task.audioType === 'MULTI_CHAPTER' || 
        task.processingType === 'MULTI_CHAPTER' ||
        task.totalChapters > 1
    );

    const result = {
        totalTasks: tasks.length,
        enhancedTasks: enhancedTasks.length,
        checkedTasks: 0,
        problematicPaths: [],
        fixedPaths: 0,
        errors: []
    };

    // 生成邮箱哈希
    const emailHash = hashEmail(email);
    
    for (const task of enhancedTasks) {
        result.checkedTasks++;
        
        try {
            const taskId = task.id;
            const correctBasePath = `users/${emailHash}/audiobooks/${taskId}`;
            const problematicBasePath = `users/${emailHash}/audiobooks/${taskId}/undefined`;
            
            console.log(`检查任务 ${taskId} 的R2路径...`);
            
            // 检查是否存在problematic路径
            const listResult = await env.R2.list({ 
                prefix: problematicBasePath,
                limit: 100
            });
            
            if (listResult.objects && listResult.objects.length > 0) {
                console.log(`发现问题路径: ${problematicBasePath}, 文件数: ${listResult.objects.length}`);
                
                result.problematicPaths.push({
                    taskId: taskId,
                    filename: task.filename,
                    problematicPath: problematicBasePath,
                    correctPath: correctBasePath,
                    fileCount: listResult.objects.length,
                    files: listResult.objects.map(obj => obj.key)
                });
                
                // 移动文件到正确路径
                for (const obj of listResult.objects) {
                    const oldKey = obj.key;
                    const newKey = oldKey.replace(`${problematicBasePath}/`, `${correctBasePath}/`);
                    
                    try {
                        // 复制文件到新位置
                        const fileObject = await env.R2.get(oldKey);
                        if (fileObject) {
                            await env.R2.put(newKey, fileObject.body, {
                                httpMetadata: fileObject.httpMetadata,
                                customMetadata: fileObject.customMetadata
                            });
                            
                            // 删除旧文件
                            await env.R2.delete(oldKey);
                            
                            result.fixedPaths++;
                            console.log(`已移动文件: ${oldKey} -> ${newKey}`);
                        }
                    } catch (moveError) {
                        console.error(`移动文件失败 ${oldKey}:`, moveError);
                        result.errors.push({
                            taskId: taskId,
                            file: oldKey,
                            error: moveError.message
                        });
                    }
                }
            }
            
        } catch (error) {
            console.error(`检查任务 ${task.id} 时出错:`, error);
            result.errors.push({
                taskId: task.id,
                error: error.message
            });
        }
    }
    
    console.log(`R2路径修复完成，共修复 ${result.fixedPaths} 个文件`);
    return result;
}

// 获取用户的所有任务
async function getUserTasks(email, env) {
    const taskList = await env.KV.list({ prefix: 'task:' });
    const tasks = [];
    
    for (const key of taskList.keys) {
        try {
            const task = await env.KV.get(key.name, { type: 'json' });
            if (task && task.email === email) {
                tasks.push(task);
            }
        } catch (error) {
            console.error(`获取任务失败 ${key.name}:`, error);
        }
    }
    
    return tasks;
}

// 生成邮箱哈希（与用户ID解析器中的方法保持一致）
function hashEmail(email) {
    if (!email || typeof email !== 'string' || email.trim() === '') {
        throw new Error('邮箱地址不能为空或无效');
    }
    
    const cleanEmail = email.trim().toLowerCase();
    
    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(cleanEmail)) {
        throw new Error(`邮箱格式无效: ${email}`);
    }
    
    let hash = 0;
    for (let i = 0; i < cleanEmail.length; i++) {
        const char = cleanEmail.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // 转换为32位整数
    }
    
    const hashResult = Math.abs(hash).toString(36);
    console.log(`邮箱哈希生成: ${email} -> ${hashResult}`);
    return hashResult;
} 