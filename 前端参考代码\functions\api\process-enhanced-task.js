// 增强版任务处理 API - 支持多章节音频生成
import { EnhancedTTSProcessor } from '../utils/enhanced-tts-processor.js';
import { EnhancedR2StorageManager } from '../utils/enhanced-r2-storage.js';
import { TaskLogger } from '../utils/task-logger.js';
import { AudiobookApiClient } from '../utils/audiobook-api-client.js';

export async function onRequestPost({ request, env }) {
    try {
        const { taskId, enableChapterSplit = true } = await request.json();
        
        if (!taskId) {
            return Response.json({ error: '任务ID不能为空' }, { status: 400 });
        }

        // 处理增强版任务
        await processEnhancedTask(taskId, enableChapterSplit, env);

        return Response.json({
            success: true,
            message: '增强版任务处理完成'
        });

    } catch (error) {
        console.error('处理增强版任务失败:', error);
        return Response.json({ error: error.message }, { status: 500 });
    }
}

// 主要的增强版任务处理函数
async function processEnhancedTask(taskId, enableChapterSplit, env) {
    const taskKey = `task:${taskId}`;
    const logger = new TaskLogger(taskId, env);
    const startTime = Date.now();
    
    // 参数验证
    if (!taskId || typeof taskId !== 'string' || taskId.trim() === '') {
        throw new Error('任务ID不能为空或无效');
    }
    
    console.log(`开始处理增强版任务: ${taskId}, 章节分割: ${enableChapterSplit}`);

    // 获取任务数据
    const task = await env.KV.get(taskKey, { type: 'json' });
    if (!task) {
        throw new Error(`任务不存在: ${taskId}`);
    }
    
    // 验证任务数据完整性
    if (!task.email || typeof task.email !== 'string' || task.email.trim() === '') {
        throw new Error(`任务 ${taskId} 的邮箱信息无效: ${task.email}`);
    }
    
    if (!task.filename || typeof task.filename !== 'string' || task.filename.trim() === '') {
        throw new Error(`任务 ${taskId} 的文件名信息无效: ${task.filename}`);
    }
    
    console.log(`任务验证通过: 邮箱=${task.email}, 文件名=${task.filename}`);

    try {
        await logger.info('增强版任务处理开始', { 
            taskId, 
            enableChapterSplit,
            processingType: enableChapterSplit ? 'MULTI_CHAPTER' : 'SINGLE_AUDIO'
        });
        
        // 获取任务信息
        await logger.stepStart('获取任务信息');
        const task = await env.KV.get(taskKey, { type: 'json' });
        if (!task) {
            throw new Error('任务不存在');
        }
        await logger.stepComplete('获取任务信息', null, { 
            filename: task.filename, 
            email: task.email,
            createdAt: task.createdAt,
            fileType: task.filename.toLowerCase().split('.').pop()
        });

        // 更新状态为处理中
        await logger.stepStart('更新任务状态');
        task.status = 'PROCESSING';
        task.progress = 5;
        task.processingType = enableChapterSplit ? 'MULTI_CHAPTER' : 'SINGLE_AUDIO';
        task.startedAt = new Date().toISOString();
        await env.KV.put(taskKey, JSON.stringify(task));
        await logger.progress(5, '增强版任务状态已更新为处理中');
        await logger.stepComplete('更新任务状态');

        // 初始化增强版存储管理器
        await logger.stepStart('初始化增强版存储管理器');
        const r2Storage = new EnhancedR2StorageManager(env.R2);
        await logger.stepComplete('初始化增强版存储管理器');

        // 从R2获取文件内容
        await logger.stepStart('读取文件内容');
        let fileContent;
        if (task.filePath) {
            console.log('从R2读取文件:', task.filePath);
            const fileObject = await r2Storage.getFile(task.filePath);
            if (!fileObject) {
                throw new Error('文件内容不存在');
            }
            
            const ext = task.filename.toLowerCase().split('.').pop();
            console.log('文件类型:', ext);
            
            if (ext === 'txt') {
                fileContent = await fileObject.text();
                console.log('文本文件读取完成，长度:', fileContent.length);
            } else {
                fileContent = await fileObject.arrayBuffer();
                console.log('二进制文件读取完成，大小:', fileContent.byteLength);
            }
        } else {
            console.log('从KV存储读取文件（兼容模式）');
            // 兼容旧的KV存储方式
            const fileKey = `file:${taskId}`;
            fileContent = await env.KV.get(fileKey);
            if (!fileContent) {
                throw new Error('文件内容不存在');
            }
        }
        await logger.stepComplete('读取文件内容', Date.now() - startTime);

        // 初始化增强版TTS处理器
        await logger.stepStart('初始化增强版TTS处理器');
        const ttsProcessor = new EnhancedTTSProcessor();
        await logger.stepComplete('初始化增强版TTS处理器');
        
        // 更新进度
        task.progress = 10;
        await env.KV.put(taskKey, JSON.stringify(task));
        await logger.progress(10, '文件读取完成，开始内容分析');

        // 创建进度更新回调
        const progressCallback = async (progress, stage) => {
            const currentTask = await env.KV.get(taskKey, { type: 'json' });
            if (currentTask) {
                currentTask.progress = Math.max(progress, 10); // 确保进度不会倒退
                currentTask.currentStage = stage || '音频转换中';
                currentTask.lastProgressUpdate = new Date().toISOString();
                await env.KV.put(taskKey, JSON.stringify(currentTask));
                await logger.progress(currentTask.progress, `增强版音频转换进度更新: ${currentTask.progress}% - ${currentTask.currentStage}`);
            }
        };

        // 使用外部API进行音频转换
        await logger.stepStart('调用外部音频转换API（增强模式）');
        const apiStartTime = Date.now();
        
        try {
            // 初始化API客户端
            const apiClient = new AudiobookApiClient();
            
            // 使用R2存储路径而不是公开URL
            await logger.stepStart('准备R2文件路径');
            let r2FilePath;
            
            if (task.filePath) {
                // 直接使用已有的R2路径
                r2FilePath = task.filePath;
                
                await logger.stepComplete('准备R2文件路径', null, {
                    r2FilePath: r2FilePath,
                    sourceType: 'existing_file_path',
                    fileType: task.filename.toLowerCase().split('.').pop()
                });
            } else {
                // 如果没有filePath，需要先提取文本并保存为文件
                const text = await ttsProcessor.extractTextFromContent(fileContent, task.filename);
                
                if (!text || text.trim().length === 0) {
                    throw new Error('无法从文件中提取有效文本');
                }
                
                const emailHash = task.email.replace('@', '_').replace('.', '_');
                const textFileName = `users/${emailHash}/tasks/${taskId}/original.txt`;
                const textBuffer = new TextEncoder().encode(text);
                await r2Storage.saveFile(textFileName, textBuffer, 'text/plain');
                r2FilePath = textFileName;
                
                await logger.stepComplete('准备R2文件路径', null, {
                    r2FilePath: r2FilePath,
                    textLength: text.length,
                    sourceType: 'extracted_and_saved'
                });
            }

            // 提交转换任务（使用R2路径）
            await logger.stepStart('提交增强版音频转换任务（R2路径模式）');
            const convertOptions = {
                language: 'zh-CN',
                voiceName: 'zh-CN-XiaochenMultilingualNeural',
                speed: 1.0,
                pitch: 0,
                volume: 1.0
            };
            
            const taskInfo = await apiClient.submitConvertTaskWithR2Path(r2FilePath, convertOptions);
            await logger.stepComplete('提交增强版音频转换任务（R2路径模式）', null, {
                externalTaskId: taskInfo.task_id,
                status: taskInfo.status,
                estimatedDuration: taskInfo.estimated_duration,
                r2FilePath: r2FilePath
            });

            // 保存外部任务ID
            task.externalTaskId = taskInfo.task_id;
            task.progress = 25;
            task.currentStage = '音频转换队列中';
            await env.KV.put(taskKey, JSON.stringify(task));

            // 等待转换完成
            await logger.stepStart('等待增强版音频转换完成');
            const result = await apiClient.waitForTaskCompletion(
                taskInfo.task_id,
                (progress, stage) => {
                    // 将外部进度映射到25-85%范围
                    const mappedProgress = 25 + (progress * 0.6);
                    progressCallback(mappedProgress, stage || '音频转换中');
                },
                1800000, // 30分钟超时
                5000     // 5秒轮询间隔
            );
            
            const apiDuration = Date.now() - apiStartTime;
            await logger.stepComplete('等待增强版音频转换完成', apiDuration, {
                externalTaskId: taskInfo.task_id,
                totalDuration: apiDuration,
                result: {
                    status: result.status,
                    audioFiles: Object.keys(result.audio_files || {}),
                    metadata: result.metadata,
                    totalChapters: result.audio_files?.mp3?.chapters?.length || 0
                }
            });

            // 从外部API结果中获取音频文件信息
            if (!result.audio_files || !result.audio_files.mp3 || !result.audio_files.mp3.chapters || result.audio_files.mp3.chapters.length === 0) {
                throw new Error('外部API未返回有效的音频文件');
            }

            // 获取多章节音频文件的R2存储路径（后端直接返回路径）
            await logger.stepStart('获取多章节音频文件R2存储路径');
            const chapters = result.audio_files.mp3.chapters;
            const audioChapters = [];
            
            for (let i = 0; i < chapters.length; i++) {
                const chapter = chapters[i];
                progressCallback(85 + (i / chapters.length) * 10, `处理第${i + 1}章音频路径`);
                
                // 后端现在直接返回R2路径而不是完整URL
                let audioPath;
                if (chapter.r2_path) {
                    // 后端直接提供R2路径
                    audioPath = chapter.r2_path;
                } else if (chapter.url && chapter.url.includes('/users/')) {
                    // 兼容模式：从URL中提取R2路径
                    audioPath = chapter.url.split('/users/')[1];
                    audioPath = 'users/' + audioPath;
                } else {
                    // 使用默认路径格式（基于用户邮箱和任务ID）
                    const emailHash = task.email.replace('@', '_').replace('.', '_');
                    audioPath = `users/${emailHash}/audios/${taskId}/chapter_${i + 1}.mp3`;
                }
                
                // 解析时长（格式如 "00:05:30"）
                const durationParts = chapter.duration.split(':');
                const durationSeconds = parseInt(durationParts[0]) * 3600 + parseInt(durationParts[1]) * 60 + parseInt(durationParts[2]);
                
                audioChapters.push({
                    title: chapter.title,
                    audioPath: audioPath, // 使用R2路径而不是Buffer
                    duration: durationSeconds,
                    wordCount: Math.round(durationSeconds * 5), // 估算字数
                    order: chapter.order || i + 1,
                    externalUrl: chapter.url, // 保留原始URL用于兼容性
                    r2Path: audioPath // 明确标识R2路径
                });
            }
            
            await logger.stepComplete('获取多章节音频文件R2存储路径', null, {
                totalChapters: audioChapters.length,
                audioChapters: audioChapters.map(ch => ({
                    title: ch.title,
                    audioPath: ch.audioPath,
                    duration: ch.duration
                }))
            });

            // 验证音频文件是否存在于R2存储中
            await logger.stepStart('验证多章节音频文件存在性');
            let allFilesExist = true;
            for (let i = 0; i < audioChapters.length; i++) {
                const chapter = audioChapters[i];
                const exists = await r2Storage.fileExists(chapter.audioPath);
                if (!exists) {
                    console.warn(`第${i + 1}章音频文件在R2存储中不存在: ${chapter.audioPath}`);
                    allFilesExist = false;
                }
            }
            
            if (!allFilesExist) {
                // 等待一段时间让外部API完成文件上传
                console.log('部分音频文件不存在，等待5秒后重试验证...');
                await new Promise(resolve => setTimeout(resolve, 5000));
                
                allFilesExist = true;
                for (let i = 0; i < audioChapters.length; i++) {
                    const chapter = audioChapters[i];
                    const retryExists = await r2Storage.fileExists(chapter.audioPath);
                    if (!retryExists) {
                        console.error(`第${i + 1}章音频文件在R2存储中仍不存在: ${chapter.audioPath}`);
                        allFilesExist = false;
                    }
                }
                
                if (!allFilesExist) {
                    throw new Error('部分音频文件在R2存储中不存在，请检查外部API的文件上传');
                }
            }
            await logger.stepComplete('验证多章节音频文件存在性', null, { 
                totalChapters: audioChapters.length,
                allFilesExist: true 
            });

        } catch (apiError) {
            await logger.stepFailed('调用外部音频转换API（增强模式）', apiError, {
                processingTime: Date.now() - apiStartTime,
                externalTaskId: task.externalTaskId,
                enableChapterSplit
            });
            throw apiError;
        }

        // 保存多章节音频元数据到媒体库（音频文件已由外部API保存到R2）
        await logger.stepStart('保存多章节音频元数据到媒体库');
        
        const bookMetadata = {
            title: task.filename.replace(/\.[^/.]+$/, ''), // 移除文件扩展名
            author: '未知作者',
            totalWordCount: audioChapters.reduce((sum, ch) => sum + ch.wordCount, 0),
            format: ext === 'epub' ? 'EPUB' : '文本',
            language: 'zh-CN',
            source: task.filePath ? 'file' : 'extracted_text',
            externalTaskId: taskInfo.task_id // 保存外部任务ID
        };

        // 使用专门的元数据保存方法（适用于外部API已保存音频文件的情况）
        const savedAudio = await r2Storage.saveMultiChapterAudioMetadata(
            task.email, 
            taskId, 
            audioChapters, 
            bookMetadata
        );

        await logger.stepComplete('保存多章节音频元数据到媒体库', null, {
            totalChapters: audioChapters.length,
            totalSize: savedAudio.totalSize,
            totalDuration: savedAudio.totalDuration,
            averageChapterSize: Math.round(savedAudio.totalSize / audioChapters.length)
        });
        
        // 任务成功完成后扣除积分
        if (!task.pointsCharged) {
            await logger.stepStart('扣除用户积分');
            
            try {
                const totalWordCount = audioChapters.reduce((sum, ch) => sum + ch.wordCount, 0);
                const pointsToCharge = Math.max(50, Math.ceil(totalWordCount / 1000) * 10); // 基础50积分，每1000字符额外10积分
                
                console.log(`开始扣除用户积分: ${task.email}, ${pointsToCharge}积分 (${totalWordCount}字符)`);
                await chargeUserPoints(task.email, pointsToCharge, taskId, env);
                
                task.pointsCharged = true;
                task.pointsCharged_amount = pointsToCharge;
                console.log('积分扣除成功，更新任务状态');
                
                await logger.stepComplete('扣除用户积分', null, { 
                    points: pointsToCharge,
                    wordCount: totalWordCount,
                    formula: `基础50 + ${Math.ceil(totalWordCount / 1000)} * 10`,
                    success: true,
                    userEmail: task.email
                });
            } catch (pointsError) {
                console.error('积分扣除失败，但音频已生成:', pointsError.message);
                
                // 积分扣除失败时的处理
                const totalWordCount = audioChapters.reduce((sum, ch) => sum + ch.wordCount, 0);
                const pointsToCharge = Math.max(50, Math.ceil(totalWordCount / 1000) * 10);
                
                await logger.stepFailed('扣除用户积分', pointsError, {
                    points: pointsToCharge,
                    wordCount: totalWordCount,
                    userEmail: task.email,
                    audioAlreadyGenerated: true,
                    chaptersGenerated: audioChapters.length,
                    errorType: pointsError.message.includes('超时') ? 'TIMEOUT' : 
                               pointsError.message.includes('积分不足') ? 'INSUFFICIENT_POINTS' : 'UNKNOWN'
                });
                
                // 标记积分扣除失败，但不影响音频文件
                task.pointsCharged = false;
                task.pointsError = pointsError.message;
                task.pointsFailedAt = new Date().toISOString();
                task.pointsCharged_amount = pointsToCharge; // 记录应扣除但失败的积分
                
                // 记录警告但继续完成任务
                console.warn(`多章节音频文件已生成，但积分扣除失败。任务将标记为成功，但积分问题需要人工处理。应扣除${pointsToCharge}积分`);
            }
        }

        // 更新任务状态为完成
        const totalDuration = Date.now() - startTime;
        task.status = 'SUCCESS';
        task.progress = 100;
        task.completedAt = new Date().toISOString();
        task.audioType = 'MULTI_CHAPTER';
        task.totalChapters = audioChapters.length;
        task.totalDuration = savedAudio.totalDuration;
        task.totalSize = savedAudio.totalSize;
        task.totalWordCount = audioChapters.reduce((sum, ch) => sum + ch.wordCount, 0);
        task.playlistUrl = `/api/audio/playlist/${taskId}`;
        task.metadataUrl = `/api/audio/metadata/${taskId}`;
        task.audioUrl = `/api/audio/playlist/${taskId}`;
        task.isEnhanced = true;
        task.chapters = audioChapters.map((chapter, index) => ({
            index: index + 1,
            title: chapter.title,
            duration: chapter.duration,
            wordCount: chapter.wordCount
        }));
        task.processingDuration = totalDuration;
        task.externalMetadata = result.metadata; // 保存外部API返回的元数据
        
        await env.KV.put(taskKey, JSON.stringify(task));

        await logger.info('增强版任务处理完成', {
            totalDuration,
            totalChapters: audioChapters.length,
            totalSize: savedAudio.totalSize,
            totalWordCount: task.totalWordCount,
            averageSpeed: Math.round(task.totalWordCount / (totalDuration / 1000)) + '字符/秒',
            processingType: task.processingType,
            processingMethod: 'EXTERNAL_API',
            externalTaskId: task.externalTaskId
        });

        console.log(`增强版任务 ${taskId} 处理完成:`);
        console.log(`- 总章节数: ${audioChapters.length}`);
        console.log(`- 总时长: ${Math.ceil(savedAudio.totalDuration / 60)} 分钟`);
        console.log(`- 总大小: ${Math.ceil(savedAudio.totalSize / 1024 / 1024)} MB`);
        
    } catch (error) {
        const totalDuration = Date.now() - startTime;
        await logger.error('增强版任务处理失败', {
            error: error.message,
            stack: error.stack,
            totalDuration,
            processingType: enableChapterSplit ? 'MULTI_CHAPTER' : 'SINGLE_AUDIO'
        });
        
        console.error(`增强版任务 ${taskId} 处理失败:`, error);
        
        // 更新任务状态为失败
        try {
            const task = await env.KV.get(taskKey, { type: 'json' });
            if (task) {
                task.status = 'FAILED';
                task.error = error.message;
                task.failedAt = new Date().toISOString();
                task.processingDuration = totalDuration;
                await env.KV.put(taskKey, JSON.stringify(task));
            }
        } catch (updateError) {
            console.error('更新失败状态时出错:', updateError);
            await logger.error('更新失败状态时出错', { error: updateError.message });
        }
        
        throw error;
    }
}

// 扣除用户积分并创建记录（增强版 - 带超时和重试）
async function chargeUserPoints(email, points, taskId, env) {
    const maxRetries = 3;
    const timeoutMs = 15000; // 15秒超时
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            console.log(`尝试扣除积分 (${attempt}/${maxRetries}): 用户${email}, 金额${points}`);
            
            // 设置超时控制
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('积分扣除操作超时')), timeoutMs);
            });
            
            const chargePromise = async () => {
                const userKey = `user:${email}`;
                
                // Step 1: 获取用户数据
                console.log('正在获取用户数据...');
                const userData = await env.KV.get(userKey, { type: 'json' });
                
                if (!userData) {
                    throw new Error('用户不存在');
                }

                if (userData.points < points) {
                    throw new Error(`积分不足，当前积分：${userData.points}，需要：${points}`);
                }

                // Step 2: 扣除积分
                console.log(`正在扣除积分: ${userData.points} - ${points} = ${userData.points - points}`);
                userData.points -= points;
                
                // 使用原子性操作更新用户数据
                await env.KV.put(userKey, JSON.stringify(userData));
                console.log('用户积分更新成功');

                // Step 3: 创建积分使用记录
                const pointsRecordId = `points_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                const pointsRecord = {
                    id: pointsRecordId,
                    email: email,
                    type: 'CONSUME',
                    amount: -points,
                    balance: userData.points,
                    description: '多章节音频转换服务',
                    taskId: taskId,
                    createdAt: new Date().toISOString(),
                    attempt: attempt  // 记录是第几次尝试成功的
                };

                const recordKey = `points_record:${email}:${pointsRecordId}`;
                console.log('正在创建积分记录...');
                await env.KV.put(recordKey, JSON.stringify(pointsRecord));
                console.log('积分记录创建成功');

                return userData.points; // 返回剩余积分
            };
            
            // 执行带超时的积分扣除操作
            const remainingPoints = await Promise.race([chargePromise(), timeoutPromise]);
            
            console.log(`用户 ${email} 扣除 ${points} 积分成功，剩余 ${remainingPoints} 积分 (尝试${attempt}次)`);
            return; // 成功则直接返回
            
        } catch (error) {
            console.error(`积分扣除第${attempt}次尝试失败:`, error.message);
            
            // 如果是最后一次尝试，或者是业务逻辑错误（非超时/网络错误），直接抛出
            if (attempt === maxRetries || 
                error.message.includes('用户不存在') || 
                error.message.includes('积分不足')) {
                
                console.error(`积分扣除最终失败 (${attempt}/${maxRetries} 尝试):`, error.message);
                throw error;
            }
            
            // 计算重试延迟（指数退避）
            const retryDelay = Math.min(1000 * Math.pow(2, attempt - 1), 5000); // 1s, 2s, 4s, 最大5s
            console.warn(`积分扣除将在${retryDelay}ms后重试 (${attempt}/${maxRetries})`);
            
            await new Promise(resolve => setTimeout(resolve, retryDelay));
        }
    }
} 