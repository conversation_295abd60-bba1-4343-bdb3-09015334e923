// 任务重启 API - 增强版（专门优化TTS处理卡住问题）
import { TaskLogger } from '../utils/task-logger.js';

export async function onRequestPost({ request, env }) {
    try {
        const { taskId, clearLogs = false, forceRestart = false } = await request.json();
        
        if (!taskId) {
            return Response.json({ error: '任务ID不能为空' }, { status: 400 });
        }

        // 获取任务信息
        const taskKey = `task:${taskId}`;
        const task = await env.KV.get(taskKey, { type: 'json' });
        
        if (!task) {
            return Response.json({ error: '任务不存在' }, { status: 404 });
        }

        const logger = new TaskLogger(taskId, env);
        
        // 分析任务状态和卡住原因
        const stuckAnalysis = analyzeStuckTask(task);
        
        await logger.info('开始任务重启分析', {
            currentStatus: task.status,
            progress: task.progress,
            stuckAnalysis,
            forceRestart,
            clearLogs
        });

        // 清除日志（如果需要）
        if (clearLogs) {
            await logger.clearLogs();
            await logger.info('任务重启 - 日志已清除');
        }

        // 保存重启前的状态信息
        const restartInfo = {
            previousStatus: task.status,
            previousProgress: task.progress,
            previousError: task.error,
            stuckReason: stuckAnalysis.reason,
            restartStrategy: stuckAnalysis.recommendedStrategy,
            restartedAt: new Date().toISOString(),
            restartCount: (task.restartCount || 0) + 1
        };

        // 重置任务状态（根据分析结果使用不同策略）
        task.status = 'PENDING';
        task.progress = 0;
        task.error = null;
        task.restartedAt = restartInfo.restartedAt;
        task.restartCount = restartInfo.restartCount;
        task.lastRestartInfo = restartInfo;
        
        // 添加TTS处理优化标记
        task.processingOptimizations = {
            enableConcurrencyControl: true,
            enableTimeoutControl: true,
            enableRetryMechanism: true,
            maxRetryAttempts: 3,
            concurrencyLimit: 2,
            chunkTimeout: 60000,
            batchSize: 5,
            enableProgressiveRecovery: true
        };
        
        // 清除完成相关的字段，但保留处理历史
        const fieldsToKeep = ['filename', 'email', 'fileSize', 'uploadedAt', 'createdAt', 'processingType'];
        const preservedTask = {};
        fieldsToKeep.forEach(field => {
            if (task[field] !== undefined) {
                preservedTask[field] = task[field];
            }
        });
        
        // 合并保留的字段和新状态
        Object.assign(task, preservedTask, {
            status: 'PENDING',
            progress: 0,
            error: null,
            restartedAt: restartInfo.restartedAt,
            restartCount: restartInfo.restartCount,
            lastRestartInfo: restartInfo,
            processingOptimizations: task.processingOptimizations
        });
        
        // 删除可能导致问题的字段
        delete task.completedAt;
        delete task.failedAt;
        delete task.audioUrl;
        delete task.audioPath;
        delete task.audioSize;
        delete task.processingDuration;
        delete task.pointsCharged; // 允许重新计费逻辑

        await env.KV.put(taskKey, JSON.stringify(task));

        await logger.info('任务已重启（增强模式）', {
            restartCount: task.restartCount,
            previousStatus: restartInfo.previousStatus,
            stuckReason: stuckAnalysis.reason,
            appliedOptimizations: task.processingOptimizations,
            strategy: stuckAnalysis.recommendedStrategy
        });

        // 根据文件类型决定处理方式
        let processEndpoint;
        if (task.processingType === 'MULTI_CHAPTER' || task.filename?.toLowerCase().endsWith('.epub')) {
            processEndpoint = '/api/process-enhanced-task';
            await logger.info('使用增强模式处理器重启任务');
        } else {
            processEndpoint = '/api/process-task';
            await logger.info('使用标准模式处理器重启任务');
        }

        // 触发任务处理（使用优化后的处理器）
        try {
            const processPayload = {
                taskId,
                optimizations: task.processingOptimizations,
                isRestart: true,
                restartCount: task.restartCount
            };
            
            const processResponse = await fetch(`${new URL(request.url).origin}${processEndpoint}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(processPayload)
            });

            if (!processResponse.ok) {
                const errorText = await processResponse.text();
                throw new Error(`任务处理请求失败 (${processResponse.status}): ${errorText}`);
            }

            await logger.info('优化后的任务处理已触发', {
                endpoint: processEndpoint,
                optimizations: task.processingOptimizations
            });

        } catch (processError) {
            await logger.error('触发任务处理失败', { 
                error: processError.message,
                endpoint: processEndpoint
            });
            
            // 更新任务状态为失败，但不抛出错误
            task.status = 'FAILED';
            task.error = `重启后处理触发失败: ${processError.message}`;
            await env.KV.put(taskKey, JSON.stringify(task));
        }

        return Response.json({
            success: true,
            message: '任务重启成功（增强模式）',
            taskId,
            restartCount: task.restartCount,
            newStatus: task.status,
            stuckAnalysis: stuckAnalysis,
            appliedOptimizations: task.processingOptimizations,
            processingEndpoint: processEndpoint,
            recommendation: '任务已使用增强的TTS处理器重启，包含超时控制、重试机制和并发优化'
        });

    } catch (error) {
        console.error('重启任务失败:', error);
        return Response.json({ 
            error: error.message,
            taskId,
            recommendation: '请稍后重试，或联系技术支持'
        }, { status: 500 });
    }
}

// GET 请求 - 获取可重启的任务列表（增强版）
export async function onRequestGet({ env }) {
    try {
        const taskList = await env.KV.list({ prefix: 'task:' });
        const restartableTasks = [];
        
        for (const key of taskList.keys) {
            const task = await env.KV.get(key.name, { type: 'json' });
            if (task && shouldTaskBeRestartable(task)) {
                const stuckAnalysis = analyzeStuckTask(task);
                
                restartableTasks.push({
                    id: task.id,
                    filename: task.filename,
                    status: task.status,
                    progress: task.progress,
                    createdAt: task.createdAt,
                    lastProgressUpdate: task.lastProgressUpdate,
                    error: task.error,
                    restartCount: task.restartCount || 0,
                    isStuck: stuckAnalysis.isStuck,
                    stuckReason: stuckAnalysis.reason,
                    recommendedStrategy: stuckAnalysis.recommendedStrategy,
                    stuckDuration: stuckAnalysis.stuckDuration,
                    fileType: getFileType(task.filename),
                    canRestart: stuckAnalysis.canRestart,
                    priority: calculateRestartPriority(task, stuckAnalysis)
                });
            }
        }
        
        // 按优先级和创建时间排序
        restartableTasks.sort((a, b) => {
            if (a.priority !== b.priority) {
                return b.priority - a.priority; // 高优先级在前
            }
            return new Date(b.createdAt) - new Date(a.createdAt);
        });
        
        return Response.json({
            success: true,
            tasks: restartableTasks,
            totalTasks: restartableTasks.length,
            summary: {
                stuck: restartableTasks.filter(t => t.isStuck).length,
                failed: restartableTasks.filter(t => t.status === 'FAILED').length,
                highPriority: restartableTasks.filter(t => t.priority >= 8).length
            }
        });

    } catch (error) {
        console.error('获取可重启任务列表失败:', error);
        return Response.json({ error: error.message }, { status: 500 });
    }
}

// 增强的任务卡住分析
function analyzeStuckTask(task) {
    const now = new Date();
    const lastUpdate = new Date(task.lastProgressUpdate || task.createdAt);
    const stuckDuration = now - lastUpdate;
    const stuckMinutes = Math.floor(stuckDuration / 60000);
    
    let reason = '未知';
    let recommendedStrategy = 'standard_restart';
    let canRestart = true;
    let isStuck = false;

    if (task.status === 'PROCESSING') {
        if (stuckDuration > 5 * 60 * 1000) { // 5分钟
            isStuck = true;
            
            if (stuckMinutes > 30) {
                reason = 'TTS_API_TIMEOUT_SEVERE';
                recommendedStrategy = 'enhanced_restart_with_optimizations';
            } else if (stuckMinutes > 10) {
                reason = 'TTS_API_TIMEOUT_MODERATE';
                recommendedStrategy = 'restart_with_concurrency_control';
            } else {
                reason = 'TTS_API_TIMEOUT_MILD';
                recommendedStrategy = 'restart_with_timeout_control';
            }
            
            // 检查是否是EPUB处理
            if (task.filename?.toLowerCase().endsWith('.epub') || task.processingType === 'MULTI_CHAPTER') {
                reason += '_EPUB_PROCESSING';
                recommendedStrategy = 'enhanced_restart_with_optimizations';
            }
            
            // 检查重启次数
            if ((task.restartCount || 0) >= 3) {
                reason += '_MULTIPLE_RESTART_FAILURES';
                canRestart = false;
                recommendedStrategy = 'manual_intervention_required';
            }
        }
    } else if (task.status === 'FAILED') {
        isStuck = false;
        canRestart = true;
        
        if (task.error?.includes('timeout') || task.error?.includes('超时')) {
            reason = 'PREVIOUS_TIMEOUT_FAILURE';
            recommendedStrategy = 'enhanced_restart_with_optimizations';
        } else if (task.error?.includes('TTS') || task.error?.includes('转换')) {
            reason = 'TTS_PROCESSING_FAILURE';
            recommendedStrategy = 'restart_with_concurrency_control';
        } else {
            reason = 'GENERAL_PROCESSING_FAILURE';
            recommendedStrategy = 'standard_restart';
        }
    }

    return {
        isStuck,
        reason,
        recommendedStrategy,
        canRestart,
        stuckDuration: stuckMinutes,
        analysis: {
            timeSinceLastUpdate: stuckMinutes,
            currentStatus: task.status,
            progress: task.progress,
            restartCount: task.restartCount || 0,
            hasError: !!task.error
        }
    };
}

// 判断任务是否应该可重启
function shouldTaskBeRestartable(task) {
    return (
        task.status === 'FAILED' || 
        (task.status === 'PROCESSING' && analyzeStuckTask(task).isStuck) ||
        task.status === 'TIMEOUT'
    );
}

// 获取文件类型
function getFileType(filename) {
    if (!filename) return 'unknown';
    const ext = filename.toLowerCase().split('.').pop();
    return ext || 'unknown';
}

// 计算重启优先级
function calculateRestartPriority(task, stuckAnalysis) {
    let priority = 5; // 基础优先级
    
    // 根据卡住时间调整
    if (stuckAnalysis.stuckDuration > 30) priority += 3;
    else if (stuckAnalysis.stuckDuration > 10) priority += 2;
    else if (stuckAnalysis.stuckDuration > 5) priority += 1;
    
    // 根据重启次数调整
    const restartCount = task.restartCount || 0;
    if (restartCount === 0) priority += 2; // 首次重启优先
    else if (restartCount >= 3) priority -= 3; // 多次重启降低优先级
    
    // 根据文件类型调整
    if (task.filename?.toLowerCase().endsWith('.epub')) priority += 1;
    
    // 根据错误类型调整
    if (task.error?.includes('timeout')) priority += 2;
    
    return Math.max(1, Math.min(10, priority)); // 限制在1-10范围内
} 