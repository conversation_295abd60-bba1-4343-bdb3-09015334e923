// 修复特定任务状态的API
import { UserIdResolver } from '../utils/user-id-resolver.js';

export async function onRequestPost({ request, env }) {
    try {
        const { email, taskId, forceUserId } = await request.json();
        
        if (!email || !taskId) {
            return Response.json({ error: '邮箱和任务ID不能为空' }, { status: 400 });
        }
        
        console.log(`🔧 开始修复特定任务 - 邮箱: ${email}, 任务ID: ${taskId}`);
        
        // 获取任务详情
        const task = await env.KV.get(`task:${taskId}`, { type: 'json' });
        
        if (!task) {
            return Response.json({ error: '任务不存在' }, { status: 404 });
        }
        
        if (task.email !== email) {
            return Response.json({ error: '无权访问该任务' }, { status: 403 });
        }
        
        const result = {
            taskId: taskId,
            originalStatus: task.status,
            newStatus: null,
            updated: false,
            filesFound: [],
            userIdResolution: null,
            pathsChecked: [],
            fixMethod: null,
            error: null
        };
        
        // 如果任务已经是成功状态，直接返回
        if (task.status === 'SUCCESS' || task.status === 'COMPLETED') {
            result.newStatus = task.status;
            result.fixMethod = 'already_completed';
            return Response.json({
                success: true,
                result: result,
                message: '任务已经是完成状态'
            });
        }
        
        // 尝试多种方法修复任务状态
        let filesFound = false;
        
        // 方法1：使用用户ID解析器
        try {
            const resolver = new UserIdResolver(env);
            const userIdResult = await resolver.resolveUserId(email, taskId);
            result.userIdResolution = userIdResult;
            
            if (userIdResult.userId) {
                const checkResult = await checkR2WithUserId(env, userIdResult.userId, taskId);
                result.pathsChecked.push(...checkResult.pathsChecked);
                result.filesFound.push(...checkResult.files);
                
                if (checkResult.found) {
                    filesFound = true;
                    result.fixMethod = 'user_id_resolver';
                }
            }
        } catch (error) {
            console.warn('用户ID解析器方法失败:', error);
        }
        
        // 方法2：如果指定了强制用户ID，使用它
        if (!filesFound && forceUserId) {
            try {
                const checkResult = await checkR2WithUserId(env, forceUserId, taskId);
                result.pathsChecked.push(...checkResult.pathsChecked);
                result.filesFound.push(...checkResult.files);
                
                if (checkResult.found) {
                    filesFound = true;
                    result.fixMethod = 'force_user_id';
                }
            } catch (error) {
                console.warn('强制用户ID方法失败:', error);
            }
        }
        
        // 方法3：通用用户ID发现策略
        if (!filesFound) {
            try {
                const discoveryResult = await universalUserIdDiscovery(env, email, taskId);
                result.pathsChecked.push(...discoveryResult.pathsChecked);
                result.filesFound.push(...discoveryResult.files);

                if (discoveryResult.found) {
                    filesFound = true;
                    result.fixMethod = 'universal_discovery';
                    result.discoveredUserId = discoveryResult.userId;
                    result.discoveryMethod = discoveryResult.method;
                }
            } catch (error) {
                console.warn('通用用户ID发现失败:', error);
            }
        }
        
        // 方法4：模糊搜索
        if (!filesFound) {
            try {
                const fuzzyResult = await fuzzySearchR2(env, taskId);
                result.pathsChecked.push('fuzzy_search');
                result.filesFound.push(...fuzzyResult.files);
                
                if (fuzzyResult.found) {
                    filesFound = true;
                    result.fixMethod = 'fuzzy_search';
                }
            } catch (error) {
                console.warn('模糊搜索方法失败:', error);
            }
        }
        
        // 如果找到文件，更新任务状态
        if (filesFound) {
            const updatedTask = {
                ...task,
                status: 'SUCCESS',
                progress: 100,
                completedAt: new Date().toISOString(),
                lastProgressUpdate: new Date().toISOString(),
                fixedAt: new Date().toISOString(),
                fixMethod: result.fixMethod
            };
            
            // 设置音频URL
            if (!updatedTask.audioUrl) {
                if (updatedTask.isEnhanced || updatedTask.totalChapters > 1) {
                    updatedTask.audioUrl = `/api/audio/playlist/${taskId}`;
                    updatedTask.playlistUrl = `/api/audio/playlist/${taskId}`;
                    updatedTask.metadataUrl = `/api/audio/metadata/${taskId}`;
                } else {
                    updatedTask.audioUrl = `/api/audio/${taskId}.mp3`;
                }
            }
            
            // 保存更新的任务
            await env.KV.put(`task:${taskId}`, JSON.stringify(updatedTask));
            
            result.updated = true;
            result.newStatus = 'SUCCESS';
            
            console.log(`✅ 任务 ${taskId} 已成功修复为 SUCCESS 状态，使用方法: ${result.fixMethod}`);
        } else {
            result.error = '未找到对应的音频文件';
            console.log(`❌ 任务 ${taskId} 修复失败：未找到音频文件`);
        }
        
        return Response.json({
            success: result.updated,
            result: result,
            message: result.updated ? 
                `任务状态已成功修复为 ${result.newStatus}` : 
                '未找到对应的音频文件，无法修复任务状态'
        });
        
    } catch (error) {
        console.error('修复特定任务失败:', error);
        return Response.json({ 
            error: '修复失败: ' + error.message,
            success: false
        }, { status: 500 });
    }
}

// 使用指定用户ID检查R2存储
async function checkR2WithUserId(env, userId, taskId) {
    const result = {
        found: false,
        files: [],
        pathsChecked: []
    };
    
    const pathPatterns = [
        `users/${userId}/audios/${taskId}/`,
        `users/${userId}/audiobooks/${taskId}/`,
        `users/${userId}/audio/${taskId}/`
    ];
    
    for (const path of pathPatterns) {
        result.pathsChecked.push(path);
        
        try {
            // 尝试多个R2绑定
            const r2Bindings = [
                { name: 'R2', bucket: env.R2 },
                { name: 'STORAGE', bucket: env.STORAGE },
                { name: 'AUDIO_BUCKET', bucket: env.AUDIO_BUCKET }
            ].filter(binding => binding.bucket);

            let listResult = null;
            for (const binding of r2Bindings) {
                try {
                    listResult = await binding.bucket.list({
                        prefix: path,
                        limit: 20
                    });
                    if (listResult && listResult.objects && listResult.objects.length > 0) {
                        console.log(`✅ 使用绑定 ${binding.name} 找到文件`);
                        break;
                    }
                } catch (bindingError) {
                    console.warn(`绑定 ${binding.name} 失败:`, bindingError.message);
                    continue;
                }
            }

            if (!listResult) {
                throw new Error('所有R2绑定都失败');
            }
            
            if (listResult.objects && listResult.objects.length > 0) {
                const audioFiles = listResult.objects.filter(obj => {
                    const key = obj.key.toLowerCase();
                    return key.endsWith('.mp3') || key.endsWith('.wav') || key.endsWith('.m4a');
                });
                
                result.files.push(...listResult.objects.map(obj => ({
                    key: obj.key,
                    size: obj.size,
                    type: obj.key.toLowerCase().endsWith('.mp3') ? 'audio' : 'other',
                    path: path
                })));
                
                if (audioFiles.length > 0) {
                    result.found = true;
                    break;
                }
            }
        } catch (error) {
            console.warn(`检查路径 ${path} 失败:`, error);
        }
    }
    
    return result;
}

// 模糊搜索R2存储
async function fuzzySearchR2(env, taskId) {
    const result = {
        found: false,
        files: []
    };
    
    try {
        // 尝试多个R2绑定
        const r2Bindings = [
            { name: 'R2', bucket: env.R2 },
            { name: 'STORAGE', bucket: env.STORAGE },
            { name: 'AUDIO_BUCKET', bucket: env.AUDIO_BUCKET }
        ].filter(binding => binding.bucket);

        let listResult = null;
        for (const binding of r2Bindings) {
            try {
                listResult = await binding.bucket.list({
                    prefix: 'users/',
                    limit: 1000
                });
                if (listResult && listResult.delimitedPrefixes && listResult.delimitedPrefixes.length > 0) {
                    console.log(`✅ 使用绑定 ${binding.name} 进行反向搜索`);
                    break;
                }
            } catch (bindingError) {
                console.warn(`绑定 ${binding.name} 反向搜索失败:`, bindingError.message);
                continue;
            }
        }

        if (!listResult) {
            throw new Error('所有R2绑定的反向搜索都失败');
        }
        
        if (listResult.objects) {
            const matchingFiles = listResult.objects.filter(obj => 
                obj.key.includes(taskId)
            );
            
            if (matchingFiles.length > 0) {
                const audioFiles = matchingFiles.filter(obj => {
                    const key = obj.key.toLowerCase();
                    return key.endsWith('.mp3') || key.endsWith('.wav') || key.endsWith('.m4a');
                });
                
                result.files.push(...matchingFiles.map(obj => ({
                    key: obj.key,
                    size: obj.size,
                    type: 'fuzzy_match'
                })));
                
                if (audioFiles.length > 0) {
                    result.found = true;
                }
            }
        }
    } catch (error) {
        console.error('模糊搜索失败:', error);
    }
    
    return result;
}

// 通用用户ID发现策略
async function universalUserIdDiscovery(env, email, taskId) {
    const result = {
        found: false,
        files: [],
        pathsChecked: [],
        userId: null,
        method: null
    };

    try {
        // 生成潜在的用户ID候选列表
        const candidates = await generateUserIdCandidates(env, email, taskId);

        for (const candidate of candidates) {
            const checkResult = await checkR2WithUserId(env, candidate.id, taskId);
            result.pathsChecked.push(...checkResult.pathsChecked);

            if (checkResult.found) {
                result.found = true;
                result.files.push(...checkResult.files);
                result.userId = candidate.id;
                result.method = candidate.method;

                console.log(`🎯 通用发现成功找到用户ID: ${candidate.id} (方法: ${candidate.method})`);

                // 更新用户数据
                await updateUserWithDiscoveredId(env, email, candidate.id, candidate.method);

                break;
            }
        }
    } catch (error) {
        console.error('通用用户ID发现失败:', error);
    }

    return result;
}

// 生成用户ID候选列表
async function generateUserIdCandidates(env, email, taskId) {
    const candidates = [];

    // 方法1：邮箱哈希算法（多种变体）
    const emailVariants = [
        email.trim().toLowerCase(),
        email.trim(),
        email.toLowerCase(),
        email
    ];

    for (const variant of emailVariants) {
        try {
            const hash = hashEmail(variant);
            candidates.push({
                id: hash,
                method: `emailHash_${variant === email.trim().toLowerCase() ? 'standard' : 'variant'}`,
                confidence: variant === email.trim().toLowerCase() ? 90 : 70
            });
        } catch (error) {
            // 继续尝试其他变体
        }
    }

    // 方法2：从任务ID提取
    if (taskId.includes('_')) {
        const parts = taskId.split('_');
        if (parts.length > 1 && parts[1].length >= 3) {
            candidates.push({
                id: parts[1],
                method: 'taskId_extraction',
                confidence: 60
            });
        }
    }

    // 方法3：从邮箱用户名生成
    const emailParts = email.split('@');
    if (emailParts.length > 0) {
        const username = emailParts[0];
        if (username.match(/^[a-z0-9]+$/i) && username.length >= 3) {
            candidates.push({
                id: username.toLowerCase(),
                method: 'email_username',
                confidence: 40
            });
        }
    }

    // 方法4：从用户的其他任务推断
    try {
        const userKey = `user:${email}`;
        const userData = await env.KV.get(userKey, { type: 'json' });

        if (userData?.taskIds && Array.isArray(userData.taskIds)) {
            const recentTasks = userData.taskIds.slice(-5);

            for (const otherTaskId of recentTasks) {
                if (otherTaskId !== taskId) {
                    try {
                        const task = await env.KV.get(`task:${otherTaskId}`, { type: 'json' });
                        if (task && task.audioUrl) {
                            const match = task.audioUrl.match(/users\/([^\/]+)\/(audios|audiobooks)/);
                            if (match) {
                                candidates.push({
                                    id: match[1],
                                    method: 'task_inference',
                                    confidence: 85
                                });
                            }
                        }
                    } catch (error) {
                        // 继续检查其他任务
                    }
                }
            }
        }
    } catch (error) {
        console.warn('从任务推断用户ID失败:', error);
    }

    // 方法5：R2反向搜索
    try {
        // 尝试多个R2绑定进行模糊搜索
        const r2Bindings = [
            { name: 'R2', bucket: env.R2 },
            { name: 'STORAGE', bucket: env.STORAGE },
            { name: 'AUDIO_BUCKET', bucket: env.AUDIO_BUCKET }
        ].filter(binding => binding.bucket);

        let searchResult = null;
        for (const binding of r2Bindings) {
            try {
                searchResult = await binding.bucket.list({
                    prefix: 'users/',
                    limit: 500
                });
                if (searchResult && searchResult.objects && searchResult.objects.length > 0) {
                    console.log(`✅ 使用绑定 ${binding.name} 进行模糊搜索`);
                    break;
                }
            } catch (bindingError) {
                console.warn(`绑定 ${binding.name} 模糊搜索失败:`, bindingError.message);
                continue;
            }
        }

        if (!searchResult) {
            throw new Error('所有R2绑定的模糊搜索都失败');
        }

        if (searchResult.objects) {
            const matchingFiles = searchResult.objects.filter(obj =>
                obj.key.includes(taskId)
            );

            for (const file of matchingFiles) {
                const match = file.key.match(/users\/([^\/]+)\//);
                if (match) {
                    candidates.push({
                        id: match[1],
                        method: 'r2_reverse_search',
                        confidence: 95
                    });
                }
            }
        }
    } catch (error) {
        console.warn('R2反向搜索失败:', error);
    }

    // 去重并排序
    const uniqueCandidates = [];
    const seenIds = new Set();

    for (const candidate of candidates) {
        if (!seenIds.has(candidate.id)) {
            seenIds.add(candidate.id);
            uniqueCandidates.push(candidate);
        }
    }

    // 按置信度排序
    uniqueCandidates.sort((a, b) => b.confidence - a.confidence);

    return uniqueCandidates;
}

// 邮箱哈希算法
function hashEmail(email) {
    if (!email || typeof email !== 'string') {
        throw new Error('邮箱地址无效');
    }

    let hash = 0;
    for (let i = 0; i < email.length; i++) {
        const char = email.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash;
    }

    return Math.abs(hash).toString(36);
}

// 更新用户数据
async function updateUserWithDiscoveredId(env, email, userId, method) {
    try {
        const userKey = `user:${email}`;
        const userData = await env.KV.get(userKey, { type: 'json' });

        if (userData) {
            userData.userId = userId;
            userData.userIdDiscoveryMethod = method;
            userData.userIdDiscoveredAt = new Date().toISOString();

            await env.KV.put(userKey, JSON.stringify(userData));
            console.log(`✅ 已更新用户 ${email} 的用户ID为 ${userId}`);
        }
    } catch (error) {
        console.warn('更新用户数据失败:', error);
    }
}

export async function onRequestOptions() {
    return new Response(null, {
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type',
            'Access-Control-Max-Age': '86400'
        }
    });
}
