/**
 * R2任务状态更新API
 * 专门用于更新R2中的任务状态
 */

import { R2TaskStatusManager } from '../utils/r2-task-status-manager.js';

/**
 * 获取单个任务状态
 */
export async function onRequestGet({ request, env }) {
    const url = new URL(request.url);
    const pathSegments = url.pathname.split('/');
    const taskId = pathSegments[pathSegments.length - 1];
    const email = url.searchParams.get('email');

    if (!taskId || !email) {
        return Response.json({ error: '任务ID和邮箱不能为空' }, { status: 400 });
    }

    try {
        const statusManager = new R2TaskStatusManager(env.R2, env);
        const status = await statusManager.getTaskStatus(taskId, email);

        if (!status) {
            return Response.json({ error: '任务不存在' }, { status: 404 });
        }

        const formattedStatus = R2TaskStatusManager.formatTaskForDisplay(status);

        return Response.json({
            success: true,
            task: formattedStatus,
            dataSource: 'R2'
        });

    } catch (error) {
        console.error(`获取R2任务状态失败 ${taskId}:`, error);
        return Response.json({ 
            error: '获取任务状态失败',
            details: error.message 
        }, { status: 500 });
    }
}

/**
 * 更新任务状态
 */
export async function onRequestPost({ request, env }) {
    try {
        const { taskId, email, updates, options = {} } = await request.json();

        if (!taskId || !email || !updates) {
            return Response.json({ error: '任务ID、邮箱和更新数据不能为空' }, { status: 400 });
        }

        const statusManager = new R2TaskStatusManager(env.R2, env);
        
        // 验证任务存在
        const currentStatus = await statusManager.getTaskStatus(taskId, email);
        if (!currentStatus) {
            return Response.json({ error: '任务不存在' }, { status: 404 });
        }

        // 执行更新
        const updatedStatus = await statusManager.updateTaskStatus(taskId, email, updates, options);

        return Response.json({
            success: true,
            task: updatedStatus,
            message: '任务状态更新成功',
            dataSource: 'R2'
        });

    } catch (error) {
        console.error('更新R2任务状态失败:', error);
        return Response.json({ 
            error: '更新任务状态失败',
            details: error.message 
        }, { status: 500 });
    }
}

/**
 * 标记任务开始处理
 */
export async function onRequestPatch({ request, env }) {
    try {
        const { taskId, email, action, data = {} } = await request.json();

        if (!taskId || !email || !action) {
            return Response.json({ error: '任务ID、邮箱和操作类型不能为空' }, { status: 400 });
        }

        const statusManager = new R2TaskStatusManager(env.R2, env);
        let result;

        switch (action) {
            case 'start':
                result = await statusManager.markAsProcessing(taskId, email);
                break;
                
            case 'complete':
                result = await statusManager.markAsCompleted(taskId, email, data);
                break;
                
            case 'fail':
                const error = data.error || '未知错误';
                result = await statusManager.markAsFailed(taskId, email, error, data);
                break;
                
            default:
                return Response.json({ error: '不支持的操作类型' }, { status: 400 });
        }

        return Response.json({
            success: true,
            task: result,
            action: action,
            message: `任务${action}操作成功`,
            dataSource: 'R2'
        });

    } catch (error) {
        console.error('R2任务操作失败:', error);
        return Response.json({ 
            error: '任务操作失败',
            details: error.message 
        }, { status: 500 });
    }
}

/**
 * 删除任务状态
 */
export async function onRequestDelete({ request, env }) {
    const url = new URL(request.url);
    const pathSegments = url.pathname.split('/');
    const taskId = pathSegments[pathSegments.length - 1];
    const email = url.searchParams.get('email');

    if (!taskId || !email) {
        return Response.json({ error: '任务ID和邮箱不能为空' }, { status: 400 });
    }

    try {
        const statusManager = new R2TaskStatusManager(env.R2, env);
        
        // 验证任务存在
        const exists = await statusManager.taskExists(taskId, email);
        if (!exists) {
            return Response.json({ error: '任务不存在' }, { status: 404 });
        }

        // 删除任务状态
        const deleted = await statusManager.deleteTaskStatus(taskId, email);

        if (deleted) {
            return Response.json({
                success: true,
                message: '任务状态删除成功',
                taskId: taskId
            });
        } else {
            return Response.json({ error: '删除任务状态失败' }, { status: 500 });
        }

    } catch (error) {
        console.error('删除R2任务状态失败:', error);
        return Response.json({ 
            error: '删除任务状态失败',
            details: error.message 
        }, { status: 500 });
    }
}

/**
 * 获取任务统计信息
 */
export async function onRequestOptions({ request, env }) {
    const url = new URL(request.url);
    const email = url.searchParams.get('email');

    if (!email) {
        return Response.json({ error: '邮箱不能为空' }, { status: 400 });
    }

    try {
        const statusManager = new R2TaskStatusManager(env.R2, env);
        const statistics = await statusManager.getTaskStatistics(email);

        if (!statistics) {
            return Response.json({ error: '获取统计信息失败' }, { status: 500 });
        }

        return Response.json({
            success: true,
            statistics: statistics,
            dataSource: 'R2'
        });

    } catch (error) {
        console.error('获取R2任务统计失败:', error);
        return Response.json({ 
            error: '获取统计信息失败',
            details: error.message 
        }, { status: 500 });
    }
}

/**
 * 健康检查和架构信息
 */
export async function onRequestHead({ request, env }) {
    try {
        // 简单的健康检查
        const statusManager = new R2TaskStatusManager(env.R2, env);
        
        return new Response(null, {
            status: 200,
            headers: {
                'X-Architecture': 'R2',
                'X-Status-Manager': 'R2TaskStatusManager',
                'X-Health': 'OK',
                'Cache-Control': 'no-cache'
            }
        });

    } catch (error) {
        return new Response(null, {
            status: 503,
            headers: {
                'X-Architecture': 'R2',
                'X-Health': 'ERROR',
                'X-Error': error.message
            }
        });
    }
}
