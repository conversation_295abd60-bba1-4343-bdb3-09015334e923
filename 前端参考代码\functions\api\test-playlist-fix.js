// 测试播放列表修复API
import { EnhancedR2StorageManager } from '../utils/enhanced-r2-storage.js';

export async function onRequestGet({ request, env, params }) {
    try {
        const url = new URL(request.url);
        const taskId = url.searchParams.get('taskId') || '1749972653214_ievr6x3q0';
        const email = url.searchParams.get('email') || '<EMAIL>';

        console.log(`测试播放列表修复: 任务ID=${taskId}, 邮箱=${email}`);

        // 初始化存储管理器
        const r2Storage = new EnhancedR2StorageManager(env.R2);
        const emailHash = r2Storage.hashEmail(email);

        console.log(`计算的邮箱哈希: ${emailHash}`);

        // 测试1: 直接获取播放列表
        console.log('=== 测试1: 直接获取播放列表 ===');
        const playlist = await r2Storage.getBookPlaylist(email, taskId);
        
        // 测试2: 获取元数据
        console.log('=== 测试2: 获取元数据 ===');
        const metadata = await r2Storage.getBookMetadata(email, taskId);

        // 测试3: 列出所有文件
        console.log('=== 测试3: 列出所有文件 ===');
        const basePath = `users/${emailHash}/audiobooks/${taskId}`;
        const listResult = await env.R2.list({ prefix: basePath });
        
        const files = listResult.objects.map(obj => ({
            key: obj.key,
            size: obj.size,
            lastModified: obj.uploaded
        }));

        // 测试4: 直接读取播放列表文件
        console.log('=== 测试4: 直接读取播放列表文件 ===');
        const playlistPath = `${basePath}/playlist.json`;
        let directPlaylistContent = null;
        try {
            const playlistObject = await env.R2.get(playlistPath);
            if (playlistObject) {
                directPlaylistContent = await playlistObject.text();
                console.log(`直接读取播放列表成功，内容长度: ${directPlaylistContent.length}`);
            } else {
                console.log('直接读取播放列表失败：文件不存在');
            }
        } catch (error) {
            console.error('直接读取播放列表失败:', error);
        }

        return Response.json({
            success: true,
            taskId,
            email,
            emailHash,
            basePath,
            results: {
                playlist: playlist ? {
                    title: playlist.title,
                    totalChapters: playlist.totalChapters,
                    chaptersCount: playlist.chapters?.length || 0
                } : null,
                metadata: metadata ? {
                    title: metadata.title,
                    totalChapters: metadata.totalChapters,
                    chaptersCount: metadata.chapters?.length || 0
                } : null,
                files: files,
                directPlaylistContent: directPlaylistContent ? {
                    length: directPlaylistContent.length,
                    preview: directPlaylistContent.substring(0, 500)
                } : null
            }
        }, {
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Content-Type': 'application/json'
            }
        });

    } catch (error) {
        console.error('测试播放列表修复失败:', error);
        return Response.json({ 
            error: '测试失败',
            message: error.message,
            stack: error.stack
        }, { status: 500 });
    }
}

export async function onRequestOptions() {
    return new Response(null, {
        status: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type'
        }
    });
} 