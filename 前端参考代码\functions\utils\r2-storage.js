/**
 * R2存储管理工具类
 * 负责处理文件的上传、下载、删除等操作
 */

export class R2StorageManager {
    constructor(r2Bucket) {
        this.r2 = r2Bucket;
    }

    /**
     * 生成邮箱哈希值用于目录名
     */
    hashEmail(email) {
        // 参数验证
        if (!email || typeof email !== 'string' || email.trim() === '') {
            throw new Error('邮箱地址不能为空或无效');
        }
        
        const cleanEmail = email.trim().toLowerCase();
        
        // 验证邮箱格式
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(cleanEmail)) {
            throw new Error(`邮箱格式无效: ${email}`);
        }
        
        // 使用简单的哈希算法生成目录名
        let hash = 0;
        for (let i = 0; i < cleanEmail.length; i++) {
            const char = cleanEmail.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        
        const hashResult = Math.abs(hash).toString(36);
        console.log(`邮箱哈希生成: ${email} -> ${hashResult}`);
        return hashResult;
    }

    /**
     * 生成文件路径
     */
    generatePath(email, category, taskId, filename) {
        // 参数验证
        if (!email || typeof email !== 'string') {
            throw new Error('邮箱参数无效');
        }
        if (!category || typeof category !== 'string') {
            throw new Error('分类参数无效');
        }
        if (!taskId || typeof taskId !== 'string') {
            throw new Error('任务ID参数无效');
        }
        if (!filename || typeof filename !== 'string') {
            throw new Error('文件名参数无效');
        }

        const emailHash = this.hashEmail(email);
        // 正确的路径格式：不需要 audiobook-storage 前缀
        const path = `users/${emailHash}/${category}/${taskId}/${filename}`;

        // 验证生成的路径不包含undefined
        if (path.includes('undefined')) {
            throw new Error(`生成的路径包含undefined: ${path}`);
        }

        console.log(`生成R2路径: ${path}`);
        return path;
    }

    /**
     * 保存用户上传的原始文件
     */
    async saveUploadedFile(email, taskId, filename, fileContent, fileType = 'text/plain') {
        const path = this.generatePath(email, 'tasks', taskId, `original.${this.getFileExtension(filename)}`);

        try {
            await this.r2.put(path, fileContent, {
                httpMetadata: {
                    contentType: fileType,
                    cacheControl: 'public, max-age=31536000'
                },
                customMetadata: {
                    email: email,
                    taskId: taskId,
                    originalFilename: filename,
                    uploadedAt: new Date().toISOString()
                }
            });

            console.log(`文件已保存到R2: ${path}`);
            return path;
        } catch (error) {
            console.error('保存上传文件失败:', error);
            throw new Error('文件保存失败');
        }
    }

    /**
     * 保存提取的文本内容
     */
    async saveExtractedText(email, taskId, textContent) {
        const path = this.generatePath(email, 'tasks', taskId, 'extracted_text.txt');

        try {
            await this.r2.put(path, textContent, {
                httpMetadata: {
                    contentType: 'text/plain; charset=utf-8'
                },
                customMetadata: {
                    email: email,
                    taskId: taskId,
                    extractedAt: new Date().toISOString(),
                    textLength: textContent.length.toString()
                }
            });

            console.log(`提取的文本已保存到R2: ${path}`);
            return path;
        } catch (error) {
            console.error('保存提取文本失败:', error);
            throw new Error('文本保存失败');
        }
    }

    /**
     * 保存生成的音频文件
     */
    async saveAudioFile(email, taskId, audioBuffer, metadata = {}) {
        const path = this.generatePath(email, 'audios', taskId, 'audio.mp3');
        
        try {
            await this.r2.put(path, audioBuffer, {
                httpMetadata: {
                    contentType: 'audio/mpeg',
                    cacheControl: 'public, max-age=31536000'
                },
                customMetadata: {
                    email: email,
                    taskId: taskId,
                    createdAt: new Date().toISOString(),
                    audioSize: audioBuffer.byteLength.toString(),
                    ...metadata
                }
            });

            // 保存音频元数据
            if (Object.keys(metadata).length > 0) {
                const metadataPath = this.generatePath(email, 'audios', taskId, 'metadata.json');
                await this.r2.put(metadataPath, JSON.stringify(metadata, null, 2), {
                    httpMetadata: {
                        contentType: 'application/json'
                    }
                });
            }

            console.log(`音频文件已保存到R2: ${path}`);
            return path;
        } catch (error) {
            console.error('保存音频文件失败:', error);
            throw new Error('音频保存失败');
        }
    }

    /**
     * 保存网页内容
     */
    async saveWebContent(email, taskId, originalContent, extractedText, url) {
        try {
            // 保存原始网页内容
            const originalPath = this.generatePath(email, 'web_content', taskId, 'original_content.html');
            await this.r2.put(originalPath, originalContent, {
                httpMetadata: {
                    contentType: 'text/html; charset=utf-8'
                },
                customMetadata: {
                    email: email,
                    taskId: taskId,
                    sourceUrl: url,
                    fetchedAt: new Date().toISOString()
                }
            });

            // 保存提取的文本
            const textPath = this.generatePath(email, 'web_content', taskId, 'extracted_text.txt');
            await this.r2.put(textPath, extractedText, {
                httpMetadata: {
                    contentType: 'text/plain; charset=utf-8'
                },
                customMetadata: {
                    email: email,
                    taskId: taskId,
                    sourceUrl: url,
                    extractedAt: new Date().toISOString(),
                    textLength: extractedText.length.toString()
                }
            });

            console.log(`网页内容已保存到R2: ${originalPath}, ${textPath}`);
            return { originalPath, textPath };
        } catch (error) {
            console.error('保存网页内容失败:', error);
            throw new Error('网页内容保存失败');
        }
    }

    /**
     * 获取文件
     */
    async getFile(path) {
        try {
            const object = await this.r2.get(path);
            if (!object) {
                return null;
            }
            return object;
        } catch (error) {
            console.error(`获取文件失败 ${path}:`, error);
            return null;
        }
    }

    /**
     * 获取文件的指定范围（支持 Range 请求）
     */
    async getFileRange(path, start, end) {
        try {
            // 首先获取文件信息以确定文件大小
            const headObject = await this.r2.head(path);
            if (!headObject) {
                console.error(`文件不存在: ${path}`);
                return null;
            }

            const fileSize = headObject.size;
            
            // 处理范围参数
            const actualStart = Math.max(0, start || 0);
            const actualEnd = end !== undefined ? Math.min(end, fileSize - 1) : fileSize - 1;
            
            // 验证范围
            if (actualStart >= fileSize || actualEnd >= fileSize || actualStart > actualEnd) {
                console.error(`无效的范围请求: ${actualStart}-${actualEnd}, 文件大小: ${fileSize}`);
                return null;
            }

            const rangeLength = actualEnd - actualStart + 1;
            
            console.log(`获取文件范围: ${path}, ${actualStart}-${actualEnd}/${fileSize} (${rangeLength} bytes)`);

            const object = await this.r2.get(path, {
                range: { offset: actualStart, length: rangeLength }
            });
            
            if (!object) {
                console.error(`获取文件范围失败: ${path}`);
                return null;
            }
            
            return object;
        } catch (error) {
            console.error(`获取文件范围失败 ${path} (${start}-${end}):`, error);
            return null;
        }
    }

    /**
     * 获取文件头信息（不下载文件内容）
     */
    async headFile(path) {
        try {
            const object = await this.r2.head(path);
            if (!object) {
                return null;
            }
            return object;
        } catch (error) {
            console.error(`获取文件头信息失败 ${path}:`, error);
            return null;
        }
    }

    /**
     * 获取音频文件
     */
    async getAudioFile(email, taskId) {
        const path = this.generatePath(email, 'audios', taskId, 'audio.mp3');
        return await this.getFile(path);
    }

    /**
     * 获取文本内容
     */
    async getTextContent(email, taskId, category = 'tasks') {
        const path = this.generatePath(email, category, taskId, 'extracted_text.txt');
        const object = await this.getFile(path);
        if (object) {
            return await object.text();
        }

        // 向后兼容：如果在新路径找不到，尝试旧路径
        if (category === 'tasks') {
            const legacyPath = this.generatePath(email, 'uploads', taskId, 'extracted_text.txt');
            const legacyObject = await this.getFile(legacyPath);
            if (legacyObject) {
                console.log(`从旧路径获取文本内容: ${legacyPath}`);
                return await legacyObject.text();
            }
        }

        return null;
    }

    /**
     * 删除任务相关的所有文件
     */
    async deleteTaskFiles(email, taskId) {
        const emailHash = this.hashEmail(email);
        const basePaths = [
            `users/${emailHash}/tasks/${taskId}/`,      // 新路径
            `users/${emailHash}/uploads/${taskId}/`,    // 旧路径（向后兼容）
            `users/${emailHash}/audios/${taskId}/`,
            `users/${emailHash}/audiobooks/${taskId}/`, // 增强版音频
            `users/${emailHash}/web_content/${taskId}/`
        ];

        const deletePromises = [];

        for (const basePath of basePaths) {
            try {
                const objects = await this.r2.list({ prefix: basePath });
                for (const object of objects.objects) {
                    deletePromises.push(this.r2.delete(object.key));
                }
            } catch (error) {
                console.error(`列出文件失败 ${basePath}:`, error);
            }
        }

        try {
            await Promise.all(deletePromises);
            console.log(`任务 ${taskId} 的所有文件已删除`);
        } catch (error) {
            console.error(`删除任务文件失败 ${taskId}:`, error);
            throw new Error('文件删除失败');
        }
    }

    /**
     * 清理临时文件
     */
    async cleanupTempFiles(olderThanHours = 24) {
        try {
            const tempObjects = await this.r2.list({ prefix: 'temp/' });
            const cutoffTime = new Date(Date.now() - olderThanHours * 60 * 60 * 1000);

            const deletePromises = [];
            for (const object of tempObjects.objects) {
                if (object.uploaded < cutoffTime) {
                    deletePromises.push(this.r2.delete(object.key));
                }
            }

            await Promise.all(deletePromises);
            console.log(`清理了 ${deletePromises.length} 个临时文件`);
        } catch (error) {
            console.error('清理临时文件失败:', error);
        }
    }

    /**
     * 获取用户存储使用情况
     */
    async getUserStorageUsage(email) {
        const emailHash = this.hashEmail(email);
        const prefix = `users/${emailHash}/`;

        try {
            const objects = await this.r2.list({ prefix });
            let totalSize = 0;
            let fileCount = 0;

            for (const object of objects.objects) {
                totalSize += object.size;
                fileCount++;
            }

            return {
                totalSize,
                fileCount,
                formattedSize: this.formatFileSize(totalSize)
            };
        } catch (error) {
            console.error('获取存储使用情况失败:', error);
            return { totalSize: 0, fileCount: 0, formattedSize: '0 B' };
        }
    }

    /**
     * 工具方法：获取文件扩展名
     */
    getFileExtension(filename) {
        const lastDot = filename.lastIndexOf('.');
        return lastDot > 0 ? filename.substring(lastDot + 1).toLowerCase() : 'txt';
    }

    /**
     * 工具方法：格式化文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 检查文件是否存在
     */
    async fileExists(path) {
        try {
            const object = await this.r2.head(path);
            return object !== null;
        } catch (error) {
            // 如果文件不存在，R2 会返回错误
            return false;
        }
    }

    /**
     * 保存文件到指定路径
     */
    async saveFile(path, content, contentType = 'application/octet-stream') {
        try {
            await this.r2.put(path, content, {
                httpMetadata: {
                    contentType: contentType,
                    cacheControl: 'public, max-age=31536000'
                },
                customMetadata: {
                    uploadedAt: new Date().toISOString()
                }
            });

            console.log(`文件已保存到R2: ${path}`);
            return path;
        } catch (error) {
            console.error('保存文件失败:', error);
            throw new Error(`文件保存失败: ${error.message}`);
        }
    }

    /**
     * 创建任务完成标记文件
     * 用于后端通知前端任务已完成
     */
    async createCompletionMarker(email, taskId, metadata = {}) {
        const userHash = this.hashEmail(email);
        const markerPath = `users/${userHash}/tasks/${taskId}/.completed`;

        const markerData = {
            taskId: taskId,
            email: email,
            completedAt: new Date().toISOString(),
            markerVersion: '1.0',
            ...metadata
        };

        try {
            await this.r2.put(markerPath, JSON.stringify(markerData), {
                httpMetadata: {
                    contentType: 'application/json'
                },
                customMetadata: {
                    type: 'completion-marker',
                    taskId: taskId,
                    email: email
                }
            });

            console.log(`✅ 任务完成标记已创建: ${markerPath}`);
            return markerPath;
        } catch (error) {
            console.error('创建完成标记失败:', error);
            throw new Error('无法创建任务完成标记');
        }
    }

    /**
     * 检查任务完成标记是否存在
     */
    async checkCompletionMarker(email, taskId) {
        const userHash = this.hashEmail(email);
        const markerPath = `users/${userHash}/tasks/${taskId}/.completed`;

        try {
            const markerObject = await this.r2.get(markerPath);
            if (markerObject) {
                const markerData = await markerObject.json();
                return {
                    exists: true,
                    data: markerData,
                    path: markerPath
                };
            }
            return { exists: false };
        } catch (error) {
            console.error('检查完成标记失败:', error);
            return { exists: false };
        }
    }

    /**
     * 删除任务完成标记文件
     * 在状态同步完成后清理
     */
    async removeCompletionMarker(email, taskId) {
        const userHash = this.hashEmail(email);
        const markerPath = `users/${userHash}/tasks/${taskId}/.completed`;

        try {
            await this.r2.delete(markerPath);
            console.log(`🗑️ 任务完成标记已删除: ${markerPath}`);
            return true;
        } catch (error) {
            console.error('删除完成标记失败:', error);
            return false;
        }
    }

    /**
     * 批量检查多个任务的完成标记
     * 用于前端批量状态同步
     */
    async batchCheckCompletionMarkers(email, taskIds) {
        const userHash = this.hashEmail(email);
        const results = [];

        for (const taskId of taskIds) {
            const markerResult = await this.checkCompletionMarker(email, taskId);
            if (markerResult.exists) {
                results.push({
                    taskId: taskId,
                    markerData: markerResult.data,
                    markerPath: markerResult.path
                });
            }
        }

        console.log(`📋 批量检查完成标记: ${taskIds.length} 个任务，发现 ${results.length} 个完成标记`);
        return results;
    }
}