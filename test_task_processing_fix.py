#!/usr/bin/env python3
"""
测试任务处理服务修复的脚本
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ.setdefault('PYTHONPATH', str(project_root))

async def test_task_processing_fix():
    """测试任务处理服务对新音频合并结果的处理"""
    try:
        print("开始测试任务处理服务修复...")
        
        # 模拟新的音频合并结果结构
        new_conversion_result = {
            "audio_file": {
                "filename": "test_merged_audio.mp3",
                "url": "/files/users/1/tasks/123/test_merged_audio.mp3",
                "title": "测试音频",
                "duration": 120.5,
                "size": 482000,
                "format": "mp3",
                "segments_merged": 3
            },
            "total_duration": 120.5,
            "word_count": 500,
            "segments_processed": 3,
            "title": "测试音频"
        }
        
        print("新的转换结果结构:")
        print(f"  audio_file: {new_conversion_result['audio_file']}")
        print(f"  total_duration: {new_conversion_result['total_duration']}")
        print(f"  segments_processed: {new_conversion_result['segments_processed']}")
        
        # 测试任务处理逻辑
        print("\n测试任务结果处理逻辑...")
        
        # 模拟任务处理服务中的逻辑
        conversion_result = new_conversion_result
        
        # 适配新的音频合并结果结构
        if "audio_file" in conversion_result:
            # 新的合并音频格式：单个音频文件
            audio_file = conversion_result["audio_file"]
            task_audio_files = [audio_file]  # 转换为列表格式以保持兼容性
            task_playlist_url = None  # 不再使用播放列表
            print("✅ 检测到新的音频合并格式")
        else:
            # 兼容旧格式（如果存在）
            task_audio_files = conversion_result.get("audio_files", [])
            task_playlist_url = conversion_result.get("playlist_url")
            print("⚠️  使用旧格式兼容模式")
        
        task_total_duration = conversion_result["total_duration"]
        
        print(f"\n处理后的任务数据:")
        print(f"  task.audio_files: {task_audio_files}")
        print(f"  task.playlist_url: {task_playlist_url}")
        print(f"  task.total_duration: {task_total_duration}")
        
        # 验证结果
        if len(task_audio_files) == 1:
            audio_file = task_audio_files[0]
            print(f"\n✅ 成功处理单个合并音频文件:")
            print(f"  文件名: {audio_file.get('filename')}")
            print(f"  URL: {audio_file.get('url')}")
            print(f"  时长: {audio_file.get('duration')}秒")
            print(f"  大小: {audio_file.get('size')} bytes")
            print(f"  合并的分段数: {audio_file.get('segments_merged')}")
            
            if task_playlist_url is None:
                print("✅ 正确设置playlist_url为None")
            else:
                print("⚠️  playlist_url应该为None")
                
            return True
        else:
            print("❌ 音频文件处理失败")
            return False
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_old_format_compatibility():
    """测试对旧格式的兼容性"""
    try:
        print("\n开始测试旧格式兼容性...")
        
        # 模拟旧的结果结构（多个分段文件 + 播放列表）
        old_conversion_result = {
            "audio_files": [
                {
                    "filename": "segment_0001.mp3",
                    "url": "/files/users/1/tasks/123/segment_0001.mp3",
                    "duration": 60,
                    "size": 240000
                },
                {
                    "filename": "segment_0002.mp3", 
                    "url": "/files/users/1/tasks/123/segment_0002.mp3",
                    "duration": 60,
                    "size": 240000
                }
            ],
            "playlist_url": "/files/users/1/tasks/123/playlist_123.json",
            "total_duration": 120,
            "word_count": 500,
            "segment_count": 2,
            "title": "测试音频"
        }
        
        print("旧的转换结果结构:")
        print(f"  audio_files: {len(old_conversion_result['audio_files'])} 个文件")
        print(f"  playlist_url: {old_conversion_result['playlist_url']}")
        print(f"  total_duration: {old_conversion_result['total_duration']}")
        
        # 测试任务处理逻辑
        conversion_result = old_conversion_result
        
        # 适配新的音频合并结果结构
        if "audio_file" in conversion_result:
            # 新的合并音频格式：单个音频文件
            audio_file = conversion_result["audio_file"]
            task_audio_files = [audio_file]
            task_playlist_url = None
            print("使用新格式")
        else:
            # 兼容旧格式（如果存在）
            task_audio_files = conversion_result.get("audio_files", [])
            task_playlist_url = conversion_result.get("playlist_url")
            print("✅ 使用旧格式兼容模式")
        
        task_total_duration = conversion_result["total_duration"]
        
        print(f"\n处理后的任务数据:")
        print(f"  task.audio_files: {len(task_audio_files)} 个文件")
        print(f"  task.playlist_url: {task_playlist_url}")
        print(f"  task.total_duration: {task_total_duration}")
        
        # 验证结果
        if len(task_audio_files) == 2 and task_playlist_url is not None:
            print("✅ 旧格式兼容性测试通过")
            return True
        else:
            print("❌ 旧格式兼容性测试失败")
            return False
        
    except Exception as e:
        print(f"兼容性测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("任务处理服务修复测试")
    print("=" * 50)
    
    # 运行测试
    success1 = asyncio.run(test_task_processing_fix())
    success2 = asyncio.run(test_old_format_compatibility())
    
    if success1 and success2:
        print("\n✅ 所有测试通过！任务处理服务修复成功。")
        print("现在可以正确处理新的音频合并结果格式，同时保持对旧格式的兼容性。")
    else:
        print("\n❌ 测试失败！请检查修复代码。")
        sys.exit(1)
