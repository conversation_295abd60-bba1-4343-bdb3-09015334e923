// 调试播放列表 API
export async function onRequestGet({ request, env }) {
    try {
        const url = new URL(request.url);
        const email = url.searchParams.get('email');
        const taskId = url.searchParams.get('taskId');
        
        if (!email || !taskId) {
            return Response.json({ 
                error: '缺少必要参数：email 和 taskId' 
            }, { status: 400 });
        }

        console.log(`调试播放列表: 邮箱=${email}, 任务=${taskId}`);

        // 计算邮箱哈希 - 与用户ID解析器保持一致
        function hashEmail(email) {
            if (!email || typeof email !== 'string' || email.trim() === '') {
                throw new Error('邮箱地址不能为空或无效');
            }
            
            const cleanEmail = email.trim().toLowerCase();
            
            // 验证邮箱格式
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(cleanEmail)) {
                throw new Error(`邮箱格式无效: ${email}`);
            }
            
            let hash = 0;
            for (let i = 0; i < cleanEmail.length; i++) {
                const char = cleanEmail.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash; // 转换为32位整数
            }
            
            const hashResult = Math.abs(hash).toString(36);
            console.log(`邮箱哈希生成: ${email} -> ${hashResult}`);
            return hashResult;
        }

        const emailHash = hashEmail(email);
        const basePath = `users/${emailHash}/audiobooks/${taskId}`;
        const playlistPath = `${basePath}/playlist.json`;
        const metadataPath = `${basePath}/metadata.json`;

        console.log(`调试播放列表: 基础路径=${basePath}`);
        console.log(`调试播放列表: 播放列表路径=${playlistPath}`);
        console.log(`调试播放列表: 元数据路径=${metadataPath}`);

        const result = {
            email,
            taskId,
            emailHash,
            basePath,
            playlistPath,
            metadataPath,
            files: [],
            playlistExists: false,
            playlistContent: null,
            playlistSize: 0,
            metadataExists: false,
            metadataContent: null,
            errors: []
        };

        // 列出所有文件
        try {
            const listResult = await env.R2.list({ prefix: basePath });
            result.files = listResult.objects.map(obj => ({
                key: obj.key,
                size: obj.size,
                lastModified: obj.uploaded
            }));
            console.log(`调试播放列表: 找到 ${result.files.length} 个文件`);
        } catch (error) {
            result.errors.push(`列出文件失败: ${error.message}`);
        }

        // 检查播放列表文件
        try {
            const playlistObject = await env.R2.get(playlistPath);
            if (playlistObject) {
                result.playlistExists = true;
                result.playlistSize = playlistObject.size;
                result.playlistContent = await playlistObject.text();
                console.log(`调试播放列表: 播放列表文件存在，大小=${result.playlistSize}`);
                
                // 尝试解析JSON
                try {
                    const parsed = JSON.parse(result.playlistContent);
                    result.playlistParsed = true;
                    result.playlistChapters = parsed.chapters?.length || 0;
                } catch (parseError) {
                    result.playlistParsed = false;
                    result.errors.push(`播放列表JSON解析失败: ${parseError.message}`);
                }
            } else {
                console.log(`调试播放列表: 播放列表文件不存在`);
            }
        } catch (error) {
            result.errors.push(`读取播放列表失败: ${error.message}`);
        }

        // 检查元数据文件
        try {
            const metadataObject = await env.R2.get(metadataPath);
            if (metadataObject) {
                result.metadataExists = true;
                result.metadataContent = await metadataObject.text();
                console.log(`调试播放列表: 元数据文件存在`);
                
                // 尝试解析JSON
                try {
                    const parsed = JSON.parse(result.metadataContent);
                    result.metadataParsed = true;
                    result.metadataChapters = parsed.chapters?.length || 0;
                } catch (parseError) {
                    result.metadataParsed = false;
                    result.errors.push(`元数据JSON解析失败: ${parseError.message}`);
                }
            } else {
                console.log(`调试播放列表: 元数据文件不存在`);
            }
        } catch (error) {
            result.errors.push(`读取元数据失败: ${error.message}`);
        }

        return Response.json(result, {
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type',
            }
        });

    } catch (error) {
        console.error('调试播放列表失败:', error);
        return Response.json({
            error: '服务器内部错误',
            details: error.message
        }, { status: 500 });
    }
}

export async function onRequestOptions() {
    return new Response(null, {
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type',
            'Access-Control-Max-Age': '86400'
        }
    });
} 