// URL任务处理 API
import { TTSProcessor } from '../utils/tts-processor.js';
import { WebScraper } from '../utils/web-scraper.js';
import { R2StorageManager } from '../utils/r2-storage.js';
import { TaskLogger } from '../utils/task-logger.js';
import { AudiobookApiClient } from '../utils/audiobook-api-client.js';

export async function onRequestPost({ request, env }) {
    try {
        const { taskId } = await request.json();
        
        if (!taskId) {
            return Response.json({ error: '任务ID不能为空' }, { status: 400 });
        }

        // 处理URL任务
        await processUrlTask(taskId, env);

        return Response.json({
            success: true,
            message: 'URL任务处理完成'
        });

    } catch (error) {
        console.error('处理URL任务失败:', error);
        return Response.json({ error: error.message }, { status: 500 });
    }
}

// 主要的URL任务处理函数
async function processUrlTask(taskId, env) {
    const taskKey = `task:${taskId}`;
    const logger = new TaskLogger(taskId, env);
    const startTime = Date.now();
    
    try {
        await logger.info('URL任务处理开始', { taskId });
        
        // 获取任务信息
        await logger.stepStart('获取任务信息');
        const task = await env.KV.get(taskKey, { type: 'json' });
        if (!task) {
            throw new Error('任务不存在');
        }

        if (task.type !== 'url') {
            throw new Error('不是URL类型的任务');
        }
        await logger.stepComplete('获取任务信息', null, { 
            url: task.url, 
            email: task.email,
            createdAt: task.createdAt
        });

        // 更新状态为处理中
        await logger.stepStart('更新任务状态');
        task.status = 'PROCESSING';
        task.progress = 5;
        task.startedAt = new Date().toISOString();
        await env.KV.put(taskKey, JSON.stringify(task));
        await logger.progress(5, 'URL任务状态已更新为处理中');
        await logger.stepComplete('更新任务状态');

        console.log(`开始处理URL任务 ${taskId}, URL: ${task.url}`);
        await logger.info('开始处理URL任务', { url: task.url });

        // 初始化工具
        await logger.stepStart('初始化处理工具');
        const webScraper = new WebScraper();
        const r2Storage = new R2StorageManager(env.R2);
        await logger.stepComplete('初始化处理工具');
        
        // 抓取网页内容
        await logger.stepStart('抓取网页内容');
        console.log(`步骤1: 抓取网页内容`);
        task.progress = 15;
        await env.KV.put(taskKey, JSON.stringify(task));
        await logger.progress(15, '开始抓取网页内容');

        const webContentStartTime = Date.now();
        const webContent = await webScraper.extractContentFromUrl(task.url);
        const webContentDuration = Date.now() - webContentStartTime;
        
        // 更新任务标题
        if (webContent.title && webContent.title !== '无标题') {
            task.title = webContent.title;
        }
        
        task.progress = 25;
        await env.KV.put(taskKey, JSON.stringify(task));
        await logger.progress(25, '网页内容抓取完成');
        
        await logger.stepComplete('抓取网页内容', webContentDuration, {
            title: webContent.title,
            contentLength: webContent.content.length,
            extractionTime: webContentDuration,
            url: task.url
        });

        // 验证内容质量
        await logger.stepStart('验证内容质量');
        if (!webContent.content || webContent.content.trim().length < 100) {
            throw new Error('网页内容过少或无法提取有效内容');
        }
        await logger.stepComplete('验证内容质量', null, {
            contentLength: webContent.content.length,
            qualityCheck: 'PASSED'
        });

        console.log(`网页内容提取完成: 标题="${webContent.title}", 内容长度=${webContent.content.length}字符`);
        await logger.info('网页内容提取成功', { 
            title: webContent.title,
            contentLength: webContent.content.length,
            estimatedDuration: Math.round(webContent.content.length / 5) + '秒'
        });

        // 保存网页内容到R2
        await logger.stepStart('保存网页内容');
        await r2Storage.saveWebContent(
            task.email,
            taskId,
            webContent.originalHtml || '',
            webContent.content,
            task.url
        );
        await logger.stepComplete('保存网页内容');

        // 创建进度更新回调
        const progressCallback = async (progress, stage) => {
            const currentTask = await env.KV.get(taskKey, { type: 'json' });
            if (currentTask) {
                currentTask.progress = Math.max(progress, 30); // 确保进度不会倒退
                currentTask.currentStage = stage || '音频转换中';
                currentTask.lastProgressUpdate = new Date().toISOString();
                await env.KV.put(taskKey, JSON.stringify(currentTask));
                await logger.progress(currentTask.progress, `URL音频转换进度更新: ${currentTask.progress}% - ${currentTask.currentStage}`);
            }
        };

        // 使用外部API进行文本转语音处理
        await logger.stepStart('调用外部音频转换API（URL模式）');
        const apiStartTime = Date.now();
        
        try {
            // 初始化API客户端
            const apiClient = new AudiobookApiClient();
            
            // 将网页内容保存为文本文件并使用R2路径
            await logger.stepStart('保存网页内容为文本文件');
            const emailHash = task.email.replace('@', '_').replace('.', '_');
            const textFileName = `users/${emailHash}/tasks/${taskId}/extracted_content.txt`;
            const textBuffer = new TextEncoder().encode(webContent.content);
            await r2Storage.saveFile(textFileName, textBuffer, 'text/plain');
            
            const r2FilePath = textFileName;
            
            await logger.stepComplete('保存网页内容为文本文件', null, {
                r2FilePath: r2FilePath,
                textLength: webContent.content.length,
                sourceUrl: task.url
            });

            // 提交转换任务（使用R2路径）
            await logger.stepStart('提交URL内容音频转换任务（R2路径模式）');
            const convertOptions = {
                language: 'zh-CN',
                voiceName: 'zh-CN-XiaochenMultilingualNeural',
                speed: 1.0,
                pitch: 0,
                volume: 1.0
            };
            
            const taskInfo = await apiClient.submitConvertTaskWithR2Path(r2FilePath, convertOptions);
            await logger.stepComplete('提交URL内容音频转换任务（R2路径模式）', null, {
                externalTaskId: taskInfo.task_id,
                status: taskInfo.status,
                estimatedDuration: taskInfo.estimated_duration,
                sourceUrl: task.url,
                r2FilePath: r2FilePath
            });

            // 保存外部任务ID
            task.externalTaskId = taskInfo.task_id;
            task.progress = 25;
            task.currentStage = '音频转换队列中';
            await env.KV.put(taskKey, JSON.stringify(task));

            // 等待转换完成
            await logger.stepStart('等待URL内容音频转换完成');
            const result = await apiClient.waitForTaskCompletion(
                taskInfo.task_id,
                (progress, stage) => {
                    // 将外部进度映射到25-85%范围
                    const mappedProgress = 25 + (progress * 0.6);
                    progressCallback(mappedProgress, stage || '音频转换中');
                },
                1800000, // 30分钟超时
                5000     // 5秒轮询间隔
            );
            
            const apiDuration = Date.now() - apiStartTime;
            await logger.stepComplete('等待URL内容音频转换完成', apiDuration, {
                externalTaskId: taskInfo.task_id,
                totalDuration: apiDuration,
                result: {
                    status: result.status,
                    audioFiles: Object.keys(result.audio_files || {}),
                    metadata: result.metadata
                }
            });

            // 从外部API结果中获取音频文件信息
            if (!result.audio_files || !result.audio_files.mp3) {
                throw new Error('外部API未返回有效的音频文件');
            }

            // 获取音频文件的R2存储路径（后端直接返回路径）
            await logger.stepStart('获取音频文件R2存储路径');
            let audioPath;
            let audioDuration = 0;
            
            if (result.audio_files.mp3.chapters && result.audio_files.mp3.chapters.length > 0) {
                const audioChapter = result.audio_files.mp3.chapters[0]; // URL任务通常只有一个音频文件
                
                // 后端现在直接返回R2路径而不是完整URL
                if (audioChapter.r2_path) {
                    // 后端直接提供R2路径
                    audioPath = audioChapter.r2_path;
                } else if (audioChapter.url && audioChapter.url.includes('/users/')) {
                    // 兼容模式：从URL中提取R2路径
                    audioPath = audioChapter.url.split('/users/')[1];
                    audioPath = 'users/' + audioPath;
                } else {
                    // 使用默认路径格式（基于用户邮箱和任务ID）
                    audioPath = `users/${emailHash}/audios/${taskId}/url_audio.mp3`;
                }
                
                // 解析时长（格式如 "00:05:30"）
                const durationParts = audioChapter.duration.split(':');
                audioDuration = parseInt(durationParts[0]) * 3600 + parseInt(durationParts[1]) * 60 + parseInt(durationParts[2]);
            } else {
                // 使用默认路径格式
                audioPath = `users/${emailHash}/audios/${taskId}/url_audio.mp3`;
                audioDuration = Math.ceil(webContent.content.length / 5); // 估算时长
            }
            
            await logger.stepComplete('获取音频文件R2存储路径', null, {
                audioPath,
                audioDuration,
                externalTaskId: taskInfo.task_id
            });

            // 验证音频文件是否存在于R2存储中
            await logger.stepStart('验证音频文件存在性');
            const audioExists = await r2Storage.fileExists(audioPath);
            
            if (!audioExists) {
                // 等待一段时间让外部API完成文件上传
                console.log('音频文件不存在，等待5秒后重试验证...');
                await new Promise(resolve => setTimeout(resolve, 5000));
                
                const retryExists = await r2Storage.fileExists(audioPath);
                if (!retryExists) {
                    throw new Error(`音频文件在R2存储中不存在: ${audioPath}`);
                }
            }
            
            await logger.stepComplete('验证音频文件存在性', null, { 
                audioPath: audioPath,
                exists: true 
            });

            // 任务成功完成后扣除积分
            if (!task.pointsCharged) {
                await logger.stepStart('扣除用户积分');
                
                try {
                    console.log(`开始扣除用户积分: ${task.email}, 50积分`);
                    await chargeUserPoints(task.email, 50, taskId, env);
                    
                    task.pointsCharged = true;
                    console.log('积分扣除成功，更新任务状态');
                    
                    await logger.stepComplete('扣除用户积分', null, { 
                        points: 50,
                        success: true,
                        userEmail: task.email,
                        sourceUrl: task.url
                    });
                } catch (pointsError) {
                    console.error('积分扣除失败，但音频已生成:', pointsError.message);
                    
                    // 积分扣除失败时的处理
                    await logger.stepFailed('扣除用户积分', pointsError, {
                        points: 50,
                        userEmail: task.email,
                        audioAlreadyGenerated: true,
                        sourceUrl: task.url,
                        errorType: pointsError.message.includes('超时') ? 'TIMEOUT' : 
                                   pointsError.message.includes('积分不足') ? 'INSUFFICIENT_POINTS' : 'UNKNOWN'
                    });
                    
                    // 标记积分扣除失败，但不影响音频文件
                    task.pointsCharged = false;
                    task.pointsError = pointsError.message;
                    task.pointsFailedAt = new Date().toISOString();
                    
                    // 记录警告但继续完成任务
                    console.warn('URL音频文件已生成，但积分扣除失败。任务将标记为成功，但积分问题需要人工处理。');
                    
                    // 注意：这里我们不抛出错误，让任务继续完成
                    // 因为音频已经生成，用户应该能获得结果
                }
            }

            // 保存音频元数据到本地存储（音频文件已由外部API保存）
            await logger.stepStart('保存音频元数据');
            
            // 获取实际音频文件大小
            let actualAudioSize = 0;
            try {
                const audioObject = await r2Storage.fileExists(audioPath) ? await env.R2.head(audioPath) : null;
                actualAudioSize = audioObject ? audioObject.size : 0;
            } catch (error) {
                console.warn('无法获取音频文件大小:', error);
            }
            
            // 构建音频元数据
            const audioMetadata = {
                wordCount: webContent.content.length,
                duration: audioDuration,
                language: 'zh-CN',
                voice: 'zh-CN-XiaochenMultilingualNeural',
                sourceUrl: task.url,
                title: task.title || '网页内容',
                externalTaskId: taskInfo.task_id,
                audioPath: audioPath,
                savedByExternalAPI: true,
                createdAt: new Date().toISOString()
            };
            
            // 保存元数据到KV存储（用于API查询）
            const audioMetadataKey = `audio_metadata:${taskId}`;
            await env.KV.put(audioMetadataKey, JSON.stringify(audioMetadata));
            
            await logger.stepComplete('保存音频元数据', null, {
                audioPath: audioPath,
                audioSize: actualAudioSize,
                duration: audioDuration,
                sourceUrl: task.url
            });

            // 更新任务状态为完成
            const totalDuration = Date.now() - startTime;
            task.status = 'SUCCESS';
            task.progress = 100;
            task.completedAt = new Date().toISOString();
            task.audioUrl = `/api/audio/${taskId}.mp3`;
            task.audioPath = audioPath;
            task.audioSize = audioDuration;
            task.wordCount = webContent.content.length;
            task.processingDuration = totalDuration;
            task.externalMetadata = result.metadata; // 保存外部API返回的元数据
            
            await env.KV.put(taskKey, JSON.stringify(task));

            await logger.info('URL任务处理完成', {
                totalDuration,
                audioSize: audioDuration,
                wordCount: webContent.content.length,
                averageSpeed: Math.round(webContent.content.length / (totalDuration / 1000)) + '字符/秒',
                sourceUrl: task.url,
                title: webContent.title,
                processingMethod: 'EXTERNAL_API',
                externalTaskId: taskInfo.task_id
            });

            console.log(`URL任务 ${taskId} 处理完成, 音频大小: ${audioDuration} seconds, 字数: ${webContent.content.length}`);
            
        } catch (apiError) {
            await logger.stepFailed('调用外部音频转换API', apiError, {
                contentLength: webContent.content.length,
                processingTime: Date.now() - apiStartTime,
                externalTaskId: task.externalTaskId,
                sourceUrl: task.url
            });
            throw apiError;
        }

    } catch (error) {
        const totalDuration = Date.now() - startTime;
        await logger.error('URL任务处理失败', {
            error: error.message,
            stack: error.stack,
            totalDuration
        });
        
        console.error(`URL任务 ${taskId} 处理失败:`, error);
        
        // 更新任务状态为失败
        try {
            const task = await env.KV.get(taskKey, { type: 'json' });
            if (task) {
                task.status = 'FAILED';
                task.error = error.message;
                task.failedAt = new Date().toISOString();
                task.processingDuration = totalDuration;
                await env.KV.put(taskKey, JSON.stringify(task));
            }
        } catch (updateError) {
            console.error('更新失败状态时出错:', updateError);
            await logger.error('更新失败状态时出错', { error: updateError.message });
        }
        
        throw error;
    }
}

// 扣除用户积分并创建记录（增强版 - 带超时和重试）
async function chargeUserPoints(email, points, taskId, env) {
    const maxRetries = 3;
    const timeoutMs = 15000; // 15秒超时
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            console.log(`尝试扣除积分 (${attempt}/${maxRetries}): 用户${email}, 金额${points}`);
            
            // 设置超时控制
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('积分扣除操作超时')), timeoutMs);
            });
            
            const chargePromise = async () => {
                const userKey = `user:${email}`;
                
                // Step 1: 获取用户数据
                console.log('正在获取用户数据...');
                const userData = await env.KV.get(userKey, { type: 'json' });
                
                if (!userData) {
                    throw new Error('用户不存在');
                }

                if (userData.points < points) {
                    throw new Error(`积分不足，当前积分：${userData.points}，需要：${points}`);
                }

                // Step 2: 扣除积分
                console.log(`正在扣除积分: ${userData.points} - ${points} = ${userData.points - points}`);
                userData.points -= points;
                
                // 使用原子性操作更新用户数据
                await env.KV.put(userKey, JSON.stringify(userData));
                console.log('用户积分更新成功');

                // Step 3: 创建积分使用记录
                const pointsRecordId = `points_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                const pointsRecord = {
                    id: pointsRecordId,
                    email: email,
                    type: 'CONSUME',
                    amount: -points,
                    balance: userData.points,
                    description: '音频转换服务',
                    taskId: taskId,
                    createdAt: new Date().toISOString(),
                    attempt: attempt  // 记录是第几次尝试成功的
                };

                const recordKey = `points_record:${email}:${pointsRecordId}`;
                console.log('正在创建积分记录...');
                await env.KV.put(recordKey, JSON.stringify(pointsRecord));
                console.log('积分记录创建成功');

                return userData.points; // 返回剩余积分
            };
            
            // 执行带超时的积分扣除操作
            const remainingPoints = await Promise.race([chargePromise(), timeoutPromise]);
            
            console.log(`用户 ${email} 扣除 ${points} 积分成功，剩余 ${remainingPoints} 积分 (尝试${attempt}次)`);
            return; // 成功则直接返回
            
        } catch (error) {
            console.error(`积分扣除第${attempt}次尝试失败:`, error.message);
            
            // 如果是最后一次尝试，或者是业务逻辑错误（非超时/网络错误），直接抛出
            if (attempt === maxRetries || 
                error.message.includes('用户不存在') || 
                error.message.includes('积分不足')) {
                
                console.error(`积分扣除最终失败 (${attempt}/${maxRetries} 尝试):`, error.message);
                throw error;
            }
            
            // 计算重试延迟（指数退避）
            const retryDelay = Math.min(1000 * Math.pow(2, attempt - 1), 5000); // 1s, 2s, 4s, 最大5s
            console.warn(`积分扣除将在${retryDelay}ms后重试 (${attempt}/${maxRetries})`);
            
            await new Promise(resolve => setTimeout(resolve, retryDelay));
        }
    }
}