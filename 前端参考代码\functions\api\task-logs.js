// 任务日志查看 API
import { TaskLogger } from '../utils/task-logger.js';

// 验证管理员身份
async function verifyAdmin(email, password, env) {
    try {
        // 获取第一个注册的用户作为管理员
        const userList = await env.KV.list({ prefix: 'user:' });
        if (userList.keys.length === 0) {
            return { success: false, message: '系统中没有注册用户' };
        }
        
        // 按创建时间排序，获取第一个用户
        const users = [];
        for (const key of userList.keys) {
            const user = await env.KV.get(key.name, { type: 'json' });
            if (user) {
                users.push(user);
            }
        }
        
        users.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
        const firstUser = users[0];
        
        if (!firstUser) {
            return { success: false, message: '无法获取管理员信息' };
        }
        
        // 验证凭据
        if (firstUser.email === email && firstUser.password === password) {
            return { success: true, admin: firstUser };
        } else {
            return { success: false, message: '管理员凭据验证失败' };
        }
    } catch (error) {
        console.error('管理员验证失败:', error);
        return { success: false, message: '验证过程出错' };
    }
}

export async function onRequestPost({ request, env }) {
    try {
        const requestData = await request.json();
        const { taskId, level, count = 100, adminEmail, adminPassword } = requestData;
        
        // 验证管理员身份
        if (!adminEmail || !adminPassword) {
            return Response.json({ error: '需要管理员凭据' }, { status: 401 });
        }
        
        const adminVerification = await verifyAdmin(adminEmail, adminPassword, env);
        if (!adminVerification.success) {
            return Response.json({ error: adminVerification.message }, { status: 403 });
        }
        
        if (!taskId) {
            return Response.json({ error: '任务ID不能为空' }, { status: 400 });
        }

        const logger = new TaskLogger(taskId, env);
        
        let logs;
        if (level) {
            logs = await logger.getLogsByLevel(level);
        } else {
            logs = await logger.getRecentLogs(count);
        }

        // 获取任务基本信息
        const taskKey = `task:${taskId}`;
        const task = await env.KV.get(taskKey, { type: 'json' });

        return Response.json({
            success: true,
            taskId,
            task,
            logs,
            totalLogs: logs.length,
            timestamp: new Date().toISOString(),
            admin: adminVerification.admin.email
        });

    } catch (error) {
        console.error('获取任务日志失败:', error);
        return Response.json({ 
            error: error.message,
            stack: error.stack,
            type: 'POST_REQUEST_ERROR'
        }, { status: 500 });
    }
}

// GET 请求 - 获取任务列表和状态概览
export async function onRequestGet({ request, env }) {
    try {
        // 解析URL参数
        const url = new URL(request.url);
        const taskId = url.searchParams.get('taskId');
        const adminEmail = url.searchParams.get('adminEmail');
        const adminPassword = url.searchParams.get('adminPassword');
        
        // 验证管理员身份
        if (!adminEmail || !adminPassword) {
            return Response.json({ error: '需要管理员凭据' }, { status: 401 });
        }
        
        const adminVerification = await verifyAdmin(adminEmail, adminPassword, env);
        if (!adminVerification.success) {
            return Response.json({ error: adminVerification.message }, { status: 403 });
        }
        
        // 检查基本环境
        if (!env || !env.KV) {
            throw new Error('KV存储未配置');
        }
        
        if (taskId) {
            // 获取特定任务的日志
            const logger = new TaskLogger(taskId, env);
            const logs = await logger.getLogs();
            const task = await env.KV.get(`task:${taskId}`, { type: 'json' });
            
            return Response.json({
                success: true,
                taskId,
                task,
                logs,
                totalLogs: logs.length,
                admin: adminVerification.admin.email
            });
        } else {
            // 获取所有任务的概览
            const taskList = await env.KV.list({ prefix: 'task:' });
            const tasks = [];
            
            // 限制处理数量，避免超时
            const keysToProcess = taskList.keys.slice(0, 30);
            
            for (const key of keysToProcess) {
                try {
                    const task = await env.KV.get(key.name, { type: 'json' });
                    if (task) {
                        // 获取最新的几条日志
                        let recentLogs = [];
                        try {
                            const logger = new TaskLogger(task.id || key.name.replace('task:', ''), env);
                            recentLogs = await logger.getRecentLogs(3); // 只获取最近3条日志
                        } catch (logError) {
                            console.warn(`获取任务 ${key.name} 日志失败:`, logError);
                            // 日志获取失败不影响任务显示
                        }
                        
                        tasks.push({
                            id: task.id || key.name.replace('task:', ''),
                            filename: task.filename || '未知文件',
                            status: task.status || 'UNKNOWN',
                            progress: task.progress || 0,
                            createdAt: task.createdAt || new Date().toISOString(),
                            email: task.email || '未知用户',
                            processingType: task.processingType,
                            error: task.error,
                            lastProgressUpdate: task.lastProgressUpdate,
                            recentLogs
                        });
                    }
                } catch (taskError) {
                    console.warn(`处理任务 ${key.name} 时出错:`, taskError);
                    // 继续处理其他任务
                }
            }
            
            // 按创建时间排序
            tasks.sort((a, b) => {
                const dateA = new Date(a.createdAt || 0);
                const dateB = new Date(b.createdAt || 0);
                return dateB - dateA;
            });
            
            return Response.json({
                success: true,
                tasks,
                totalTasks: tasks.length,
                timestamp: new Date().toISOString(),
                admin: adminVerification.admin.email
            });
        }

    } catch (error) {
        console.error('获取任务概览失败:', error);
        
        return Response.json({ 
            success: false,
            error: error.message,
            type: 'GET_REQUEST_ERROR',
            timestamp: new Date().toISOString()
        }, { status: 500 });
    }
} 