/**
 * 时间格式化工具函数
 * 确保所有时间都显示为东八区（中国）时间
 */

/**
 * 格式化日期时间为中国时区
 * @param {string} isoString - ISO格式的时间字符串
 * @returns {string} 格式化后的时间字符串
 */
export function formatDateTime(isoString) {
    if (!isoString) return '未知时间';
    
    try {
        const date = new Date(isoString);
        if (isNaN(date.getTime())) {
            return '无效时间';
        }
        
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            timeZone: 'Asia/Shanghai'  // 明确指定东八区时间
        });
    } catch (error) {
        console.error('时间格式化失败:', error, isoString);
        return '格式化失败';
    }
}

/**
 * 格式化日期时间为中国时区（不包含秒）
 * @param {string} isoString - ISO格式的时间字符串
 * @returns {string} 格式化后的时间字符串
 */
export function formatDateTimeShort(isoString) {
    if (!isoString) return '未知时间';
    
    try {
        const date = new Date(isoString);
        if (isNaN(date.getTime())) {
            return '无效时间';
        }
        
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            timeZone: 'Asia/Shanghai'  // 明确指定东八区时间
        });
    } catch (error) {
        console.error('时间格式化失败:', error, isoString);
        return '格式化失败';
    }
}

/**
 * 获取当前中国时间的ISO字符串
 * @returns {string} ISO格式的时间字符串
 */
export function getCurrentChinaTime() {
    return new Date().toISOString();
} 