/**
 * 日志记录配置
 * 优化版本：减少KV存储使用，只记录关键信息
 */

export const LoggingConfig = {
    // KV存储配置
    storage: {
        maxLogs: 200,           // 减少最大日志数量（原来1000）
        retentionDays: 7,       // 日志保留天数
        compactOldLogs: true    // 压缩旧日志
    },

    // 日志级别配置
    levels: {
        // 记录到KV存储的级别
        kvLevels: [
            'CRITICAL',    // 关键错误
            'ERROR',       // 错误
            'WARN',        // 警告  
            'INFO',        // 重要信息
            'PROGRESS',    // 进度更新
            'STEP_START',  // 步骤开始
            'STEP_COMPLETE', // 步骤完成
            'STEP_FAILED'  // 步骤失败
        ],
        
        // 只在控制台输出的级别
        consoleOnlyLevels: [
            'DEBUG',       // 调试信息
            'TRACE'        // 跟踪信息
        ]
    },

    // 特定场景的日志简化配置
    simplification: {
        // TTS API调用
        tts: {
            recordSuccessDetails: false,  // 不记录成功调用的详细信息
            recordRetryOnly: true,        // 只记录重试和失败
            maxErrorDetails: 200         // 错误信息最大字符数
        },
        
        // 段落处理
        chunk: {
            recordIndividualSuccess: false, // 不记录每个段落的成功信息
            recordFailuresOnly: true,       // 只记录失败的段落
            batchSummaryOnly: true         // 只记录批次汇总
        },
        
        // 文件处理
        file: {
            recordBasicMetadata: true,     // 记录基本元数据
            skipDetailedContent: true      // 跳过详细内容信息
        }
    },

    // 关键业务指标（始终记录）
    criticalMetrics: [
        'taskStart',
        'taskComplete', 
        'taskFailed',
        'ttsApiFailure',
        'fileProcessingError',
        'storageError',
        'chapterComplete',
        'overallProgress'
    ],

    // 性能监控（简化记录）
    performance: {
        recordProcessingTime: true,
        recordMemoryUsage: false,       // 不记录内存使用
        recordDetailedTiming: false     // 不记录详细时间
    }
};

/**
 * 检查是否应该记录到KV存储
 */
export function shouldLogToKV(level) {
    return LoggingConfig.levels.kvLevels.includes(level);
}

/**
 * 检查是否只在控制台输出
 */
export function isConsoleOnly(level) {
    return LoggingConfig.levels.consoleOnlyLevels.includes(level);
}

/**
 * 获取简化的错误信息
 */
export function simplifyError(error, maxLength = 200) {
    if (!error) return '';
    
    const errorMsg = typeof error === 'string' ? error : error.message || error.toString();
    return errorMsg.length > maxLength ? errorMsg.substring(0, maxLength) + '...' : errorMsg;
}

/**
 * 清理和简化日志数据
 */
export function cleanLogData(data, level, context = '') {
    if (!data || typeof data !== 'object') {
        return data;
    }

    const cleaned = {};
    
    // 基于上下文和级别决定保留哪些字段
    switch (context) {
        case 'tts':
            if (data.error) cleaned.error = simplifyError(data.error);
            if (data.status) cleaned.status = data.status;
            if (data.retryAttempt && data.retryAttempt > 1) cleaned.retryAttempt = data.retryAttempt;
            if (data.textLength) cleaned.textLength = data.textLength;
            break;
            
        case 'chapter':
            if (data.audioSize) cleaned.audioSize = data.audioSize;
            if (data.duration) cleaned.duration = data.duration;
            if (data.error) cleaned.error = simplifyError(data.error);
            break;
            
        case 'progress':
            if (data.progress !== undefined) cleaned.progress = data.progress;
            break;
            
        default:
            // 默认情况：保留最基本的信息
            if (data.progress !== undefined) cleaned.progress = data.progress;
            if (data.error) cleaned.error = simplifyError(data.error);
            if (data.status) cleaned.status = data.status;
            if (data.duration) cleaned.duration = data.duration;
    }

    return Object.keys(cleaned).length > 0 ? cleaned : undefined;
} 