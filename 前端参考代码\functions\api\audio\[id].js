// 音频文件服务 API - 支持 Range 请求
import { R2StorageManager } from '../../utils/r2-storage.js';

export async function onRequestGet({ request, env, params }) {
    try {
        const audioId = params.id;
        
        if (!audioId) {
            return new Response('音频ID不能为空', { status: 400 });
        }

        // 从音频ID中提取任务ID（去掉.mp3后缀）
        const taskId = audioId.replace('.mp3', '');
        
        // 获取任务信息来确定用户邮箱
        const taskKey = `task:${taskId}`;
        const task = await env.KV.get(taskKey, { type: 'json' });
        
        if (!task) {
            return new Response('任务不存在', { status: 404 });
        }

        // 初始化R2存储管理器
        const r2Storage = new R2StorageManager(env.R2);
        
        // 解析 Range 请求头
        const rangeHeader = request.headers.get('Range');
        let audioObject;
        let responseHeaders = {
            'Content-Type': 'audio/mpeg',
            'Accept-Ranges': 'bytes',
            'Cache-Control': 'public, max-age=31536000',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Range, Content-Range, Content-Length',
            'Access-Control-Expose-Headers': 'Content-Range, Content-Length, Accept-Ranges'
        };

        // 从R2获取音频文件
        if (task.audioPath) {
            // 使用新的R2路径
            if (rangeHeader) {
                // 处理 Range 请求
                const ranges = parseRangeHeader(rangeHeader);
                if (ranges && ranges.length === 1) {
                    const range = ranges[0];
                    
                    // 首先获取文件信息以确定总大小
                    const headObject = await r2Storage.headFile(task.audioPath);
                    if (!headObject) {
                        return new Response('音频文件不存在', { status: 404 });
                    }
                    
                    const fileSize = headObject.size;
                    const start = range.start !== undefined ? range.start : 0;
                    const end = range.end !== undefined ? range.end : fileSize - 1;
                    
                    // 验证范围
                    if (start >= fileSize || end >= fileSize || start > end) {
                        return new Response('Range Not Satisfiable', {
                            status: 416,
                            headers: {
                                ...responseHeaders,
                                'Content-Range': `bytes */${fileSize}`
                            }
                        });
                    }
                    
                    // 使用 R2 的 range 参数获取部分内容
                    audioObject = await r2Storage.getFileRange(task.audioPath, start, end);
                    
                    if (!audioObject) {
                        return new Response('音频文件不存在', { status: 404 });
                    }
                    
                    // 设置 206 Partial Content 响应头
                    responseHeaders = {
                        ...responseHeaders,
                        'Content-Range': `bytes ${start}-${end}/${fileSize}`,
                        'Content-Length': (end - start + 1).toString(),
                        'Last-Modified': headObject.uploaded.toUTCString(),
                        'ETag': headObject.etag
                    };
                    
                    return new Response(audioObject.body, {
                        status: 206,
                        headers: responseHeaders
                    });
                }
            }
            
            // 普通请求（非 Range）
            audioObject = await r2Storage.getFile(task.audioPath);
        } else {
            // 兼容旧的存储方式
            if (rangeHeader) {
                // 对于旧的存储方式，先获取完整文件再处理 Range
                audioObject = await r2Storage.getAudioFile(task.email, taskId);
                
                if (!audioObject) {
                    // 兼容旧的KV存储
                    const audioKey = `audio:${taskId}`;
                    const audioData = await env.KV.get(audioKey, { type: 'arrayBuffer' });
                    
                    if (!audioData) {
                        return new Response('音频文件不存在', { status: 404 });
                    }
                    
                    // 处理 KV 数据的 Range 请求
                    return handleRangeRequest(audioData, rangeHeader, responseHeaders);
                }
                
                // 处理 R2 数据的 Range 请求
                const arrayBuffer = await audioObject.body.arrayBuffer();
                return handleRangeRequest(arrayBuffer, rangeHeader, responseHeaders);
            }
            
            // 普通请求
            audioObject = await r2Storage.getAudioFile(task.email, taskId);
            
            if (!audioObject) {
                // 兼容旧的KV存储
                const audioKey = `audio:${taskId}`;
                const audioData = await env.KV.get(audioKey, { type: 'arrayBuffer' });
                
                if (!audioData) {
                    return new Response('音频文件不存在', { status: 404 });
                }

                return new Response(audioData, {
                    headers: {
                        ...responseHeaders,
                        'Content-Length': audioData.byteLength.toString(),
                        'Content-Disposition': `attachment; filename="${audioId}"`
                    }
                });
            }
        }
        
        if (!audioObject) {
            return new Response('音频文件不存在', { status: 404 });
        }

        // 返回完整的音频文件
        responseHeaders = {
            ...responseHeaders,
            'Content-Length': audioObject.size.toString(),
            'Last-Modified': audioObject.uploaded.toUTCString(),
            'ETag': audioObject.etag,
            'Content-Disposition': `attachment; filename="${audioId}"`
        };

        return new Response(audioObject.body, {
            headers: responseHeaders
        });

    } catch (error) {
        console.error('获取音频文件错误:', error);
        return new Response('服务器内部错误', { status: 500 });
    }
}

// 解析 Range 请求头
function parseRangeHeader(rangeHeader) {
    if (!rangeHeader || !rangeHeader.startsWith('bytes=')) {
        return null;
    }
    
    const ranges = [];
    const rangeSpecs = rangeHeader.substring(6).split(',');
    
    for (const rangeSpec of rangeSpecs) {
        const range = rangeSpec.trim();
        const dashIndex = range.indexOf('-');
        
        if (dashIndex === -1) continue;
        
        const startStr = range.substring(0, dashIndex);
        const endStr = range.substring(dashIndex + 1);
        
        const start = startStr ? parseInt(startStr, 10) : undefined;
        const end = endStr ? parseInt(endStr, 10) : undefined;
        
        if ((start !== undefined && isNaN(start)) || (end !== undefined && isNaN(end))) {
            continue;
        }
        
        ranges.push({ start, end });
    }
    
    return ranges.length > 0 ? ranges : null;
}

// 处理内存中数据的 Range 请求
function handleRangeRequest(arrayBuffer, rangeHeader, baseHeaders) {
    const ranges = parseRangeHeader(rangeHeader);
    
    if (!ranges || ranges.length !== 1) {
        // 不支持多范围请求，返回完整内容
        return new Response(arrayBuffer, {
            headers: {
                ...baseHeaders,
                'Content-Length': arrayBuffer.byteLength.toString()
            }
        });
    }
    
    const range = ranges[0];
    const fileSize = arrayBuffer.byteLength;
    const start = range.start !== undefined ? range.start : 0;
    const end = range.end !== undefined ? range.end : fileSize - 1;
    
    // 验证范围
    if (start >= fileSize || end >= fileSize || start > end) {
        return new Response('Range Not Satisfiable', {
            status: 416,
            headers: {
                ...baseHeaders,
                'Content-Range': `bytes */${fileSize}`
            }
        });
    }
    
    // 提取指定范围的数据
    const partialData = arrayBuffer.slice(start, end + 1);
    
    return new Response(partialData, {
        status: 206,
        headers: {
            ...baseHeaders,
            'Content-Range': `bytes ${start}-${end}/${fileSize}`,
            'Content-Length': partialData.byteLength.toString()
        }
    });
} 