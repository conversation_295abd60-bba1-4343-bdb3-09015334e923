// 极简任务状态查询 - 结合KV基础状态和HF API实时进度
import { MinimalTaskManager } from '../utils/minimal-task-manager.js';
import { HuggingFaceApiClient } from '../utils/huggingface-api-client.js';

export async function onRequestGet({ request, env }) {
    try {
        const url = new URL(request.url);
        const taskId = url.pathname.split('/').pop();
        
        if (!taskId) {
            return Response.json({ error: '任务ID不能为空' }, { status: 400 });
        }

        const taskManager = new MinimalTaskManager(taskId, env);
        const basicStatus = await taskManager.getStatus();
        
        if (!basicStatus) {
            return Response.json({ error: '任务不存在' }, { status: 404 });
        }

        // 基础响应
        const response = {
            taskId: taskId,
            status: basicStatus.status,
            createdAt: basicStatus.createdAt,
            metadata: basicStatus.metadata
        };

        // 如果任务已完成或失败，直接返回KV中的状态
        if (basicStatus.status === 'CONVERSION_SUCCESS') {
            response.completedAt = basicStatus.completedAt;
            response.audioPath = basicStatus.audioPath;
            response.progress = 100;
            response.message = '转换完成';
            
            return Response.json({
                success: true,
                task: response
            });
        }

        if (basicStatus.status === 'UPLOAD_FAILED' || basicStatus.status === 'CONVERSION_FAILED') {
            response.failedAt = basicStatus.failedAt;
            response.error = basicStatus.error;
            response.errorStage = basicStatus.errorStage;
            response.progress = 0;
            response.message = '任务失败';
            
            return Response.json({
                success: true,
                task: response
            });
        }

        // 如果任务是上传成功状态，尝试获取HF的实时进度
        if (basicStatus.status === 'UPLOAD_SUCCESS') {
            try {
                const hfClient = new HuggingFaceApiClient();
                
                // 尝试从R2获取HF任务ID（如果有的话）
                const hfTaskId = await getHfTaskId(taskId, env);
                
                if (hfTaskId) {
                    console.log(`获取HF任务 ${hfTaskId} 的实时状态`);
                    const hfStatus = await hfClient.getTaskStatus(hfTaskId);
                    
                    response.progress = hfStatus.progress;
                    response.currentStage = hfStatus.currentStage;
                    response.hfStatus = hfStatus.status;
                    response.processingDetails = hfStatus.processingDetails;
                    response.message = `转换中: ${hfStatus.currentStage}`;
                    
                    // 如果HF任务完成，检查是否需要更新KV状态
                    if (hfStatus.status === 'completed') {
                        response.message = '转换即将完成，正在保存结果...';
                    } else if (hfStatus.status === 'failed') {
                        response.message = '转换失败';
                        response.error = hfStatus.currentStage;
                    }
                } else {
                    // 没有HF任务ID，可能还在准备阶段
                    response.progress = 5;
                    response.currentStage = '准备转换';
                    response.message = '正在准备转换任务...';
                }
            } catch (error) {
                console.error('获取HF状态失败:', error);
                // 如果HF API调用失败，返回基础状态
                response.progress = 10;
                response.currentStage = '转换中';
                response.message = '正在转换，详细进度暂时无法获取';
                response.note = 'HF API暂时不可用，实际转换可能正在进行';
            }
        }

        return Response.json({
            success: true,
            task: response
        });

    } catch (error) {
        console.error('获取任务状态失败:', error);
        return Response.json({ error: error.message }, { status: 500 });
    }
}

// 尝试获取HF任务ID
async function getHfTaskId(taskId, env) {
    try {
        // 可能的存储位置：
        // 1. 临时存储在KV中
        const hfTaskKey = `hf_task:${taskId}`;
        const hfTaskId = await env.KV.get(hfTaskKey);
        
        if (hfTaskId) {
            return hfTaskId;
        }

        // 2. 从R2中的元数据文件获取
        if (env.R2) {
            try {
                const metadataPath = `audio-metadata/${taskId}/metadata.json`;
                const metadataObj = await env.R2.get(metadataPath);
                
                if (metadataObj) {
                    const metadata = await metadataObj.json();
                    return metadata.hfTaskId;
                }
            } catch (r2Error) {
                console.log('从R2获取HF任务ID失败:', r2Error);
            }
        }

        return null;
    } catch (error) {
        console.error('获取HF任务ID失败:', error);
        return null;
    }
}

// 批量获取任务状态
export async function onRequestPost({ request, env }) {
    try {
        const { taskIds } = await request.json();
        
        if (!Array.isArray(taskIds) || taskIds.length === 0) {
            return Response.json({ error: '任务ID列表不能为空' }, { status: 400 });
        }

        if (taskIds.length > 50) {
            return Response.json({ error: '单次查询任务数量不能超过50个' }, { status: 400 });
        }

        const results = [];
        
        for (const taskId of taskIds) {
            try {
                const taskManager = new MinimalTaskManager(taskId, env);
                const status = await taskManager.getStatus();
                
                if (status) {
                    results.push({
                        taskId: taskId,
                        status: status.status,
                        createdAt: status.createdAt,
                        completedAt: status.completedAt,
                        failedAt: status.failedAt,
                        metadata: status.metadata
                    });
                } else {
                    results.push({
                        taskId: taskId,
                        status: 'NOT_FOUND',
                        error: '任务不存在'
                    });
                }
            } catch (error) {
                results.push({
                    taskId: taskId,
                    status: 'ERROR',
                    error: error.message
                });
            }
        }

        return Response.json({
            success: true,
            tasks: results,
            total: results.length
        });

    } catch (error) {
        console.error('批量获取任务状态失败:', error);
        return Response.json({ error: error.message }, { status: 500 });
    }
}

// 获取任务的实时进度（仅用于前端轮询）
export async function getRealTimeProgress(taskId, env) {
    try {
        const taskManager = new MinimalTaskManager(taskId, env);
        const basicStatus = await taskManager.getStatus();
        
        if (!basicStatus || basicStatus.status !== 'UPLOAD_SUCCESS') {
            return {
                progress: basicStatus?.status === 'CONVERSION_SUCCESS' ? 100 : 0,
                stage: basicStatus?.status || '未知',
                message: '请通过基础状态API获取详细信息'
            };
        }

        // 获取HF实时进度
        const hfTaskId = await getHfTaskId(taskId, env);
        if (!hfTaskId) {
            return {
                progress: 5,
                stage: '准备中',
                message: '正在准备转换任务'
            };
        }

        const hfClient = new HuggingFaceApiClient();
        const hfStatus = await hfClient.getTaskStatus(hfTaskId);
        
        return {
            progress: hfStatus.progress,
            stage: hfStatus.currentStage,
            message: `转换进度: ${hfStatus.progress}%`,
            hfStatus: hfStatus.status,
            estimatedTime: hfStatus.processingDetails?.estimated_time
        };

    } catch (error) {
        console.error('获取实时进度失败:', error);
        return {
            progress: 0,
            stage: '状态未知',
            message: '无法获取实时进度',
            error: error.message
        };
    }
} 