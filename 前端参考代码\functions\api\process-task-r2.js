/**
 * R2架构的任务处理API
 * 使用R2状态管理器，不依赖KV存储
 */

import { R2TaskStatusManager } from '../utils/r2-task-status-manager.js';
import { R2StorageManager } from '../utils/r2-storage.js';
import { AudiobookApiClient } from '../utils/audiobook-api-client.js';
import { TTSProcessor } from '../utils/tts-processor.js';

export async function onRequestPost({ request, env }) {
    try {
        const { taskId, email, useR2Status = true } = await request.json();
        
        if (!taskId || !email) {
            return Response.json({ error: '任务ID和邮箱不能为空' }, { status: 400 });
        }

        console.log(`🚀 开始处理R2任务: ${taskId}`);
        
        // 异步处理任务，立即返回响应
        processR2Task(taskId, email, env).catch(error => {
            console.error(`❌ R2任务处理失败 ${taskId}:`, {
                error: error.message,
                stack: error.stack,
                taskId: taskId,
                email: email
            });

            // 确保任务状态被标记为失败
            const statusManager = new R2TaskStatusManager(env.R2, env);
            statusManager.markAsFailed(taskId, email, error, {
                failurePoint: 'ASYNC_PROCESSING',
                timestamp: new Date().toISOString()
            }).catch(updateError => {
                console.error(`更新失败状态时出错 ${taskId}:`, updateError);
            });
        });

        return Response.json({
            success: true,
            message: 'R2任务处理已开始',
            taskId: taskId,
            architecture: 'R2'
        });

    } catch (error) {
        console.error('启动R2任务处理失败:', error);
        return Response.json({ 
            error: '启动任务处理失败',
            details: error.message 
        }, { status: 500 });
    }
}

/**
 * 处理R2任务的主要逻辑
 */
async function processR2Task(taskId, email, env) {
    const startTime = Date.now();
    const statusManager = new R2TaskStatusManager(env.R2, env);
    const r2Storage = new R2StorageManager(env.R2);

    try {
        // 获取任务状态
        const task = await statusManager.getTaskStatus(taskId, email);
        if (!task) {
            throw new Error('任务不存在');
        }

        if (task.status !== 'PENDING') {
            console.log(`任务 ${taskId} 状态为 ${task.status}，跳过处理`);
            return;
        }

        console.log(`📋 开始处理任务: ${taskId} - ${task.filename}`);

        // 标记任务开始处理
        await statusManager.markAsProcessing(taskId, email);

        // 从R2读取文件内容
        const fileObject = await r2Storage.getFile(task.filePath);
        if (!fileObject) {
            throw new Error('原始文件不存在');
        }

        let fileContent;
        const ext = task.filename.toLowerCase().split('.').pop();
        
        if (ext === 'txt') {
            fileContent = await fileObject.text();
        } else {
            fileContent = await fileObject.arrayBuffer();
        }

        console.log(`📄 文件读取完成: ${task.filename}, 大小: ${fileContent.length || fileContent.byteLength}`);

        // 提取文本内容
        const ttsProcessor = new TTSProcessor();
        const text = await ttsProcessor.extractTextFromContent(fileContent, task.filename);
        
        if (!text || text.trim().length === 0) {
            throw new Error('无法从文件中提取有效文本');
        }

        console.log(`📝 文本提取完成，字数: ${text.length}`);

        // 保存提取的文本到R2
        const textPath = await r2Storage.saveExtractedText(email, taskId, text);
        console.log(`💾 文本已保存到R2: ${textPath}`);

        // 调用外部API进行音频转换
        const apiClient = new AudiobookApiClient();
        
        // 提交转换任务
        const convertOptions = {
            language: 'zh-CN',
            voiceName: 'zh-CN-XiaochenMultilingualNeural',
            speed: 1.0,
            pitch: 0,
            volume: 1.0
        };

        console.log(`🎵 提交音频转换任务...`);
        const taskInfo = await apiClient.submitConvertTaskWithR2Path(task.filePath, convertOptions);
        
        // 更新任务状态，保存外部任务ID
        await statusManager.updateTaskStatus(taskId, email, {
            externalTaskId: taskInfo.task_id,
            currentStage: '音频转换队列中'
        });

        console.log(`⏳ 等待音频转换完成，外部任务ID: ${taskInfo.task_id}`);

        // 等待转换完成
        const result = await apiClient.waitForTaskCompletion(
            taskInfo.task_id,
            null, // 不需要进度回调，因为用户不会实时查看
            1800000, // 30分钟超时
            10000    // 10秒轮询间隔
        );

        if (result.status !== 'completed') {
            throw new Error(`音频转换失败: ${result.status}`);
        }

        console.log(`✅ 音频转换完成`);

        // 下载并保存音频文件
        const audioUrl = result.output_files[0];
        const audioResponse = await fetch(audioUrl);
        
        if (!audioResponse.ok) {
            throw new Error(`下载音频文件失败: ${audioResponse.status}`);
        }

        const audioBuffer = await audioResponse.arrayBuffer();
        const audioPath = await r2Storage.saveAudioFile(email, taskId, audioBuffer, {
            originalUrl: audioUrl,
            duration: result.duration || '未知',
            wordCount: text.length
        });

        console.log(`🎵 音频文件已保存: ${audioPath}, 大小: ${audioBuffer.byteLength}`);

        // 扣除用户积分
        await chargeUserPoints(email, 5, taskId, env);

        // 标记任务完成
        const completionData = {
            audioPath: audioPath,
            audioSize: audioBuffer.byteLength,
            metadata: {
                wordCount: text.length,
                duration: result.duration,
                isEnhanced: false,
                totalChapters: 1,
                processingDuration: Date.now() - startTime
            },
            urls: {
                audioUrl: `/api/audio/${taskId}.mp3`,
                downloadUrl: `/api/audio/${taskId}.mp3`
            }
        };

        await statusManager.markAsCompleted(taskId, email, completionData);

        const totalDuration = Date.now() - startTime;
        console.log(`🎉 R2任务处理完成: ${taskId}, 耗时: ${totalDuration}ms`);

    } catch (error) {
        console.error(`❌ R2任务处理失败 ${taskId}:`, error);
        
        // 标记任务失败
        try {
            await statusManager.markAsFailed(taskId, email, error, {
                processingDuration: Date.now() - startTime
            });
        } catch (updateError) {
            console.error('更新失败状态时出错:', updateError);
        }
        
        throw error;
    }
}

/**
 * 扣除用户积分
 */
async function chargeUserPoints(email, points, taskId, env) {
    try {
        const userKey = `user:${email}`;
        const userData = await env.KV.get(userKey, { type: 'json' });
        
        if (!userData) {
            throw new Error('用户不存在');
        }

        if (userData.points < points) {
            throw new Error(`积分不足，当前积分：${userData.points}，需要：${points}`);
        }

        // 扣除积分
        userData.points -= points;
        userData.lastPointsUpdate = new Date().toISOString();
        
        await env.KV.put(userKey, JSON.stringify(userData));

        // 创建积分记录
        const recordKey = `points_record:${email}:${Date.now()}_${taskId}`;
        const pointsRecord = {
            email: email,
            taskId: taskId,
            type: 'CONSUME',
            amount: -points,
            description: '音频转换服务 (R2架构)',
            timestamp: new Date().toISOString(),
            balanceAfter: userData.points
        };

        await env.KV.put(recordKey, JSON.stringify(pointsRecord));

        console.log(`💰 用户 ${email} 积分扣除成功: -${points}，余额: ${userData.points}`);

    } catch (error) {
        console.error('扣除用户积分失败:', error);
        throw new Error(`积分扣除失败: ${error.message}`);
    }
}

/**
 * 批量处理待处理的R2任务
 */
export async function onRequestGet({ env }) {
    try {
        console.log('🔄 开始批量处理R2任务...');
        
        // 这里需要实现扫描所有用户的待处理任务
        // 由于R2没有全局扫描能力，这个功能需要另外的实现方式
        // 比如维护一个待处理任务队列
        
        return Response.json({
            success: true,
            message: 'R2批量处理功能待实现',
            architecture: 'R2'
        });

    } catch (error) {
        console.error('R2批量处理失败:', error);
        return Response.json({ 
            error: 'R2批量处理失败',
            details: error.message 
        }, { status: 500 });
    }
}
