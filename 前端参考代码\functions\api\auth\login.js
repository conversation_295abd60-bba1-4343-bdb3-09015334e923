// 用户登录 API
export async function onRequestPost({ request, env }) {
    try {
        const { email, password } = await request.json();
        
        if (!email || !password) {
            return Response.json({ error: '邮箱和密码不能为空' }, { status: 400 });
        }

        // 从 KV 获取用户数据
        const userKey = `user:${email}`;
        const userData = await env.KV.get(userKey, { type: 'json' });
        
        if (!userData) {
            return Response.json({ error: '用户不存在' }, { status: 404 });
        }

        // 验证密码（实际项目中应该使用加密哈希）
        if (userData.password !== password) {
            return Response.json({ error: '密码错误' }, { status: 401 });
        }

        // 更新最后登录时间
        userData.lastLogin = new Date().toISOString();
        await env.KV.put(userKey, JSON.stringify(userData));

        // 返回用户信息（不包含密码）
        const { password: _, ...userInfo } = userData;
        
        return Response.json({
            success: true,
            user: userInfo
        });

    } catch (error) {
        console.error('登录错误:', error);
        return Response.json({ error: '服务器内部错误' }, { status: 500 });
    }
} 