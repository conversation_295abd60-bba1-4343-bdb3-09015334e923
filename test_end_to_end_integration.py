#!/usr/bin/env python3
"""
端到端集成测试：测试从音频转换到任务处理的完整流程
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ.setdefault('PYTHONPATH', str(project_root))

async def test_end_to_end_integration():
    """端到端集成测试"""
    try:
        # 导入必要的模块
        from app.services.audio_conversion_service import audio_conversion_service
        from app.services.task_processing_service import task_processing_service
        from app.core.config import settings
        from app.models.task import Task, TaskStatus, TaskType
        from app.core.database import get_db
        
        print("开始端到端集成测试...")
        
        # 创建测试文本文件
        test_text = """
        第一章：人工智能的发展
        
        人工智能技术正在快速发展，为我们的生活带来了许多便利。从智能手机到自动驾驶汽车，AI技术已经渗透到我们生活的方方面面。这项技术的发展不仅改变了我们的工作方式，也改变了我们的生活方式。
        
        第二章：语音合成技术
        
        语音合成技术作为人工智能的重要分支，能够将文本转换为自然流畅的语音。这项技术在有声书制作、语音助手、无障碍服务等领域有着广泛的应用。通过不断的技术创新和优化，现代语音合成系统已经能够产生接近人类自然语音的效果。
        
        第三章：音频处理技术
        
        音频处理技术包括音频录制、编辑、压缩、格式转换等多个方面。在有声书制作过程中，音频处理技术起着至关重要的作用。通过先进的音频处理算法，我们可以提高音频质量，减少文件大小，并确保在不同设备上的兼容性。
        """
        
        # 创建临时文本文件
        temp_dir = Path(settings.temp_dir)
        temp_dir.mkdir(parents=True, exist_ok=True)
        
        test_file = temp_dir / "test_integration.txt"
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_text)
        
        print(f"创建测试文件: {test_file}")
        print(f"文本长度: {len(test_text)} 字符")
        
        # 步骤1：测试音频转换服务
        print("\n=== 步骤1：测试音频转换服务 ===")
        
        task_id = 997
        user_id = 1
        file_path = str(test_file)
        file_type = "txt"
        
        conversion_result = None
        async for update in audio_conversion_service.convert_file_to_audio(
            task_id=task_id,
            user_id=user_id,
            file_path=file_path,
            file_type=file_type
        ):
            print(f"音频转换进度: {update}")
            
            if update.get("type") == "complete":
                conversion_result = update.get("result")
                print("✅ 音频转换完成")
                break
            elif update.get("type") == "error":
                print(f"❌ 音频转换失败: {update.get('message')}")
                return False
        
        if not conversion_result:
            print("❌ 音频转换未返回结果")
            return False
        
        print(f"转换结果: {conversion_result}")
        
        # 步骤2：测试任务处理服务的结果处理逻辑
        print("\n=== 步骤2：测试任务处理服务结果处理 ===")
        
        # 模拟任务处理服务中的逻辑
        try:
            # 适配新的音频合并结果结构
            if "audio_file" in conversion_result:
                # 新的合并音频格式：单个音频文件
                audio_file = conversion_result["audio_file"]
                task_audio_files = [audio_file]  # 转换为列表格式以保持兼容性
                task_playlist_url = None  # 不再使用播放列表
                print("✅ 正确处理新的音频合并格式")
            else:
                # 兼容旧格式（如果存在）
                task_audio_files = conversion_result.get("audio_files", [])
                task_playlist_url = conversion_result.get("playlist_url")
                print("使用旧格式兼容模式")
            
            task_total_duration = conversion_result["total_duration"]
            
            print(f"任务结果处理:")
            print(f"  audio_files: {len(task_audio_files)} 个文件")
            print(f"  playlist_url: {task_playlist_url}")
            print(f"  total_duration: {task_total_duration}")
            
            # 验证结果
            if len(task_audio_files) == 1:
                audio_file = task_audio_files[0]
                print(f"✅ 成功处理合并音频文件:")
                print(f"  文件名: {audio_file.get('filename')}")
                print(f"  URL: {audio_file.get('url')}")
                print(f"  时长: {audio_file.get('duration')}秒")
                print(f"  合并的分段数: {audio_file.get('segments_merged')}")
                
                if task_playlist_url is None:
                    print("✅ 正确设置playlist_url为None")
                else:
                    print("⚠️  playlist_url应该为None")
                
            else:
                print("❌ 音频文件处理失败")
                return False
                
        except KeyError as e:
            print(f"❌ 结果处理失败，缺少字段: {str(e)}")
            return False
        
        # 步骤3：验证数据结构兼容性
        print("\n=== 步骤3：验证数据结构兼容性 ===")
        
        # 检查返回的数据结构是否包含所有必要字段
        required_fields = ["total_duration", "word_count", "title"]
        missing_fields = []
        
        for field in required_fields:
            if field not in conversion_result:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"❌ 缺少必要字段: {missing_fields}")
            return False
        else:
            print("✅ 所有必要字段都存在")
        
        # 检查audio_file字段的完整性
        if "audio_file" in conversion_result:
            audio_file = conversion_result["audio_file"]
            audio_required_fields = ["filename", "url", "duration", "size"]
            audio_missing_fields = []
            
            for field in audio_required_fields:
                if field not in audio_file:
                    audio_missing_fields.append(field)
            
            if audio_missing_fields:
                print(f"❌ 音频文件信息缺少字段: {audio_missing_fields}")
                return False
            else:
                print("✅ 音频文件信息完整")
        
        # 清理测试文件
        try:
            test_file.unlink()
            print(f"\n清理测试文件: {test_file}")
        except:
            pass
        
        print("\n=== 集成测试完成 ===")
        print("✅ 音频转换服务正常工作")
        print("✅ 任务处理服务正确处理新格式")
        print("✅ 数据结构兼容性良好")
        print("✅ 端到端流程测试通过")
        
        return True
        
    except Exception as e:
        print(f"集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("端到端集成测试")
    print("=" * 60)
    
    # 运行测试
    success = asyncio.run(test_end_to_end_integration())
    
    if success:
        print("\n🎉 端到端集成测试成功！")
        print("音频合并功能与任务处理服务完美集成。")
    else:
        print("\n❌ 端到端集成测试失败！")
        sys.exit(1)
