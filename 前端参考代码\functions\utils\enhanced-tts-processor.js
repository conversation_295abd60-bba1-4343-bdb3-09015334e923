import { TTSProcessor } from './tts-processor.js';

/**
 * 增强版TTS处理器 - 支持章节分割和渐进式加载
 */
export class EnhancedTTSProcessor extends TTSProcessor {
    constructor() {
        super();
        this.maxChapterDuration = 30 * 60; // 30分钟（秒）
        this.estimatedWordsPerSecond = 5; // 估算每秒5个字符
    }

    /**
     * 从EPUB内容中提取章节结构 (使用新的正确解析方法)
     * @param {ArrayBuffer} epubContent - EPUB文件内容
     * @returns {Array} 章节数组
     */
    async extractChaptersFromEpub(epubContent) {
        try {
            console.log('使用新的EPUB解析器提取章节结构...');
            
            // 使用父类的新方法提取章节
            const chapters = await this.extractEpubChapters(epubContent);
            
            if (!chapters || chapters.length === 0) {
                throw new Error('未能从EPUB文件中提取到任何章节内容');
            }
            
            console.log(`从EPUB中提取了 ${chapters.length} 个章节`);
            chapters.forEach((chapter, index) => {
                console.log(`章节 ${index + 1}: ${chapter.title} (${chapter.content.length}字符, 预计${Math.ceil(chapter.estimatedDuration/60)}分钟)`);
            });

            return chapters;

        } catch (error) {
            console.error('EPUB章节提取失败:', error);
            // 降级处理：将整个内容作为一个章节
            try {
                const fullText = await this.extractTextFromEpub(epubContent);
                return [{
                    title: '全文',
                    content: fullText,
                    estimatedDuration: Math.ceil(fullText.length / this.estimatedWordsPerSecond),
                    chapterNum: 1,
                    filename: 'chapter_001.html'
                }];
            } catch (fallbackError) {
                console.error('降级处理也失败:', fallbackError);
                throw new Error(`EPUB处理完全失败: ${error.message}`);
            }
        }
    }

    /**
     * 清理EPUB内容
     */
    cleanEpubContent(content) {
        let text = content;
        
        // 去除EPUB特有的标记和元数据
        text = text.replace(/<\?xml[^>]*\?>/gi, '');
        text = text.replace(/<!DOCTYPE[^>]*>/gi, '');
        text = text.replace(/<meta[^>]*>/gi, '');
        text = text.replace(/<link[^>]*>/gi, '');
        text = text.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '');
        text = text.replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '');
        
        // 保留段落结构但去除其他HTML标签
        text = text.replace(/<\/p>/gi, '\n\n');
        text = text.replace(/<\/div>/gi, '\n');
        text = text.replace(/<br[^>]*>/gi, '\n');
        text = text.replace(/<\/h[1-6]>/gi, '\n\n');
        
        // 去除所有HTML标签
        text = text.replace(/<[^>]*>/g, ' ');
        
        // 解码HTML实体
        text = text.replace(/&nbsp;/g, ' ')
                  .replace(/&lt;/g, '<')
                  .replace(/&gt;/g, '>')
                  .replace(/&amp;/g, '&')
                  .replace(/&quot;/g, '"')
                  .replace(/&#39;/g, "'")
                  .replace(/&hellip;/g, '...')
                  .replace(/&mdash;/g, '—')
                  .replace(/&ndash;/g, '–');
        
        return this.cleanText(text);
    }

    /**
     * 转义正则表达式特殊字符
     */
    escapeRegex(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    /**
     * 智能分割章节（按时长限制）
     * @param {Array} chapters - 原始章节数组
     * @returns {Array} 分割后的章节数组
     */
    splitChaptersByDuration(chapters) {
        const splitChapters = [];
        
        for (const chapter of chapters) {
            if (chapter.estimatedDuration <= this.maxChapterDuration) {
                // 章节时长在限制内，直接使用
                splitChapters.push(chapter);
            } else {
                // 章节过长，需要分割
                const targetParts = Math.ceil(chapter.estimatedDuration / this.maxChapterDuration);
                const partLength = Math.ceil(chapter.content.length / targetParts);
                
                console.log(`章节"${chapter.title}"过长(${Math.ceil(chapter.estimatedDuration/60)}分钟)，分割为${targetParts}部分`);
                
                const parts = this.splitTextIntoParts(chapter.content, partLength);
                
                parts.forEach((part, index) => {
                    splitChapters.push({
                        title: `${chapter.title} (第${index + 1}部分)`,
                        content: part,
                        estimatedDuration: Math.ceil(part.length / this.estimatedWordsPerSecond),
                        isPartial: true,
                        originalTitle: chapter.title,
                        partIndex: index + 1,
                        totalParts: parts.length
                    });
                });
            }
        }
        
        // 移除章节数量限制，允许处理更多章节
        console.log(`章节分割完成，共${splitChapters.length}个音频段落`);
        return splitChapters;
    }

    /**
     * 将文本分割为指定长度的部分
     */
    splitTextIntoParts(text, targetLength) {
        const parts = [];
        const sentences = text.split(/[。！？\n]+/).filter(s => s.trim().length > 0);
        
        let currentPart = '';
        
        for (const sentence of sentences) {
            const trimmedSentence = sentence.trim();
            if (!trimmedSentence) continue;
            
            if (currentPart.length + trimmedSentence.length + 1 <= targetLength) {
                currentPart += (currentPart ? '。' : '') + trimmedSentence;
            } else {
                if (currentPart) {
                    parts.push(currentPart + '。');
                }
                currentPart = trimmedSentence;
            }
        }
        
        if (currentPart) {
            parts.push(currentPart + '。');
        }
        
        return parts;
    }

    /**
     * 处理EPUB文件并生成分章节音频 (使用新的解析方法)
     * @param {ArrayBuffer} epubContent - EPUB内容
     * @param {function} progressCallback - 进度回调
     * @param {Object} logger - 日志记录器
     * @returns {Promise<Array>} 音频章节数组
     */
    async processEpubWithChapters(epubContent, progressCallback, logger = null) {
        // 参数验证
        if (!epubContent || !(epubContent instanceof ArrayBuffer)) {
            throw new Error('EPUB内容必须是有效的ArrayBuffer');
        }
        
        if (epubContent.byteLength === 0) {
            throw new Error('EPUB文件内容为空');
        }
        
        if (!progressCallback || typeof progressCallback !== 'function') {
            throw new Error('进度回调函数无效');
        }
        
        console.log(`开始处理EPUB文件，文件大小: ${epubContent.byteLength} 字节`);
        
        if (logger) await logger.stepStart('EPUB_PROCESSING', 'EPUB文件章节处理');
        console.log('开始处理EPUB文件，提取章节结构...');
        
        try {
            // 1. 提取章节 (使用新的解析方法)
            if (logger) await logger.stepStart('CHAPTER_EXTRACTION', '提取EPUB章节结构');
            const chapters = await this.extractChaptersFromEpub(epubContent);
            
            // 验证提取的章节数据
            if (!chapters || !Array.isArray(chapters) || chapters.length === 0) {
                throw new Error('未能从EPUB文件中提取到有效的章节内容');
            }
            
            // 验证每个章节的数据完整性
            for (let i = 0; i < chapters.length; i++) {
                const chapter = chapters[i];
                if (!chapter.content || typeof chapter.content !== 'string' || chapter.content.trim() === '') {
                    console.warn(`章节 ${i + 1} 内容为空，将跳过: ${chapter.title || '未知标题'}`);
                    chapters.splice(i, 1);
                    i--; // 调整索引
                }
            }
            
            if (chapters.length === 0) {
                throw new Error('所有章节内容都为空，无法生成音频');
            }
            
            console.log(`成功提取 ${chapters.length} 个有效章节`);
            
            if (logger) await logger.stepComplete('CHAPTER_EXTRACTION', null, { 
                chaptersFound: chapters.length,
                chapterTitles: chapters.map(ch => ch.title)
            });
            
            // 2. 按时长分割章节
            if (logger) await logger.stepStart('CHAPTER_SPLITTING', '智能章节分割');
            const splitChapters = this.splitChaptersByDuration(chapters);
            if (logger) await logger.stepComplete('CHAPTER_SPLITTING', null, { 
                originalChapters: chapters.length,
                splitChapters: splitChapters.length,
                splitDetails: splitChapters.map(ch => ({
                    title: ch.title,
                    estimatedDuration: Math.ceil(ch.estimatedDuration / 60) + '分钟',
                    isPartial: ch.isPartial || false
                }))
            });
            
            // 3. 为每个章节生成音频
            if (logger) await logger.stepStart('AUDIO_GENERATION', '生成章节音频');
            const audioChapters = [];
            const totalChapters = splitChapters.length;
            
            for (let i = 0; i < totalChapters; i++) {
                const chapter = splitChapters[i];
                const chapterStartTime = Date.now();
                
                try {
                    if (logger) await logger.info(`开始处理章节 ${i + 1}/${totalChapters}`, {
                        chapterTitle: chapter.title,
                        chapterIndex: i + 1,
                        totalChapters: totalChapters,
                        contentLength: chapter.content.length,
                        estimatedDuration: Math.ceil(chapter.estimatedDuration / 60) + '分钟'
                    });
                    
                    console.log(`处理章节 ${i + 1}/${totalChapters}: ${chapter.title}`);
                    
                    // 为单个章节生成音频
                    const audioBuffer = await this.processLongText(chapter.content, (chapterProgress) => {
                        // 计算总体进度
                        const overallProgress = Math.round(
                            ((i / totalChapters) + (chapterProgress / 100 / totalChapters)) * 80 + 10
                        );
                        if (progressCallback) {
                            progressCallback(overallProgress);
                        }
                        
                        // 记录详细进度
                        if (logger && chapterProgress % 20 === 0) {
                            logger.progress(overallProgress, `章节 ${i + 1}/${totalChapters} 音频生成进度: ${chapterProgress}%`, {
                                chapterTitle: chapter.title,
                                chapterProgress: chapterProgress,
                                overallProgress: overallProgress
                            });
                        }
                    }, logger);
                    
                    const chapterDuration = Date.now() - chapterStartTime;
                    
                    audioChapters.push({
                        title: chapter.title,
                        audioBuffer: audioBuffer,
                        duration: Math.ceil(chapter.content.length / this.estimatedWordsPerSecond),
                        wordCount: chapter.content.length,
                        isPartial: chapter.isPartial || false,
                        originalTitle: chapter.originalTitle,
                        partIndex: chapter.partIndex,
                        totalParts: chapter.totalParts
                    });
                    
                    if (logger) await logger.info(`章节 ${i + 1}/${totalChapters} 处理完成`, {
                        chapterTitle: chapter.title,
                        audioSize: audioBuffer.byteLength,
                        processingTime: Math.ceil(chapterDuration / 1000) + '秒',
                        wordCount: chapter.content.length
                    });
                    
                    console.log(`章节"${chapter.title}"处理完成，音频大小: ${audioBuffer.byteLength} bytes`);
                    
                } catch (error) {
                    if (logger) await logger.stepFailed(`CHAPTER_${i + 1}_PROCESSING`, error, {
                        chapterTitle: chapter.title,
                        chapterIndex: i + 1,
                        contentLength: chapter.content.length
                    });
                    
                    console.error(`处理章节"${chapter.title}"失败:`, error);
                    throw new Error(`章节"${chapter.title}"处理失败: ${error.message}`);
                }
            }
            
            if (logger) await logger.stepComplete('AUDIO_GENERATION', null, {
                totalChapters: audioChapters.length,
                totalAudioSize: audioChapters.reduce((sum, ch) => sum + ch.audioBuffer.byteLength, 0),
                averageChapterSize: Math.round(audioChapters.reduce((sum, ch) => sum + ch.audioBuffer.byteLength, 0) / audioChapters.length)
            });
            
            if (logger) await logger.stepComplete('EPUB_PROCESSING', null, {
                totalChapters: audioChapters.length,
                processingSuccess: true
            });
            
            console.log(`所有章节处理完成，共生成${audioChapters.length}个音频文件`);
            return audioChapters;
            
        } catch (error) {
            if (logger) await logger.stepFailed('EPUB_PROCESSING', error);
            throw error;
        }
    }

    /**
     * 处理普通文本并按时长分割
     * @param {string} text - 文本内容
     * @param {string} title - 文档标题
     * @param {function} progressCallback - 进度回调
     * @returns {Promise<Array>} 音频章节数组
     */
    async processTextWithTimeSplit(text, title = '文档', progressCallback, logger) {
        console.log('开始处理文本，按时长分割...');
        
        const estimatedDuration = Math.ceil(text.length / this.estimatedWordsPerSecond);
        
        if (estimatedDuration <= this.maxChapterDuration) {
            // 文本不长，直接处理
            const audioBuffer = await this.processLongText(text, progressCallback, logger);
            return [{
                title: title,
                audioBuffer: audioBuffer,
                duration: estimatedDuration,
                wordCount: text.length
            }];
        }
        
        // 文本过长，需要分割
        const targetParts = Math.ceil(estimatedDuration / this.maxChapterDuration);
        
        // 移除分割段数限制，允许处理更长的文本
        const partLength = Math.ceil(text.length / targetParts);
        
        console.log(`文本过长(${Math.ceil(estimatedDuration/60)}分钟)，分割为${targetParts}部分`);
        
        const parts = this.splitTextIntoParts(text, partLength);
        const audioChapters = [];
        
        for (let i = 0; i < parts.length; i++) {
            const part = parts[i];
            
            try {
                console.log(`处理第 ${i + 1}/${parts.length} 部分`);
                
                const audioBuffer = await this.processLongText(part, (partProgress) => {
                    const overallProgress = Math.round(
                        ((i / parts.length) + (partProgress / 100 / parts.length)) * 80 + 10
                    );
                    if (progressCallback) {
                        progressCallback(overallProgress);
                    }
                }, logger);
                
                audioChapters.push({
                    title: `${title} (第${i + 1}部分)`,
                    audioBuffer: audioBuffer,
                    duration: Math.ceil(part.length / this.estimatedWordsPerSecond),
                    wordCount: part.length,
                    isPartial: true,
                    originalTitle: title,
                    partIndex: i + 1,
                    totalParts: parts.length
                });
                
            } catch (error) {
                console.error(`处理第${i + 1}部分失败:`, error);
                throw new Error(`第${i + 1}部分处理失败: ${error.message}`);
            }
        }
        
        return audioChapters;
    }
} 