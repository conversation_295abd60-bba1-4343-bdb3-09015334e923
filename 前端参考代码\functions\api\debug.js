// 调试端点 - 检查环境变量和绑定
export async function onRequestGet({ request, env, params }) {
    try {
        const debugInfo = {
            timestamp: new Date().toISOString(),
            env_keys: Object.keys(env),
            has_r2: !!env.R2,
            has_storage: !!env.STORAGE,
            has_kv: !!env.KV,
            url: request.url,
            method: request.method,
            headers: Object.fromEntries(request.headers.entries())
        };

        // 测试 R2 连接
        if (env.R2) {
            try {
                const testList = await env.R2.list({ limit: 1 });
                debugInfo.r2_connection = 'OK';
                debugInfo.r2_objects_count = testList.objects.length;
            } catch (error) {
                debugInfo.r2_connection = 'ERROR';
                debugInfo.r2_error = error.message;
            }
        }

        // 测试 KV 连接
        if (env.KV) {
            try {
                const testKey = await env.KV.get('test-key');
                debugInfo.kv_connection = 'OK';
            } catch (error) {
                debugInfo.kv_connection = 'ERROR';
                debugInfo.kv_error = error.message;
            }
        }

        return new Response(JSON.stringify(debugInfo, null, 2), {
            headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            }
        });

    } catch (error) {
        return new Response(JSON.stringify({
            error: error.message,
            stack: error.stack
        }, null, 2), {
            status: 500,
            headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            }
        });
    }
} 