import { UserIdResolver } from '../utils/user-id-resolver.js';

// 验证任务完成状态 - 通过检查音频文件是否存在
export async function onRequestPost({ request, env }) {
    try {
        const { email, taskIds } = await request.json();
        
        if (!email) {
            return Response.json({ error: '邮箱参数不能为空' }, { status: 400 });
        }

        // 验证用户是否存在
        const userKey = `user:${email}`;
        const userData = await env.KV.get(userKey, { type: 'json' });
        
        if (!userData) {
            return Response.json({ error: '用户不存在' }, { status: 404 });
        }

        const verificationResults = [];
        let totalUpdated = 0;

        // 如果指定了特定的任务ID，只验证这些任务
        if (taskIds && Array.isArray(taskIds)) {
            for (const taskId of taskIds) {
                const result = await verifyAndUpdateSingleTask(env, email, taskId);
                if (result.updated) totalUpdated++;
                verificationResults.push(result);
            }
        } else {
            // 否则，验证用户的所有未完成任务
            const tasksToVerify = await findTasksToVerify(env, email);
            
            for (const task of tasksToVerify) {
                const result = await verifyAndUpdateSingleTask(env, email, task.id);
                if (result.updated) totalUpdated++;
                verificationResults.push(result);
            }
        }

        return Response.json({
            success: true,
            totalUpdated,
            verificationResults,
            message: `验证完成，更新了 ${totalUpdated} 个任务的状态`
        });

    } catch (error) {
        console.error('验证任务完成状态失败:', error);
        return Response.json({ error: '验证失败: ' + error.message }, { status: 500 });
    }
}

// 查找需要验证的任务（处理中或等待中的任务）
async function findTasksToVerify(env, email) {
    const tasks = [];
    
    try {
        // 获取用户数据
        const userKey = `user:${email}`;
        const userData = await env.KV.get(userKey, { type: 'json' });
        
        let taskIds = [];
        
        if (userData?.taskIds && Array.isArray(userData.taskIds)) {
            taskIds = userData.taskIds;
        } else {
            // 后备方案：扫描所有任务
            const taskList = await env.KV.list({ prefix: 'task:', limit: 1000 });
            taskIds = taskList.keys
                .map(key => key.name.replace('task:', ''))
                .filter(id => id);
        }

        const now = new Date();

        for (const taskId of taskIds) {
            try {
                const task = await env.KV.get(`task:${taskId}`, { type: 'json' });
                
                // 验证条件：
                // 1. 属于该用户
                // 2. 状态为 PROCESSING 或 PENDING
                // 3. 或者是状态为 PROCESSING 但超过10分钟没有进度更新的任务（可能卡住了）
                if (task && task.email === email) {
                    const shouldVerify = 
                        (task.status === 'PROCESSING' || task.status === 'PENDING') ||
                        (task.status === 'PROCESSING' && isTaskStuckOrCompleted(task, now));
                    
                    if (shouldVerify) {
                        tasks.push({
                            id: task.id,
                            filename: task.filename,
                            title: task.title,
                            status: task.status,
                            progress: task.progress,
                            isEnhanced: task.isEnhanced,
                            totalChapters: task.totalChapters,
                            lastProgressUpdate: task.lastProgressUpdate,
                            createdAt: task.createdAt
                        });
                    }
                }
            } catch (error) {
                console.warn(`检查任务 ${taskId} 失败:`, error);
            }
        }
    } catch (error) {
        console.error('查找需要验证的任务失败:', error);
    }
    
    return tasks;
}

// 判断任务是否卡住或已完成但状态未更新
function isTaskStuckOrCompleted(task, now) {
    // 如果任务没有最后更新时间，使用创建时间
    const lastUpdate = task.lastProgressUpdate ? 
        new Date(task.lastProgressUpdate) : 
        new Date(task.createdAt);
    
    // 计算时间差（毫秒）
    const timeDiff = now - lastUpdate;
    
    // 如果超过10分钟（600000毫秒）没有进度更新，认为可能卡住或已完成
    const isStuck = timeDiff > 10 * 60 * 1000;
    
    if (isStuck) {
        console.log(`🔍 任务 ${task.id} 可能卡住或已完成: 最后更新时间 ${lastUpdate.toLocaleString()}, 距今 ${Math.round(timeDiff / 60000)} 分钟`);
    }
    
    return isStuck;
}

// 验证并更新单个任务
async function verifyAndUpdateSingleTask(env, email, taskId) {
    const result = {
        taskId,
        updated: false,
        originalStatus: null,
        newStatus: null,
        audioFound: false,
        error: null
    };
    
    try {
        const taskKey = `task:${taskId}`;
        const task = await env.KV.get(taskKey, { type: 'json' });
        
        if (!task) {
            result.error = '任务不存在';
            return result;
        }
        
        if (task.email !== email) {
            result.error = '无权验证此任务';
            return result;
        }
        
        result.originalStatus = task.status;
        
        // 如果任务已经是成功状态，不需要验证
        if (task.status === 'SUCCESS' || task.status === 'COMPLETED') {
            result.newStatus = task.status;
            return result;
        }
        
        // 检查音频文件是否存在
        const audioExists = await checkAudioFileExists(env, email, taskId, task);
        result.audioFound = audioExists;
        
        if (audioExists) {
            // 音频文件存在，更新任务状态为成功
            task.status = 'SUCCESS';
            task.progress = 100;
            task.completedAt = new Date().toISOString();
            
            // 设置音频URL
            if (!task.audioUrl) {
                if (task.isEnhanced || task.totalChapters > 1) {
                    task.audioUrl = `/api/audio/playlist/${taskId}`;
                    task.playlistUrl = `/api/audio/playlist/${taskId}`;
                    task.metadataUrl = `/api/audio/metadata/${taskId}`;
                } else {
                    task.audioUrl = `/api/audio/${taskId}.mp3`;
                }
            }
            
            // 保存更新后的任务
            await env.KV.put(taskKey, JSON.stringify(task));
            
            result.updated = true;
            result.newStatus = 'SUCCESS';
            
            console.log(`✅ 任务 ${taskId} 状态已更新为 SUCCESS（发现音频文件）`);
        } else {
            result.newStatus = task.status;
            console.log(`❌ 任务 ${taskId} 音频文件不存在，保持原状态 ${task.status}`);
        }
        
    } catch (error) {
        result.error = error.message;
        console.error(`验证任务 ${taskId} 失败:`, error);
    }
    
    return result;
}

// 检查音频文件是否存在
async function checkAudioFileExists(env, email, taskId, task) {
    try {
        console.log(`🔍 开始检查音频文件: 邮箱=${email}, 任务ID=${taskId}`);
        
        // 使用新的用户ID解析器
        const resolver = new UserIdResolver(env);
        const userIdResult = await resolver.resolveUserId(email, taskId);
        
        if (!userIdResult.userId) {
            console.error(`❌ 无法解析用户ID: ${email}`, userIdResult);
            return false;
        }
        
        const userId = userIdResult.userId;
        console.log(`📁 使用用户ID: ${userId} (方法: ${userIdResult.method}, 置信度: ${userIdResult.confidence}%)`);
        
        // 检查不同可能的音频文件或播放列表文件路径
        const possiblePaths = [
            `users/${userId}/audios/${taskId}/audio.mp3`,
            `users/${userId}/audios/${taskId}/audio_merged.mp3`,
            `users/${userId}/audios/${taskId}/final_audio.mp3`,
            `users/${userId}/audios/${taskId}/converted_audio.mp3`,
            `users/${userId}/audios/${taskId}/playlist.json`,
            `users/${userId}/audios/${taskId}/playlist.m3u8`,
            `users/${userId}/audios/${taskId}/output.mp3`,
            // 新的audiobooks路径
            `users/${userId}/audiobooks/${taskId}/playlist.json`,
            `users/${userId}/audiobooks/${taskId}/metadata.json`
        ];
        
        // 如果是增强版，还要检查章节文件和播放列表
        if (task.isEnhanced || task.totalChapters > 1) {
            possiblePaths.push(
                `users/${userId}/audios/${taskId}/chapter_1.mp3`,
                `users/${userId}/audios/${taskId}/chapter_001.mp3`,
                `users/${userId}/audios/${taskId}/metadata.json`,
                `users/${userId}/audiobooks/${taskId}/chapter_001.mp3`,
                `users/${userId}/audiobooks/${taskId}/chapter_1.mp3`
            );
        }
        
        console.log(`🔍 检查 ${possiblePaths.length} 个可能的文件路径...`);
        
        // 使用R2存储检查文件是否存在
        for (const path of possiblePaths) {
            try {
                console.log(`🔍 检查路径: ${path}`);
                const object = await env.R2.head(path);
                if (object) {
                    console.log(`✅ 找到音频文件: ${path} (大小: ${object.size} bytes)`);
                    
                    // 记录用户ID解析的详细信息
                    console.log(`🎯 用户ID解析详情:`, {
                        email: email,
                        resolvedUserId: userId,
                        method: userIdResult.method,
                        confidence: userIdResult.confidence,
                        foundPath: path
                    });
                    
                    return true;
                }
            } catch (error) {
                console.log(`❌ 路径不存在: ${path}`);
                continue;
            }
        }
        
        // 如果是增强版，检查章节文件（使用list方法）
        if (task.isEnhanced || task.totalChapters > 1) {
            const chapterPrefixes = [
                `users/${userId}/audios/${taskId}/chapter_`,
                `users/${userId}/audiobooks/${taskId}/chapter_`
            ];
            
            for (const prefix of chapterPrefixes) {
                try {
                    console.log(`🔍 检查章节文件目录: ${prefix}`);
                    const chapterList = await env.R2.list({
                        prefix: prefix
                    });
                    
                    if (chapterList.objects && chapterList.objects.length > 0) {
                        console.log(`✅ 找到章节文件: ${chapterList.objects.length} 个文件`);
                        chapterList.objects.forEach(obj => {
                            console.log(`  - ${obj.key} (${obj.size} bytes)`);
                        });
                        
                        // 记录用户ID解析的详细信息
                        console.log(`🎯 用户ID解析详情:`, {
                            email: email,
                            resolvedUserId: userId,
                            method: userIdResult.method,
                            confidence: userIdResult.confidence,
                            foundChapters: chapterList.objects.length
                        });
                        
                        return true;
                    }
                } catch (error) {
                    console.warn(`检查章节文件时出错: ${error.message}`);
                    continue;
                }
            }
        }
        
        // 最后尝试：使用list方法检查目录是否有任何音频文件
        const directoryPrefixes = [
            `users/${userId}/audios/${taskId}/`,
            `users/${userId}/audiobooks/${taskId}/`
        ];
        
        for (const prefix of directoryPrefixes) {
            try {
                console.log(`🔍 最后尝试：列出目录内容 ${prefix}`);
                const audioList = await env.R2.list({
                    prefix: prefix,
                    limit: 20
                });
                
                console.log(`📂 目录中共有 ${audioList.objects ? audioList.objects.length : 0} 个文件`);
                
                if (audioList.objects && audioList.objects.length > 0) {
                    // 打印所有文件
                    audioList.objects.forEach(obj => {
                        console.log(`  📄 ${obj.key} (${obj.size} bytes, 修改时间: ${obj.lastModified})`);
                    });
                    
                    // 查找任何音频文件
                    const audioFiles = audioList.objects.filter(obj => 
                        obj.key.endsWith('.mp3') || 
                        obj.key.endsWith('.wav') || 
                        obj.key.endsWith('.m4a') ||
                        obj.key.endsWith('.aac')
                    );
                    
                    if (audioFiles.length > 0) {
                        console.log(`✅ 找到 ${audioFiles.length} 个音频文件`);
                        
                        // 记录用户ID解析的详细信息
                        console.log(`🎯 用户ID解析详情:`, {
                            email: email,
                            resolvedUserId: userId,
                            method: userIdResult.method,
                            confidence: userIdResult.confidence,
                            foundAudioFiles: audioFiles.length,
                            totalFiles: audioList.objects.length
                        });
                        
                        return true;
                    }
                    
                    // 查找播放列表文件
                    const playlistFiles = audioList.objects.filter(obj => 
                        obj.key.endsWith('.json') || 
                        obj.key.endsWith('.m3u8')
                    );
                    
                    if (playlistFiles.length > 0) {
                        console.log(`✅ 找到 ${playlistFiles.length} 个播放列表文件`);
                        
                        // 记录用户ID解析的详细信息
                        console.log(`🎯 用户ID解析详情:`, {
                            email: email,
                            resolvedUserId: userId,
                            method: userIdResult.method,
                            confidence: userIdResult.confidence,
                            foundPlaylistFiles: playlistFiles.length,
                            totalFiles: audioList.objects.length
                        });
                        
                        return true;
                    }
                }
            } catch (error) {
                console.warn(`检查目录 ${prefix} 时出错: ${error.message}`);
                continue;
            }
        }
        
        console.log(`❌ 未找到任务 ${taskId} 的音频文件 (用户ID: ${userId}, 解析方法: ${userIdResult.method})`);
        console.log(`🔍 用户ID解析详情:`, userIdResult);
        
        return false;

    } catch (error) {
        console.error('检查音频文件失败:', error);
        return false;
    }
}

// GET方法：查询需要验证的任务
export async function onRequestGet({ request, env }) {
    try {
        const url = new URL(request.url);
        const email = url.searchParams.get('email');
        
        if (!email) {
            return Response.json({ error: '邮箱参数不能为空' }, { status: 400 });
        }

        // 验证用户是否存在
        const userKey = `user:${email}`;
        const userData = await env.KV.get(userKey, { type: 'json' });
        
        if (!userData) {
            return Response.json({ error: '用户不存在' }, { status: 404 });
        }

        const tasksToVerify = await findTasksToVerify(env, email);

        return Response.json({
            success: true,
            tasksToVerify,
            total: tasksToVerify.length,
            message: tasksToVerify.length > 0 ? 
                `发现 ${tasksToVerify.length} 个任务需要验证` : 
                '没有发现需要验证的任务'
        });

    } catch (error) {
        console.error('查询需要验证的任务失败:', error);
        return Response.json({ error: '查询失败: ' + error.message }, { status: 500 });
    }
} 