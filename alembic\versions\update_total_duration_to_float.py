"""Update total_duration to float

Revision ID: update_total_duration_to_float
Revises: 
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'update_total_duration_to_float'
down_revision = None
depends_on = None


def upgrade():
    """Upgrade database schema"""
    # 更新tasks表的total_duration字段类型从Integer改为Float
    op.alter_column('tasks', 'total_duration',
                    existing_type=sa.Integer(),
                    type_=sa.Float(),
                    existing_nullable=True)


def downgrade():
    """Downgrade database schema"""
    # 回滚：将total_duration字段类型从Float改回Integer
    # 注意：这可能会导致精度丢失
    op.alter_column('tasks', 'total_duration',
                    existing_type=sa.Float(),
                    type_=sa.Integer(),
                    existing_nullable=True)
