# 电子书转音频单体应用

这是一个将电子书文件转换为音频的单体应用，支持Docker一键部署。

## 功能特性

- 📚 支持多种格式：EPUB、TXT、PDF、Word文档
- 🎵 高质量音频生成：支持多种语音和语言
- 🔐 本地用户认证：无需依赖外部服务
- 💾 本地文件存储：所有数据存储在本地
- 🐳 Docker部署：一键启动，开箱即用
- 📱 响应式界面：支持桌面和移动设备

## 技术架构

- **后端**：FastAPI + SQLite + 本地文件存储
- **前端**：HTML5 + CSS3 + JavaScript（集成在后端）
- **认证**：JWT Token认证
- **存储**：本地文件系统 + SQLite数据库
- **容器化**：Docker + Docker Compose

## 快速开始

### 一键启动（推荐）

**Windows用户：**
```cmd
# 方式1：使用PowerShell（推荐）
powershell -ExecutionPolicy Bypass -File start.ps1

# 方式2：使用批处理文件
start_windows.bat

# 方式3：如果上述方式有问题，使用简化版本
start.bat
```

**Linux/macOS用户：**
```bash
chmod +x start.sh
./start.sh
```

### 手动Docker部署

```bash
# 克隆项目
git clone <repository-url>
cd audiobooks_monolith

# 复制环境配置
cp .env.example .env
# 编辑 .env 文件配置TTS服务

# 启动服务
docker-compose up -d

# 访问应用
# 浏览器打开 http://localhost:8000
```

### 手动Python部署

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
cd c:\Zhou\coding\audiobooks_docker\
# Windows: venv\Scripts\activate
# Linux/macOS: source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 复制环境配置
cp .env.example .env
# 编辑 .env 文件

# 启动服务
python main.py

# 访问应用
# 浏览器打开 http://localhost:8000
```

## 目录结构

```
audiobooks_monolith/
├── app/                    # 应用核心代码
│   ├── api/               # API路由
│   ├── core/              # 核心配置和工具
│   ├── models/            # 数据模型
│   ├── services/          # 业务逻辑服务
│   ├── static/            # 静态文件（CSS、JS、图片）
│   ├── templates/         # HTML模板
│   └── main.py           # 应用入口
├── data/                  # 数据目录
│   ├── database/          # SQLite数据库
│   ├── uploads/           # 用户上传文件
│   └── audio/             # 生成的音频文件
├── docker/                # Docker配置
├── docs/                  # 文档
├── requirements.txt       # Python依赖
├── Dockerfile            # Docker镜像构建
├── docker-compose.yml    # Docker编排
└── README.md             # 项目说明
```

## 环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `APP_HOST` | 应用监听地址 | `0.0.0.0` |
| `APP_PORT` | 应用监听端口 | `8000` |
| `SECRET_KEY` | JWT密钥 | 自动生成 |
| `DATABASE_URL` | 数据库连接 | `sqlite:///data/database/app.db` |
| `UPLOAD_DIR` | 上传目录 | `data/uploads` |
| `AUDIO_DIR` | 音频目录 | `data/audio` |
| `TTS_API_URL` | TTS服务地址 | 外部API |
| `TTS_API_KEY` | TTS API密钥 | 需要配置 |

## 使用说明

1. **注册账户**：首次使用需要注册用户账户
2. **上传文件**：支持拖拽上传或点击选择文件
3. **选择设置**：配置语音、语速等参数
4. **开始转换**：系统自动处理并生成音频
5. **在线播放**：转换完成后可在线播放或下载

## 开发指南

详细的开发文档请参考 [docs/](docs/) 目录。

## 许可证

MIT License
