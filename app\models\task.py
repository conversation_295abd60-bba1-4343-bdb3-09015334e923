"""
任务数据模型
"""

from sqlalchemy import Column, String, Integer, Float, Text, JSON, ForeignKey, Enum as SQLEnum
from sqlalchemy.orm import relationship
from pydantic import BaseModel, Field
from typing import Optional, Dict, List, Any
from datetime import datetime
from enum import Enum

from .base import BaseDBModel, BaseResponse


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"          # 等待中
    QUEUED = "queued"           # 已入队
    PROCESSING = "processing"    # 处理中
    COMPLETED = "completed"      # 已完成
    FAILED = "failed"           # 失败
    CANCELLED = "cancelled"      # 已取消


class TaskType(str, Enum):
    """任务类型枚举"""
    EPUB = "epub"
    TXT = "txt"
    PDF = "pdf"
    DOCX = "docx"
    URL = "url"


class Task(BaseDBModel):
    """任务数据库模型"""
    __tablename__ = "tasks"

    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    title = Column(String(255), nullable=False)
    task_type = Column(SQLEnum(TaskType), nullable=False)
    status = Column(SQLEnum(TaskStatus), default=TaskStatus.PENDING, nullable=False)
    
    # 文件信息
    original_filename = Column(String(255), nullable=True)
    file_size = Column(Integer, nullable=True)
    file_url = Column(Text, nullable=True)
    
    # 处理配置
    processing_mode = Column(String(50), default="standard", nullable=False)  # standard, enhanced
    voice_settings = Column(JSON, nullable=True)
    
    # 结果信息
    audio_files = Column(JSON, nullable=True)  # 生成的音频文件列表
    playlist_url = Column(Text, nullable=True)
    total_duration = Column(Float, nullable=True)  # 总时长（秒，支持小数）
    
    # 进度和错误信息
    progress = Column(Integer, default=0, nullable=False)  # 进度百分比
    error_message = Column(Text, nullable=True)
    
    # 关联用户
    user = relationship("User", back_populates="tasks")


class TaskCreate(BaseModel):
    """任务创建模型"""
    title: str = Field(..., min_length=1, max_length=255)
    task_type: TaskType
    processing_mode: str = "standard"
    voice_settings: Optional[Dict[str, Any]] = None


class TaskUpdate(BaseModel):
    """任务更新模型"""
    title: Optional[str] = None
    status: Optional[TaskStatus] = None
    progress: Optional[int] = Field(None, ge=0, le=100)
    error_message: Optional[str] = None
    audio_files: Optional[List[Dict[str, Any]]] = None
    playlist_url: Optional[str] = None
    total_duration: Optional[float] = None


class TaskResponse(BaseResponse):
    """任务响应模型"""
    user_id: int
    title: str
    task_type: TaskType
    status: TaskStatus
    original_filename: Optional[str] = None
    file_size: Optional[int] = None
    file_url: Optional[str] = None
    processing_mode: str
    voice_settings: Optional[Dict[str, Any]] = None
    audio_files: Optional[List[Dict[str, Any]]] = None
    playlist_url: Optional[str] = None
    total_duration: Optional[float] = None
    progress: int
    error_message: Optional[str] = None


class TaskSummary(BaseModel):
    """任务摘要模型"""
    id: int
    title: str
    task_type: TaskType
    status: TaskStatus
    progress: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class TaskDetail(TaskResponse):
    """任务详情模型"""
    pass


class TaskStats(BaseModel):
    """任务统计模型"""
    total_tasks: int
    pending_tasks: int
    processing_tasks: int
    completed_tasks: int
    failed_tasks: int


class ConversionTask:
    """转换任务类 - 用于任务队列处理"""

    def __init__(self):
        self.task_id: str = ""
        self.user_id: str = ""
        self.status: TaskStatus = TaskStatus.PENDING
        self.file_url: str = ""
        self.voice_settings: Optional[Dict[str, Any]] = None
        self.original_filename: str = ""
        self.progress: int = 0
        self.current_stage: str = ""
        self.error_message: Optional[str] = None

        # 时间戳
        self.created_at: Optional[datetime] = None
        self.started_at: Optional[datetime] = None
        self.completed_at: Optional[datetime] = None

        # 队列相关
        self.queue_position: int = 0
        self.retry_count: int = 0
        self.max_retries: int = 3

        # 结果数据
        self.metadata: Optional[Dict[str, Any]] = None
        self.audio_files: Optional[Dict[str, Any]] = None

        # 本地存储相关
        self.local_file_path: Optional[str] = None
        self.original_task_id: Optional[str] = None

        # 初始化创建时间
        if not self.created_at:
            self.created_at = datetime.now()

    def mark_completed(self):
        """标记任务为完成状态"""
        self.status = TaskStatus.COMPLETED
        self.progress = 100
        self.completed_at = datetime.now()
        self.current_stage = "completed"

    def mark_failed(self, error_message: str):
        """标记任务为失败状态"""
        self.status = TaskStatus.FAILED
        self.error_message = error_message
        self.completed_at = datetime.now()
        self.current_stage = "failed"

    def can_retry(self) -> bool:
        """检查是否可以重试"""
        return self.retry_count < self.max_retries

    def increment_retry(self):
        """增加重试次数"""
        self.retry_count += 1
        self.status = TaskStatus.PENDING
        self.error_message = None
        self.progress = 0
        self.current_stage = "retrying"

    def update_progress(self, progress: int, stage: str = ""):
        """更新任务进度"""
        self.progress = max(0, min(100, progress))
        if stage:
            self.current_stage = stage

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "task_id": self.task_id,
            "user_id": self.user_id,
            "status": self.status.value if isinstance(self.status, TaskStatus) else self.status,
            "file_url": self.file_url,
            "voice_settings": self.voice_settings,
            "original_filename": self.original_filename,
            "progress": self.progress,
            "current_stage": self.current_stage,
            "error_message": self.error_message,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "queue_position": self.queue_position,
            "retry_count": self.retry_count,
            "metadata": self.metadata,
            "audio_files": self.audio_files,
            "local_file_path": self.local_file_path,
            "original_task_id": self.original_task_id
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ConversionTask':
        """从字典创建任务对象"""
        task = cls()
        task.task_id = data.get("task_id", "")
        task.user_id = data.get("user_id", "")

        # 处理状态
        status_value = data.get("status", "pending")
        if isinstance(status_value, str):
            task.status = TaskStatus(status_value)
        else:
            task.status = status_value

        task.file_url = data.get("file_url", "")
        task.voice_settings = data.get("voice_settings")
        task.original_filename = data.get("original_filename", "")
        task.progress = data.get("progress", 0)
        task.current_stage = data.get("current_stage", "")
        task.error_message = data.get("error_message")

        # 处理时间戳
        if data.get("created_at"):
            task.created_at = datetime.fromisoformat(data["created_at"])
        if data.get("started_at"):
            task.started_at = datetime.fromisoformat(data["started_at"])
        if data.get("completed_at"):
            task.completed_at = datetime.fromisoformat(data["completed_at"])

        task.queue_position = data.get("queue_position", 0)
        task.retry_count = data.get("retry_count", 0)
        task.metadata = data.get("metadata")
        task.audio_files = data.get("audio_files")
        task.local_file_path = data.get("local_file_path")
        task.original_task_id = data.get("original_task_id")

        return task
