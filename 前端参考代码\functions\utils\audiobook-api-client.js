/**
 * 电子书转音频服务API客户端
 * 用于调用外部的音频转换服务
 */
export class AudiobookApiClient {
    constructor(baseUrl = 'https://fromozu-audiobooks-backend.hf.space') {
        this.baseUrl = baseUrl;
        this.defaultTimeout = 30000; // 30秒默认超时
    }

    /**
     * 获取文件上传的预签名URL
     * @param {string} filename - 文件名
     * @param {number} fileSize - 文件大小
     * @param {string} contentType - 文件MIME类型
     * @returns {Promise<Object>} 上传信息
     */
    async getUploadUrl(filename, fileSize, contentType) {
        const response = await fetch(`${this.baseUrl}/api/files/upload-url`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                filename,
                file_size: fileSize,
                content_type: contentType
            }),
            signal: AbortSignal.timeout(this.defaultTimeout)
        });

        if (!response.ok) {
            const error = await response.json().catch(() => ({ error: response.statusText }));
            throw new Error(`获取上传URL失败: ${error.error || response.statusText}`);
        }

        return await response.json();
    }

    /**
     * 上传文件到预签名URL
     * @param {string} uploadUrl - 预签名上传URL
     * @param {File|ArrayBuffer} fileData - 文件数据
     * @param {string} contentType - 文件MIME类型
     * @param {Function} progressCallback - 进度回调函数
     * @returns {Promise<void>}
     */
    async uploadFile(uploadUrl, fileData, contentType, progressCallback = null) {
        const xhr = new XMLHttpRequest();
        
        return new Promise((resolve, reject) => {
            xhr.upload.addEventListener('progress', (event) => {
                if (event.lengthComputable && progressCallback) {
                    const progress = Math.round((event.loaded / event.total) * 100);
                    progressCallback(progress);
                }
            });

            xhr.addEventListener('load', () => {
                if (xhr.status >= 200 && xhr.status < 300) {
                    resolve();
                } else {
                    reject(new Error(`文件上传失败: ${xhr.status} ${xhr.statusText}`));
                }
            });

            xhr.addEventListener('error', () => {
                reject(new Error('文件上传网络错误'));
            });

            xhr.addEventListener('timeout', () => {
                reject(new Error('文件上传超时'));
            });

            xhr.open('PUT', uploadUrl);
            xhr.setRequestHeader('Content-Type', contentType);
            xhr.timeout = 300000; // 5分钟上传超时
            xhr.send(fileData);
        });
    }

    /**
     * 提交转换任务
     * @param {string} fileUrl - 文件URL
     * @param {Object} options - 转换选项
     * @returns {Promise<Object>} 任务信息
     */
    async submitConvertTask(fileUrl, options = {}) {
        const requestData = {
            file_url: fileUrl,
            voice_settings: {
                language: options.language || 'zh-CN',
                voice_name: options.voiceName || 'zh-CN-XiaochenMultilingualNeural',
                speed: options.speed || 1.0,
                pitch: options.pitch || 0,
                volume: options.volume || 1.0
            },
            output_formats: options.outputFormats || ['mp3'],
            audio_quality: options.audioQuality || 'high',
            split_by_chapter: options.splitByChapter || true,
            max_segment_length: options.maxSegmentLength || 300
        };

        const response = await fetch(`${this.baseUrl}/api/convert`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData),
            signal: AbortSignal.timeout(this.defaultTimeout)
        });

        if (!response.ok) {
            const error = await response.json().catch(() => ({ error: response.statusText }));
            throw new Error(`提交转换任务失败: ${error.error || response.statusText}`);
        }

        return await response.json();
    }

    /**
     * 提交转换任务（使用R2存储路径）
     * @param {string} r2Path - R2存储路径（如：users/75aifw/uploads/1749699246524_glestrzca/original.txt）
     * @param {Object} options - 转换选项
     * @returns {Promise<Object>} 任务信息
     */
    async submitConvertTaskWithR2Path(r2Path, options = {}) {
        const requestData = {
            r2_file_path: r2Path, // 使用R2路径而不是完整URL
            voice_settings: {
                language: options.language || 'zh-CN',
                voice_name: options.voiceName || 'zh-CN-XiaochenMultilingualNeural',
                speed: options.speed || 1.0,
                pitch: options.pitch || 0,
                volume: options.volume || 1.0
            }
        };

        console.log(`🚀 提交转换任务到后端: ${this.baseUrl}/api/convert-r2`);
        console.log(`📁 R2文件路径: ${r2Path}`);
        console.log(`🎵 语音设置:`, requestData.voice_settings);
        console.log(`⏱️ 超时设置: ${this.defaultTimeout}ms`);
        console.log(`🌍 执行环境:`, {
            userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'N/A',
            isWorker: typeof WorkerGlobalScope !== 'undefined',
            hasAbortController: typeof AbortController !== 'undefined',
            hasAbortSignal: typeof AbortSignal !== 'undefined'
        });

        try {
            // 使用兼容性更好的超时控制方式
            const controller = new AbortController();
            const timeoutId = setTimeout(() => {
                console.log(`⏰ 请求超时，正在中止请求...`);
                controller.abort();
            }, this.defaultTimeout);

            console.log(`📤 即将发送HTTP请求...`);
            const startTime = Date.now();

            const response = await fetch(`${this.baseUrl}/api/convert-r2`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(requestData),
                signal: controller.signal
            });

            clearTimeout(timeoutId);
            const requestTime = Date.now() - startTime;
            console.log(`📡 后端响应状态: ${response.status} ${response.statusText} (耗时: ${requestTime}ms)`);

            if (!response.ok) {
                let errorDetails;
                try {
                    errorDetails = await response.json();
                } catch (parseError) {
                    errorDetails = { error: response.statusText };
                }

                console.error(`❌ 后端API调用失败:`, {
                    status: response.status,
                    statusText: response.statusText,
                    errorDetails: errorDetails,
                    requestData: requestData
                });

                throw new Error(`提交R2转换任务失败 (${response.status}): ${errorDetails.error || response.statusText}`);
            }

            const result = await response.json();
            console.log(`✅ 转换任务提交成功:`, result);
            return result;

        } catch (error) {
            // 处理超时错误
            if (error.name === 'AbortError') {
                console.error(`⏰ API调用超时 (${this.defaultTimeout}ms):`, {
                    requestData: requestData,
                    baseUrl: this.baseUrl
                });
                throw new Error(`API调用超时 (${this.defaultTimeout/1000}秒)`);
            }

            console.error(`💥 API调用异常:`, {
                error: error.message,
                errorName: error.name,
                stack: error.stack,
                requestData: requestData,
                baseUrl: this.baseUrl
            });
            throw error;
        }
    }

    /**
     * 查询任务状态
     * @param {string} taskId - 任务ID
     * @returns {Promise<Object>} 任务状态信息
     */
    async getTaskStatus(taskId) {
        const response = await fetch(`${this.baseUrl}/api/queue/status/${taskId}`, {
            method: 'GET',
            signal: AbortSignal.timeout(this.defaultTimeout)
        });

        if (!response.ok) {
            const error = await response.json().catch(() => ({ error: response.statusText }));
            throw new Error(`查询任务状态失败: ${error.error || response.statusText}`);
        }

        return await response.json();
    }

    /**
     * 获取转换结果
     * @param {string} taskId - 任务ID
     * @returns {Promise<Object>} 转换结果
     */
    async getTaskResult(taskId) {
        const response = await fetch(`${this.baseUrl}/api/queue/result/${taskId}`, {
            method: 'GET',
            signal: AbortSignal.timeout(this.defaultTimeout)
        });

        if (!response.ok) {
            const error = await response.json().catch(() => ({ error: response.statusText }));
            throw new Error(`获取转换结果失败: ${error.error || response.statusText}`);
        }

        return await response.json();
    }

    /**
     * 轮询等待任务完成
     * @param {string} taskId - 任务ID
     * @param {Function} progressCallback - 进度回调函数
     * @param {number} maxWaitTime - 最大等待时间（毫秒）
     * @param {number} pollInterval - 轮询间隔（毫秒）
     * @returns {Promise<Object>} 最终任务结果
     */
    async waitForTaskCompletion(taskId, progressCallback = null, maxWaitTime = 1800000, pollInterval = 5000) {
        const startTime = Date.now();
        let lastProgress = -1;
        let consecutiveErrors = 0;
        const maxConsecutiveErrors = 5;
        
        console.log(`开始轮询任务 ${taskId}，最大等待时间: ${maxWaitTime/1000}秒，轮询间隔: ${pollInterval/1000}秒`);
        
        while (Date.now() - startTime < maxWaitTime) {
            try {
                const status = await this.getTaskStatus(taskId);
                consecutiveErrors = 0; // 重置错误计数
                
                // 记录状态变化
                if (status.progress !== lastProgress) {
                    console.log(`任务 ${taskId} 进度更新: ${status.progress}% (状态: ${status.status})`);
                    lastProgress = status.progress;
                }
                
                if (progressCallback && status.progress !== undefined) {
                    progressCallback(status.progress, status.current_stage || status.status);
                }

                switch (status.status) {
                    case 'completed':
                        console.log(`任务 ${taskId} 已完成，获取结果...`);
                        // 任务完成，获取结果
                        return await this.getTaskResult(taskId);
                    
                    case 'failed':
                        console.error(`任务 ${taskId} 失败:`, status.error);
                        throw new Error(`转换任务失败: ${status.error || '未知错误'}`);
                    
                    case 'cancelled':
                        console.warn(`任务 ${taskId} 已被取消`);
                        throw new Error('转换任务已被取消');
                    
                    case 'queued':
                        console.log(`任务 ${taskId} 仍在队列中，继续等待...`);
                        await new Promise(resolve => setTimeout(resolve, pollInterval));
                        break;
                        
                    case 'processing':
                        // 继续等待，但检查是否进度长时间无变化
                        const elapsedTime = Date.now() - startTime;
                        if (elapsedTime > 300000 && status.progress === lastProgress) { // 5分钟无进度变化
                            console.warn(`任务 ${taskId} 进度长时间无变化 (${status.progress}%)，但仍在处理中`);
                        }
                        await new Promise(resolve => setTimeout(resolve, pollInterval));
                        break;
                    
                    default:
                        console.warn(`任务 ${taskId} 未知状态: ${status.status}`);
                        await new Promise(resolve => setTimeout(resolve, pollInterval));
                }
                
            } catch (error) {
                consecutiveErrors++;
                
                if (error.message.includes('转换任务失败') || error.message.includes('已被取消')) {
                    throw error;
                }
                
                // 网络错误等，继续重试
                console.warn(`轮询任务 ${taskId} 状态时出错 (第${consecutiveErrors}次): ${error.message}`);
                
                if (consecutiveErrors >= maxConsecutiveErrors) {
                    console.error(`任务 ${taskId} 连续 ${maxConsecutiveErrors} 次查询失败，停止轮询`);
                    throw new Error(`连续查询失败 ${maxConsecutiveErrors} 次: ${error.message}`);
                }
                
                // 逐渐增加重试间隔
                const retryDelay = Math.min(pollInterval * consecutiveErrors, 30000);
                console.log(`等待 ${retryDelay/1000}秒 后重试...`);
                await new Promise(resolve => setTimeout(resolve, retryDelay));
            }
        }
        
        console.error(`任务 ${taskId} 等待超时 (${maxWaitTime/1000}秒)`);
        throw new Error(`任务等待超时 (${maxWaitTime / 1000}秒)，请稍后手动检查任务状态`);
    }

    /**
     * 取消转换任务
     * @param {string} taskId - 任务ID
     * @returns {Promise<Object>} 取消结果
     */
    async cancelTask(taskId) {
        const response = await fetch(`${this.baseUrl}/api/queue/cancel/${taskId}`, {
            method: 'DELETE',
            signal: AbortSignal.timeout(this.defaultTimeout)
        });

        if (!response.ok) {
            const error = await response.json().catch(() => ({ error: response.statusText }));
            throw new Error(`取消任务失败: ${error.error || response.statusText}`);
        }

        return await response.json();
    }

    /**
     * 获取可用语音列表
     * @returns {Promise<Object>} 语音列表
     */
    async getVoiceList() {
        const response = await fetch(`${this.baseUrl}/api/voices/list`, {
            method: 'GET',
            signal: AbortSignal.timeout(this.defaultTimeout)
        });

        if (!response.ok) {
            const error = await response.json().catch(() => ({ error: response.statusText }));
            throw new Error(`获取语音列表失败: ${error.error || response.statusText}`);
        }

        return await response.json();
    }

    /**
     * 检查服务健康状态
     * @returns {Promise<Object>} 健康状态
     */
    async checkHealth() {
        const response = await fetch(`${this.baseUrl}/health`, {
            method: 'GET',
            signal: AbortSignal.timeout(this.defaultTimeout)
        });

        if (!response.ok) {
            const error = await response.json().catch(() => ({ error: response.statusText }));
            throw new Error(`健康检查失败: ${error.error || response.statusText}`);
        }

        return await response.json();
    }
} 