// 任务状态检查API
export async function onRequestGet({ request, env }) {
    try {
        const url = new URL(request.url);
        const taskId = url.searchParams.get('taskId');
        
        if (!taskId) {
            return Response.json({ error: '缺少任务ID' }, { status: 400 });
        }

        console.log(`任务状态检查: ${taskId}`);
        
        // 从KV存储获取任务信息
        const taskKey = `task:${taskId}`;
        const task = await env.KV.get(taskKey, { type: 'json' });
        
        if (!task) {
            return Response.json({ error: '任务不存在' }, { status: 404 });
        }
        
        // 返回任务详细信息
        const taskInfo = {
            taskId: taskId,
            status: task.status,
            email: task.email,
            isEnhanced: task.isEnhanced || false,
            totalChapters: task.totalChapters || 0,
            createdAt: task.createdAt,
            updatedAt: task.updatedAt,
            title: task.title || '未知标题',
            progress: task.progress || 0,
            error: task.error || null
        };
        
        // 如果是增强版任务，添加额外信息
        if (task.isEnhanced) {
            taskInfo.enhancedInfo = {
                totalDuration: task.totalDuration || 0,
                totalSize: task.totalSize || 0,
                totalWordCount: task.totalWordCount || 0,
                processingTime: task.processingTime || 0
            };
        }
        
        console.log(`任务状态检查完成: ${task.status}, 增强版: ${task.isEnhanced}`);
        
        return Response.json(taskInfo, {
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type',
                'Cache-Control': 'no-cache'
            }
        });

    } catch (error) {
        console.error('任务状态检查失败:', error);
        return Response.json({ 
            error: '服务器内部错误',
            details: error.message 
        }, { status: 500 });
    }
}

// 处理OPTIONS请求（CORS预检）
export async function onRequestOptions() {
    return new Response(null, {
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type',
            'Access-Control-Max-Age': '86400'
        }
    });
} 