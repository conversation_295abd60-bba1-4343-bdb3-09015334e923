#!/usr/bin/env python3
"""
更新数据库schema以支持float类型的total_duration字段
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ.setdefault('PYTHONPATH', str(project_root))

def update_database_schema():
    """更新数据库schema"""
    try:
        from app.core.database import engine, Base
        from app.core.config import get_database_path
        from sqlalchemy import text, inspect
        import shutil
        from datetime import datetime
        
        print("开始更新数据库schema...")
        
        # 1. 备份现有数据库
        db_path = get_database_path()
        if os.path.exists(db_path):
            backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            shutil.copy2(db_path, backup_path)
            print(f"✅ 数据库已备份到: {backup_path}")
        
        # 2. 检查当前表结构
        inspector = inspect(engine)
        if 'tasks' in inspector.get_table_names():
            columns = inspector.get_columns('tasks')
            total_duration_column = None
            for col in columns:
                if col['name'] == 'total_duration':
                    total_duration_column = col
                    break
            
            if total_duration_column:
                print(f"当前total_duration字段类型: {total_duration_column['type']}")
                
                # 检查是否已经是FLOAT类型
                if 'FLOAT' in str(total_duration_column['type']).upper():
                    print("✅ total_duration字段已经是FLOAT类型，无需更新")
                    return True
        
        # 3. 更新表结构
        print("正在更新表结构...")
        
        # 对于SQLite，我们需要重新创建表
        with engine.begin() as conn:
            # 创建临时表来保存数据
            conn.execute(text("""
                CREATE TABLE tasks_backup AS 
                SELECT id, user_id, title, task_type, status, original_filename, 
                       file_size, file_url, processing_mode, voice_settings, 
                       audio_files, playlist_url, 
                       CAST(total_duration AS REAL) as total_duration,
                       progress, error_message, created_at, updated_at
                FROM tasks
            """))
            
            # 删除原表
            conn.execute(text("DROP TABLE tasks"))
            
            # 重新创建表（使用新的模型定义）
            Base.metadata.create_all(bind=engine)
            
            # 恢复数据
            conn.execute(text("""
                INSERT INTO tasks (id, user_id, title, task_type, status, original_filename, 
                                 file_size, file_url, processing_mode, voice_settings, 
                                 audio_files, playlist_url, total_duration, progress, 
                                 error_message, created_at, updated_at)
                SELECT id, user_id, title, task_type, status, original_filename, 
                       file_size, file_url, processing_mode, voice_settings, 
                       audio_files, playlist_url, total_duration, progress, 
                       error_message, created_at, updated_at
                FROM tasks_backup
            """))
            
            # 删除临时表
            conn.execute(text("DROP TABLE tasks_backup"))
        
        print("✅ 数据库schema更新完成")
        
        # 4. 验证更新结果
        inspector = inspect(engine)
        columns = inspector.get_columns('tasks')
        for col in columns:
            if col['name'] == 'total_duration':
                print(f"✅ 更新后total_duration字段类型: {col['type']}")
                break
        
        # 5. 测试数据库连接
        with engine.connect() as conn:
            result = conn.execute(text("SELECT COUNT(*) FROM tasks"))
            count = result.scalar()
            print(f"✅ 数据库连接正常，tasks表有 {count} 条记录")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库schema更新失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_float_duration():
    """测试float类型的duration存储"""
    try:
        from app.core.database import SessionLocal
        from app.models.task import Task, TaskStatus, TaskType
        from sqlalchemy import text
        
        print("\n测试float类型duration存储...")
        
        db = SessionLocal()
        try:
            # 测试插入float类型的duration
            with db.begin():
                db.execute(text("""
                    INSERT INTO tasks (user_id, title, task_type, status, total_duration, created_at, updated_at)
                    VALUES (1, 'Test Float Duration', 'txt', 'completed', 123.456, datetime('now'), datetime('now'))
                """))
            
            # 测试查询
            result = db.execute(text("SELECT total_duration FROM tasks WHERE title = 'Test Float Duration'")).scalar()
            print(f"✅ 成功存储和读取float duration: {result} ({type(result).__name__})")
            
            # 清理测试数据
            db.execute(text("DELETE FROM tasks WHERE title = 'Test Float Duration'"))
            db.commit()
            
            return True
            
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ float duration测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("数据库Schema更新工具")
    print("=" * 50)
    
    # 更新schema
    if update_database_schema():
        # 测试float类型
        if test_float_duration():
            print("\n🎉 数据库schema更新成功！")
            print("现在支持float类型的total_duration字段。")
        else:
            print("\n⚠️  Schema更新成功，但float测试失败。")
    else:
        print("\n❌ 数据库schema更新失败！")
        sys.exit(1)
