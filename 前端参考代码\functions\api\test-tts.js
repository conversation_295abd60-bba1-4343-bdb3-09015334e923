// TTS测试API - 用于调试TTS问题
import { TTSProcessor } from '../utils/tts-processor.js';
import { R2StorageManager } from '../utils/r2-storage.js';

export async function onRequestPost({ request, env }) {
    try {
        const { taskId, testText } = await request.json();
        
        if (testText) {
            // 直接测试提供的文本
            return await testDirectText(testText, env);
        } else if (taskId) {
            // 测试指定任务的文本
            return await testTaskText(taskId, env);
        } else {
            return Response.json({ error: '请提供testText或taskId参数' }, { status: 400 });
        }

    } catch (error) {
        console.error('TTS测试失败:', error);
        return Response.json({ 
            error: error.message, 
            stack: error.stack 
        }, { status: 500 });
    }
}

// 直接测试文本
async function testDirectText(text, env) {
    const ttsProcessor = new TTSProcessor();
    
    console.log('=== TTS直接文本测试 ===');
    console.log(`测试文本: ${text.substring(0, 100)}...`);
    console.log(`文本长度: ${text.length}`);
    
    try {
        // 测试文本清理
        const cleanedText = text.trim()
            .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')
            .replace(/\s+/g, ' ');
        
        console.log(`清理后长度: ${cleanedText.length}`);
        
        // 测试文本分割
        const chunks = ttsProcessor.splitText(cleanedText);
        console.log(`分割成 ${chunks.length} 段`);
        
        // 测试第一段的TTS转换
        if (chunks.length > 0) {
            console.log(`测试第一段: ${chunks[0]}`);
            const audioBuffer = await ttsProcessor.textToSpeech(chunks[0]);
            
            return Response.json({
                success: true,
                message: 'TTS测试成功',
                originalLength: text.length,
                cleanedLength: cleanedText.length,
                chunks: chunks.length,
                firstChunk: chunks[0],
                audioSize: audioBuffer.byteLength
            });
        } else {
            return Response.json({
                error: '文本分割后没有有效内容',
                originalText: text.substring(0, 200),
                cleanedText: cleanedText.substring(0, 200)
            }, { status: 400 });
        }
        
    } catch (error) {
        return Response.json({
            error: `TTS转换失败: ${error.message}`,
            textSample: text.substring(0, 200),
            stack: error.stack
        }, { status: 500 });
    }
}

// 测试任务文本
async function testTaskText(taskId, env) {
    console.log('=== TTS任务文本测试 ===');
    console.log(`任务ID: ${taskId}`);
    
    try {
        // 获取任务信息
        const taskKey = `task:${taskId}`;
        const task = await env.KV.get(taskKey, { type: 'json' });
        
        if (!task) {
            return Response.json({ error: '任务不存在' }, { status: 404 });
        }
        
        console.log(`任务文件: ${task.filename}`);
        console.log(`文件路径: ${task.filePath}`);
        
        // 初始化R2存储管理器
        const r2Storage = new R2StorageManager(env.R2);
        
        // 获取提取的文本
        const extractedTextPath = task.filePath.replace('original.txt', 'extracted_text.txt');
        console.log(`尝试读取提取的文本: ${extractedTextPath}`);
        
        const textObject = await r2Storage.getFile(extractedTextPath);
        if (!textObject) {
            return Response.json({ error: '提取的文本文件不存在' }, { status: 404 });
        }
        
        const extractedText = await textObject.text();
        console.log(`提取的文本长度: ${extractedText.length}`);
        console.log(`文本开头: ${extractedText.substring(0, 200)}`);
        
        // 现在测试这个文本
        return await testDirectText(extractedText, env);
        
    } catch (error) {
        return Response.json({
            error: `测试任务文本失败: ${error.message}`,
            taskId: taskId,
            stack: error.stack
        }, { status: 500 });
    }
} 