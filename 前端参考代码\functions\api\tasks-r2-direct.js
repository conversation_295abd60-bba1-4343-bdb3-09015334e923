// 直接从R2读取任务状态的简化API
import { UserIdResolver } from '../utils/user-id-resolver.js';

export async function onRequestGet({ request, env }) {
    try {
        const url = new URL(request.url);
        const email = url.searchParams.get('email');
        
        if (!email) {
            return Response.json({ error: '邮箱参数不能为空' }, { status: 400 });
        }

        // 验证用户是否存在
        const userKey = `user:${email}`;
        const userData = await env.KV.get(userKey, { type: 'json' });
        
        if (!userData) {
            return Response.json({ error: '用户不存在' }, { status: 404 });
        }

        // 解析用户ID
        const userIdResolver = new UserIdResolver(env.R2, env);
        const userIdResult = await userIdResolver.resolveUserId(email);
        
        if (!userIdResult.userId) {
            console.warn(`无法解析用户ID: ${email}`);
            return Response.json({ 
                success: true, 
                tasks: [], 
                message: '无法解析用户ID' 
            });
        }

        console.log(`📋 直接从R2加载用户任务: ${email} (userId: ${userIdResult.userId})`);

        // 直接从R2读取任务状态文件
        const tasks = await loadTasksFromR2(env.R2, userIdResult.userId);
        
        // 按创建时间排序（最新的在前）
        tasks.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        console.log(`✅ 从R2加载了 ${tasks.length} 个任务`);

        return Response.json({
            success: true,
            tasks: tasks,
            total: tasks.length,
            architecture: 'R2-Direct',
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('直接从R2加载任务失败:', error);
        return Response.json({ 
            error: '加载任务失败: ' + error.message 
        }, { status: 500 });
    }
}

// 直接从R2读取任务状态文件
async function loadTasksFromR2(r2Storage, userId) {
    const tasks = [];
    const prefix = `users/${userId}/tasks/`;
    
    try {
        // 列出用户的所有任务目录
        const objects = await r2Storage.list({ prefix: prefix });
        
        // 提取任务ID（从路径中解析）
        const taskIds = new Set();
        for (const obj of objects.objects) {
            const pathParts = obj.key.split('/');
            if (pathParts.length >= 4 && pathParts[3]) {
                taskIds.add(pathParts[3]); // users/userId/tasks/taskId/...
            }
        }

        console.log(`🔍 发现 ${taskIds.size} 个任务目录`);

        // 读取每个任务的状态文件
        for (const taskId of taskIds) {
            try {
                const statusPath = `users/${userId}/tasks/${taskId}/status.json`;
                const statusObject = await r2Storage.get(statusPath);
                
                if (statusObject) {
                    const statusData = await statusObject.json();
                    
                    // 确保状态数据完整性
                    const task = {
                        id: statusData.id || taskId,
                        email: statusData.email,
                        filename: statusData.filename || '未知文件',
                        title: statusData.filename || '未知文件',
                        type: statusData.type || 'file',
                        status: statusData.status || 'pending',
                        createdAt: statusData.createdAt,
                        updatedAt: statusData.updatedAt,
                        startedAt: statusData.startedAt,
                        completedAt: statusData.completedAt,
                        fileSize: statusData.fileSize,
                        filePath: statusData.filePath,
                        audioPath: statusData.audioPath,
                        audioSize: statusData.audioSize,
                        error: statusData.error,
                        metadata: statusData.metadata || {},
                        urls: statusData.urls || {},
                        // 格式化显示字段
                        createdAtFormatted: formatDateTime(statusData.createdAt),
                        typeDisplay: statusData.type === 'file' ? '文件上传' : 'URL抓取',
                        audioSizeFormatted: statusData.audioSize ? formatFileSize(statusData.audioSize) : null
                    };
                    
                    tasks.push(task);
                } else {
                    console.warn(`任务 ${taskId} 的状态文件不存在`);
                }
            } catch (error) {
                console.error(`读取任务 ${taskId} 状态失败:`, error);
            }
        }

    } catch (error) {
        console.error('列出R2任务目录失败:', error);
    }

    return tasks;
}

// 辅助函数
function formatDateTime(dateString) {
    if (!dateString) return '未知时间';
    
    try {
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (error) {
        return '时间格式错误';
    }
}

function formatFileSize(bytes) {
    if (!bytes || bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
