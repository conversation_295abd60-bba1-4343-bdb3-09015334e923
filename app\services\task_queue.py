"""
任务队列管理服务
实现异步任务队列系统，支持任务状态跟踪、进度更新和错误处理
基于原始参考代码完整迁移，保持所有算法逻辑不变，集成本地存储状态持久化
"""

import asyncio
from typing import Dict, List, Optional
from datetime import datetime, timedelta

from collections import deque
import threading

from app.models.task import ConversionTask, TaskStatus
from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)


class TaskQueue:
    """任务队列管理器"""

    def __init__(self):
        self.tasks: Dict[str, ConversionTask] = {}
        self.queue: deque = deque()
        self.processing_tasks: Dict[str, ConversionTask] = {}
        self.completed_tasks: Dict[str, ConversionTask] = {}
        self.failed_tasks: Dict[str, ConversionTask] = {}

        self.max_concurrent_tasks = settings.max_concurrent_tasks
        self.max_queue_size = settings.queue_max_size
        self.is_running = False
        self.worker_tasks: List[asyncio.Task] = []

        # 统计信息
        self.total_processed = 0
        self.total_failed = 0
        self.average_processing_time = 0

        self._lock = threading.Lock()

        # 状态服务（延迟初始化）
        self._status_service = None

    def _get_status_service(self):
        """获取状态服务实例（延迟初始化）"""
        if self._status_service is None:
            from app.services.status_service import status_service
            self._status_service = status_service
        return self._status_service

    async def start(self):
        """启动队列处理器"""
        if self.is_running:
            return

        self.is_running = True
        logger.info("任务队列启动")

        # 启动工作线程
        for i in range(self.max_concurrent_tasks):
            worker_task = asyncio.create_task(self._worker(f"worker-{i+1}"))
            self.worker_tasks.append(worker_task)

        # 启动清理任务
        cleanup_task = asyncio.create_task(self._cleanup_completed_tasks())
        self.worker_tasks.append(cleanup_task)

    async def stop(self):
        """停止队列处理器"""
        if not self.is_running:
            return

        self.is_running = False
        logger.info("正在停止任务队列...")

        # 取消所有工作任务
        for task in self.worker_tasks:
            task.cancel()

        # 等待所有任务完成
        await asyncio.gather(*self.worker_tasks, return_exceptions=True)
        self.worker_tasks.clear()

        logger.info("任务队列已停止")

    def add_task(self, task: ConversionTask) -> bool:
        """添加任务到队列"""
        with self._lock:
            if len(self.queue) >= self.max_queue_size:
                logger.warning(f"队列已满，无法添加任务: {task.task_id}")
                return False

            # 预先解析用户ID和任务ID（如果是本地路径任务）
            self._preparse_task_info(task)

            # 更新任务状态
            task.status = TaskStatus.QUEUED
            task.queue_position = len(self.queue) + 1

            # 添加到队列和任务字典
            self.queue.append(task)
            self.tasks[task.task_id] = task

            logger.info(
                f"任务已添加到队列: {task.task_id}, 队列位置: {task.queue_position}"
            )

            # 异步保存状态到本地存储 (仅在任务入队时保存一次)
            # 注意：这里不保存状态，因为任务还未开始处理
            # 状态更新将在关键节点进行

            return True

    def _preparse_task_info(self, task: ConversionTask):
        """预先解析任务信息（用户ID和原始任务ID）"""
        try:
            # 获取存储服务实例
            from app.services.storage_service import storage_service

            # 如果是本地路径任务，解析用户ID和任务ID
            if hasattr(task, 'local_file_path') and task.local_file_path:
                user_id, parsed_task_id = storage_service.parse_file_url_info(task.local_file_path)
                logger.info(f"预解析本地路径结果 - 用户ID: {user_id}, 解析的任务ID: {parsed_task_id}")

                # 设置解析的信息
                task.user_id = user_id
                task.original_task_id = parsed_task_id

                # 对于本地路径，设置正确的file_url
                task.file_url = f"local://{task.local_file_path}"
            elif task.file_url and not task.file_url.startswith("local://"):
                # 传统URL解析
                user_id, parsed_task_id = storage_service.parse_file_url_info(task.file_url)
                logger.info(f"预解析URL结果 - 用户ID: {user_id}, 解析的任务ID: {parsed_task_id}")

                task.user_id = user_id
                if parsed_task_id:
                    task.original_task_id = parsed_task_id

        except Exception as e:
            logger.warning(f"预解析任务信息失败: {task.task_id}, {str(e)}")

    async def _save_task_status(self, task: ConversionTask):
        """保存任务状态到本地存储 (兼容性方法)"""
        try:
            if not task.user_id:
                logger.warning(f"任务缺少用户ID，跳过状态保存: {task.task_id}")
                return

            status_service = self._get_status_service()
            success = await status_service.save_status(
                task.user_id,
                task.original_task_id or task.task_id,
                task
            )

            if success:
                logger.debug(f"任务状态保存成功: {task.task_id}")
            else:
                logger.warning(f"任务状态保存失败: {task.task_id}")

        except Exception as e:
            logger.error(f"保存任务状态异常: {task.task_id}, {str(e)}")

    async def _update_task_processing_status(self, task: ConversionTask):
        """
        更新任务开始处理状态 (已优化：不再调用)

        优化说明：
        - 为了减少50%的本地存储写入操作，移除了中间的PROCESSING状态更新
        - 状态流转简化为：PENDING → COMPLETED/FAILED
        - 特别适合快速处理的任务（如小文本文件，1-2秒内完成）
        - 保留此方法以备将来需要时恢复PROCESSING状态更新
        """
        try:
            if not task.user_id:
                logger.warning(f"任务缺少用户ID，跳过状态更新: {task.task_id}")
                return

            status_service = self._get_status_service()
            success = await status_service.update_task_processing(
                task.user_id,
                task.original_task_id or task.task_id
            )

            if success:
                logger.info(f"任务处理状态更新成功: {task.task_id}")
            else:
                logger.warning(f"任务处理状态更新失败: {task.task_id}")

        except Exception as e:
            logger.error(f"更新任务处理状态异常: {task.task_id}, {str(e)}")

    async def _update_task_completed_status(self, task: ConversionTask):
        """更新任务完成状态"""
        try:
            if not task.user_id:
                logger.warning(f"任务缺少用户ID，跳过状态更新: {task.task_id}")
                return

            status_service = self._get_status_service()

            # 准备完成状态数据
            audio_url = None
            metadata = {}
            urls = {}

            if hasattr(task, 'audio_files') and task.audio_files:
                if "mp3" in task.audio_files and "chapters" in task.audio_files["mp3"]:
                    chapters = task.audio_files["mp3"]["chapters"]
                    if chapters:
                        first_chapter = chapters[0]
                        audio_url = first_chapter.get("url")

                if "mp3" in task.audio_files and "playlists" in task.audio_files["mp3"]:
                    playlists = task.audio_files["mp3"]["playlists"]
                    for format_type, playlist_info in playlists.items():
                        if isinstance(playlist_info, dict) and "url" in playlist_info:
                            urls[f"{format_type}Url"] = playlist_info["url"]

            if hasattr(task, 'metadata') and task.metadata:
                if "total_words" in task.metadata:
                    metadata["wordCount"] = task.metadata["total_words"]
                if "total_chapters" in task.metadata:
                    metadata["totalChapters"] = task.metadata["total_chapters"]

            success = await status_service.update_task_completed(
                task.user_id,
                task.original_task_id or task.task_id,
                audio_url=audio_url,
                metadata=metadata,
                urls=urls
            )

            if success:
                logger.info(f"任务完成状态更新成功: {task.task_id}")
            else:
                logger.warning(f"任务完成状态更新失败: {task.task_id}")

        except Exception as e:
            logger.error(f"更新任务完成状态异常: {task.task_id}, {str(e)}")

    async def _update_task_failed_status(self, task: ConversionTask):
        """更新任务失败状态"""
        try:
            if not task.user_id:
                logger.warning(f"任务缺少用户ID，跳过状态更新: {task.task_id}")
                return

            status_service = self._get_status_service()
            success = await status_service.update_task_failed(
                task.user_id,
                task.original_task_id or task.task_id,
                task.error_message or "未知错误"
            )

            if success:
                logger.info(f"任务失败状态更新成功: {task.task_id}")
            else:
                logger.warning(f"任务失败状态更新失败: {task.task_id}")

        except Exception as e:
            logger.error(f"更新任务失败状态异常: {task.task_id}, {str(e)}")

    def get_task(self, task_id: str) -> Optional[ConversionTask]:
        """获取任务信息"""
        # 首先从内存中查找
        task = self.tasks.get(task_id)
        if task:
            return task

        # 如果内存中没有，尝试从本地存储加载
        # 这里需要用户ID，但我们只有任务ID，所以暂时返回None
        # 实际使用中应该通过 get_task_with_user_id 方法
        return None

    async def get_task_with_user_id(self, user_id: str, task_id: str) -> Optional[ConversionTask]:
        """通过用户ID和任务ID获取任务信息"""
        # 首先从内存中查找
        task = self.tasks.get(task_id)
        if task:
            return task

        # 从本地存储加载状态
        try:
            status_service = self._get_status_service()
            status_data = await status_service.load_status(user_id, task_id)

            if not status_data:
                return None

            # 从状态数据重建任务对象
            task = ConversionTask()
            task.task_id = status_data.get("task_id", task_id)
            task.status = TaskStatus(status_data.get("status", "pending"))
            task.progress = status_data.get("progress", 0)
            task.current_stage = status_data.get("current_stage", "")
            task.user_id = user_id
            task.original_task_id = status_data.get("original_task_id")
            task.original_filename = status_data.get("original_filename", "")
            task.error_message = status_data.get("error_message")

            # 解析时间字段
            if status_data.get("created_at"):
                task.created_at = datetime.fromisoformat(status_data["created_at"])
            if status_data.get("started_at"):
                task.started_at = datetime.fromisoformat(status_data["started_at"])
            if status_data.get("completed_at"):
                task.completed_at = datetime.fromisoformat(status_data["completed_at"])

            # 将任务添加到内存缓存
            self.tasks[task_id] = task

            return task

        except Exception as e:
            logger.error(f"从本地存储加载任务状态失败: {user_id}:{task_id}, {str(e)}")
            return None

    def get_queue_status(self) -> Dict:
        """获取队列状态"""
        with self._lock:
            current_task_id = None
            if self.processing_tasks:
                current_task_id = list(self.processing_tasks.keys())[0]

            queue_items = []
            for i, task in enumerate(self.queue):
                queue_items.append(
                    {
                        "task_id": task.task_id,
                        "position": i + 1,
                        "estimated_start": self._estimate_start_time(i),
                        "file_name": task.original_filename,
                    }
                )

            return {
                "queue_size": len(self.queue),
                "current_task": current_task_id,
                "is_running": self.is_running,
                "processing_count": len(self.processing_tasks),
                "completed_count": len(self.completed_tasks),
                "failed_count": len(self.failed_tasks),
                "average_processing_time": f"{self.average_processing_time}分钟",
                "queue_items": queue_items[:10],  # 只返回前10个
            }

    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        with self._lock:
            task = self.tasks.get(task_id)
            if not task:
                return False

            if task.status in [TaskStatus.QUEUED, TaskStatus.PENDING]:
                # 从队列中移除
                try:
                    self.queue.remove(task)
                    task.status = TaskStatus.CANCELLED
                    logger.info(f"任务已取消: {task_id}")
                    return True
                except ValueError:
                    pass

            return False

    async def _worker(self, worker_name: str):
        """工作线程"""
        logger.info(f"工作线程启动: {worker_name}")

        while self.is_running:
            try:
                # 获取下一个任务
                task = await self._get_next_task()
                if not task:
                    await asyncio.sleep(1)
                    continue

                # 处理任务
                await self._process_task(task, worker_name)

            except asyncio.CancelledError:
                logger.info(f"工作线程被取消: {worker_name}")
                break
            except Exception as e:
                logger.error(f"工作线程异常: {worker_name}, {str(e)}")
                await asyncio.sleep(5)

        logger.info(f"工作线程停止: {worker_name}")

    async def _get_next_task(self) -> Optional[ConversionTask]:
        """获取下一个待处理任务"""
        with self._lock:
            if not self.queue:
                return None

            if len(self.processing_tasks) >= self.max_concurrent_tasks:
                return None

            task = self.queue.popleft()
            task.status = TaskStatus.PROCESSING
            task.started_at = datetime.now()
            task.queue_position = 0

            self.processing_tasks[task.task_id] = task

            # 更新其他任务的队列位置
            for i, queued_task in enumerate(self.queue):
                queued_task.queue_position = i + 1

            # 优化：移除PROCESSING状态更新，直接处理任务
            # 减少50%的本地存储写入操作，状态流转简化为：PENDING → COMPLETED/FAILED
            # asyncio.create_task(self._update_task_processing_status(task))

            return task

    async def _process_task(self, task: ConversionTask, worker_name: str):
        """处理单个任务"""
        start_time = datetime.now()

        try:
            logger.info(f"{worker_name} 开始处理任务: {task.task_id}")

            # 导入处理服务
            from app.services.conversion_service import conversion_service

            # 执行转换
            result = await conversion_service.process_conversion_task(task)

            if result:
                task.mark_completed()
                with self._lock:
                    self.completed_tasks[task.task_id] = task
                    if task.task_id in self.processing_tasks:
                        del self.processing_tasks[task.task_id]

                # 保存完成状态到本地存储
                await self._update_task_completed_status(task)

                # 更新统计信息
                processing_time = (datetime.now() - start_time).total_seconds() / 60
                self._update_statistics(processing_time, success=True)

                logger.info(
                    f"{worker_name} 任务处理完成: {task.task_id}, 耗时: {processing_time:.1f}分钟"
                )
            else:
                raise Exception("转换处理失败")

        except Exception as e:
            error_msg = str(e)
            logger.error(
                f"{worker_name} 任务处理失败: {task.task_id}, 错误: {error_msg}"
            )

            task.mark_failed(error_msg)

            with self._lock:
                self.failed_tasks[task.task_id] = task
                if task.task_id in self.processing_tasks:
                    del self.processing_tasks[task.task_id]

            # 保存失败状态到本地存储
            await self._update_task_failed_status(task)

            # 检查是否可以重试
            if task.can_retry():
                logger.info(
                    f"任务将重试: {task.task_id}, 重试次数: {task.retry_count + 1}"
                )
                task.increment_retry()
                self.add_task(task)

            # 更新统计信息
            processing_time = (datetime.now() - start_time).total_seconds() / 60
            self._update_statistics(processing_time, success=False)

    def _update_statistics(self, processing_time: float, success: bool):
        """更新统计信息"""
        with self._lock:
            if success:
                self.total_processed += 1
            else:
                self.total_failed += 1

            # 更新平均处理时间
            total_tasks = self.total_processed + self.total_failed
            if total_tasks > 0:
                self.average_processing_time = (
                    self.average_processing_time * (total_tasks - 1) + processing_time
                ) / total_tasks

    def _estimate_start_time(self, queue_position: int) -> str:
        """估算任务开始时间"""
        if self.average_processing_time <= 0:
            return "未知"

        estimated_minutes = queue_position * self.average_processing_time
        estimated_time = datetime.now() + timedelta(minutes=estimated_minutes)

        return estimated_time.strftime("%H:%M")

    async def _cleanup_completed_tasks(self):
        """定期清理已完成的任务"""
        while self.is_running:
            try:
                await asyncio.sleep(3600)  # 每小时清理一次

                cutoff_time = datetime.now() - timedelta(hours=24)

                with self._lock:
                    # 清理24小时前的已完成任务
                    completed_to_remove = []
                    for task_id, task in self.completed_tasks.items():
                        if task.completed_at and task.completed_at < cutoff_time:
                            completed_to_remove.append(task_id)

                    for task_id in completed_to_remove:
                        del self.completed_tasks[task_id]
                        if task_id in self.tasks:
                            del self.tasks[task_id]

                    # 清理24小时前的失败任务
                    failed_to_remove = []
                    for task_id, task in self.failed_tasks.items():
                        if task.completed_at and task.completed_at < cutoff_time:
                            failed_to_remove.append(task_id)

                    for task_id in failed_to_remove:
                        del self.failed_tasks[task_id]
                        if task_id in self.tasks:
                            del self.tasks[task_id]

                    if completed_to_remove or failed_to_remove:
                        logger.info(
                            f"清理任务: 已完成 {len(completed_to_remove)}, 失败 {len(failed_to_remove)}"
                        )

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"清理任务异常: {str(e)}")


# 创建全局任务队列实例
task_queue = TaskQueue()
