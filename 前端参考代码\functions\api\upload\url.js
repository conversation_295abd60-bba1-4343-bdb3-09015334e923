// URL上传 API
export async function onRequestPost({ request, env }) {
    try {
        const { url, email } = await request.json();
        
        if (!url || !email) {
            return Response.json({ error: 'URL和邮箱不能为空' }, { status: 400 });
        }

        // 验证URL格式
        let validUrl;
        try {
            validUrl = new URL(url);
            if (!['http:', 'https:'].includes(validUrl.protocol)) {
                throw new Error('无效协议');
            }
        } catch {
            return Response.json({ error: '请输入有效的网页链接' }, { status: 400 });
        }

        // 验证URL长度
        if (url.length > 2048) {
            return Response.json({ error: '链接长度不能超过2048字符' }, { status: 400 });
        }

        // 获取用户数据
        const userKey = `user:${email}`;
        const userData = await env.KV.get(userKey, { type: 'json' });
        
        if (!userData) {
            return Response.json({ error: '用户不存在' }, { status: 404 });
        }

        // 检查积分是否足够
        if (userData.points < 50) {
            return Response.json({ error: '积分不足，需要50积分' }, { status: 402 });
        }

        // 生成任务ID
        const taskId = generateTaskId();
        
        // 创建任务记录
        const task = {
            id: taskId,
            email: email,
            url: url,
            title: extractTitleFromUrl(url),
            type: 'url',
            status: 'PENDING',
            createdAt: new Date().toISOString(),
            progress: 0,
            retryCount: 0,
            pointsCharged: false // 标记积分是否已扣除
        };

        // 不再立即扣除积分，改为任务完成后扣除

        // 保存任务
        const taskKey = `task:${taskId}`;
        await env.KV.put(taskKey, JSON.stringify(task));

        // 立即触发任务处理
        try {
            // 使用 fetch 调用处理API（异步执行）
            const processResponse = await fetch(new URL('/api/process-url-task', request.url), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ taskId })
            });
            
            console.log(`URL任务 ${taskId} 处理请求已发送, 状态: ${processResponse.status}`);
        } catch (processError) {
            console.error('触发URL任务处理失败:', processError);
            // 不影响上传结果，任务会在后续批量处理中被处理
        }

        return Response.json({
            success: true,
            taskId: taskId,
            points: userData.points, // 返回当前积分，未扣除
            message: '链接处理成功，开始转换'
        });

    } catch (error) {
        console.error('URL处理错误:', error);
        return Response.json({ error: '服务器内部错误' }, { status: 500 });
    }
}

// 生成任务ID
function generateTaskId() {
    return 'task_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

// 从URL提取标题
function extractTitleFromUrl(url) {
    try {
        const urlObj = new URL(url);
        const hostname = urlObj.hostname.replace('www.', '');
        const pathname = urlObj.pathname.split('/').filter(p => p).pop() || 'index';
        return `${hostname} - ${pathname}`;
    } catch {
        return 'Web Content';
    }
} 