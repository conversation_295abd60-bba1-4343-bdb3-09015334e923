// 调试用户ID算法一致性的API
import { UserIdResolver } from '../utils/user-id-resolver.js';
import { R2StorageManager } from '../utils/r2-storage.js';

export async function onRequestPost({ request, env }) {
    try {
        const { email, testEmails } = await request.json();
        
        if (!email && !testEmails) {
            return Response.json({ error: '需要提供邮箱地址或测试邮箱列表' }, { status: 400 });
        }
        
        const result = {
            algorithmComparison: {},
            testResults: [],
            inconsistencies: [],
            recommendations: []
        };
        
        // 测试单个邮箱
        if (email) {
            const singleTest = await testSingleEmail(env, email);
            result.testResults.push(singleTest);
            
            if (singleTest.inconsistent) {
                result.inconsistencies.push(singleTest);
            }
        }
        
        // 批量测试邮箱
        if (testEmails && Array.isArray(testEmails)) {
            for (const testEmail of testEmails) {
                const test = await testSingleEmail(env, testEmail);
                result.testResults.push(test);
                
                if (test.inconsistent) {
                    result.inconsistencies.push(test);
                }
            }
        }
        
        // 算法对比分析
        result.algorithmComparison = await compareAlgorithms();
        
        // 生成建议
        result.recommendations = generateRecommendations(result);
        
        return Response.json({
            success: true,
            result: result,
            summary: {
                totalTested: result.testResults.length,
                inconsistencies: result.inconsistencies.length,
                consistencyRate: ((result.testResults.length - result.inconsistencies.length) / result.testResults.length * 100).toFixed(2) + '%'
            }
        });
        
    } catch (error) {
        console.error('调试用户ID算法失败:', error);
        return Response.json({ 
            error: '调试失败: ' + error.message,
            success: false
        }, { status: 500 });
    }
}

// 测试单个邮箱的用户ID生成
async function testSingleEmail(env, email) {
    const test = {
        email: email,
        userIdResolver: null,
        r2StorageManager: null,
        consistent: false,
        inconsistent: false,
        error: null,
        details: {}
    };
    
    try {
        // 使用UserIdResolver生成
        const resolver = new UserIdResolver(env);
        test.userIdResolver = resolver.hashEmail(email);
        test.details.resolverMethod = 'UserIdResolver.hashEmail()';
        
        // 使用R2StorageManager生成
        const r2Manager = new R2StorageManager(env.AUDIO_BUCKET);
        test.r2StorageManager = r2Manager.hashEmail(email);
        test.details.r2Method = 'R2StorageManager.hashEmail()';
        
        // 检查一致性
        test.consistent = test.userIdResolver === test.r2StorageManager;
        test.inconsistent = !test.consistent;
        
        if (test.inconsistent) {
            test.details.issue = '两个类生成的用户ID不一致';
            test.details.difference = {
                resolver: test.userIdResolver,
                r2Manager: test.r2StorageManager
            };
        }
        
        // 尝试在R2中验证
        if (env.AUDIO_BUCKET) {
            const r2Verification = await verifyInR2(env, email, test.userIdResolver, test.r2StorageManager);
            test.details.r2Verification = r2Verification;
        }
        
    } catch (error) {
        test.error = error.message;
        test.details.error = error;
    }
    
    return test;
}

// 在R2中验证用户ID
async function verifyInR2(env, email, resolverUserId, r2UserId) {
    const verification = {
        resolverUserIdExists: false,
        r2UserIdExists: false,
        foundPaths: [],
        recommendation: null
    };
    
    try {
        // 检查resolver生成的用户ID
        const resolverPaths = [
            `users/${resolverUserId}/`,
            `users/${resolverUserId}/audios/`,
            `users/${resolverUserId}/audiobooks/`
        ];
        
        for (const path of resolverPaths) {
            try {
                const listResult = await env.AUDIO_BUCKET.list({
                    prefix: path,
                    limit: 5
                });
                
                if (listResult.objects && listResult.objects.length > 0) {
                    verification.resolverUserIdExists = true;
                    verification.foundPaths.push({
                        path: path,
                        fileCount: listResult.objects.length,
                        type: 'resolver'
                    });
                }
            } catch (error) {
                // 继续检查其他路径
            }
        }
        
        // 检查R2Manager生成的用户ID（如果不同）
        if (resolverUserId !== r2UserId) {
            const r2Paths = [
                `users/${r2UserId}/`,
                `users/${r2UserId}/audios/`,
                `users/${r2UserId}/audiobooks/`
            ];
            
            for (const path of r2Paths) {
                try {
                    const listResult = await env.AUDIO_BUCKET.list({
                        prefix: path,
                        limit: 5
                    });
                    
                    if (listResult.objects && listResult.objects.length > 0) {
                        verification.r2UserIdExists = true;
                        verification.foundPaths.push({
                            path: path,
                            fileCount: listResult.objects.length,
                            type: 'r2Manager'
                        });
                    }
                } catch (error) {
                    // 继续检查其他路径
                }
            }
        }
        
        // 生成建议
        if (verification.resolverUserIdExists && verification.r2UserIdExists) {
            verification.recommendation = 'CONFLICT: 两个用户ID都在R2中存在文件，需要数据迁移';
        } else if (verification.resolverUserIdExists) {
            verification.recommendation = 'USE_RESOLVER: 使用UserIdResolver生成的用户ID';
        } else if (verification.r2UserIdExists) {
            verification.recommendation = 'USE_R2MANAGER: 使用R2StorageManager生成的用户ID';
        } else {
            verification.recommendation = 'NO_DATA: R2中没有找到对应的用户数据';
        }
        
    } catch (error) {
        verification.error = error.message;
    }
    
    return verification;
}

// 对比算法实现
async function compareAlgorithms() {
    const comparison = {
        userIdResolverCode: null,
        r2StorageManagerCode: null,
        differences: [],
        identical: false
    };
    
    try {
        // 这里我们分析两个类的hashEmail方法的实现
        // 由于无法直接获取源代码，我们通过测试来验证
        
        const testEmails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ];
        
        for (const email of testEmails) {
            try {
                // 模拟UserIdResolver的实现
                const resolverHash = simulateUserIdResolverHash(email);
                
                // 模拟R2StorageManager的实现
                const r2Hash = simulateR2StorageManagerHash(email);
                
                if (resolverHash !== r2Hash) {
                    comparison.differences.push({
                        email: email,
                        resolverResult: resolverHash,
                        r2Result: r2Hash
                    });
                }
            } catch (error) {
                comparison.differences.push({
                    email: email,
                    error: error.message
                });
            }
        }
        
        comparison.identical = comparison.differences.length === 0;
        
    } catch (error) {
        comparison.error = error.message;
    }
    
    return comparison;
}

// 模拟UserIdResolver的hashEmail实现
function simulateUserIdResolverHash(email) {
    const cleanEmail = email.trim().toLowerCase();
    let hash = 0;
    for (let i = 0; i < cleanEmail.length; i++) {
        const char = cleanEmail.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(36);
}

// 模拟R2StorageManager的hashEmail实现
function simulateR2StorageManagerHash(email) {
    const cleanEmail = email.trim().toLowerCase();
    let hash = 0;
    for (let i = 0; i < cleanEmail.length; i++) {
        const char = cleanEmail.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(36);
}

// 生成修复建议
function generateRecommendations(result) {
    const recommendations = [];
    
    if (result.inconsistencies.length > 0) {
        recommendations.push({
            type: 'CRITICAL',
            title: '算法不一致问题',
            description: '发现UserIdResolver和R2StorageManager生成的用户ID不一致',
            action: '需要统一两个类的hashEmail方法实现'
        });
    }
    
    if (result.testResults.some(test => test.details.r2Verification?.recommendation === 'CONFLICT')) {
        recommendations.push({
            type: 'WARNING',
            title: '数据冲突',
            description: '同一用户的不同用户ID在R2中都存在数据',
            action: '需要进行数据迁移和合并'
        });
    }
    
    if (result.testResults.some(test => test.error)) {
        recommendations.push({
            type: 'ERROR',
            title: '算法执行错误',
            description: '某些邮箱在生成用户ID时出现错误',
            action: '检查邮箱格式验证和错误处理逻辑'
        });
    }
    
    return recommendations;
}

export async function onRequestOptions() {
    return new Response(null, {
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type',
            'Access-Control-Max-Age': '86400'
        }
    });
}
