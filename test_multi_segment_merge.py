#!/usr/bin/env python3
"""
测试多分段音频合并功能的脚本
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ.setdefault('PYTHONPATH', str(project_root))

async def test_multi_segment_audio_conversion():
    """测试多分段音频转换和合并功能"""
    try:
        # 导入必要的模块
        from app.services.audio_conversion_service import audio_conversion_service
        from app.core.config import settings
        
        print("开始测试多分段音频转换和合并功能...")
        
        # 创建长文本，确保会被分段
        long_text = """
        第一章：人工智能的发展历程
        
        人工智能（Artificial Intelligence，简称AI）是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。该领域的研究包括机器人、语言识别、图像识别、自然语言处理和专家系统等。人工智能从诞生以来，理论和技术日益成熟，应用领域也不断扩大，可以设想，未来人工智能带来的科技产品，将会是人类智慧的"容器"。人工智能可以对人的意识、思维的信息过程的模拟。人工智能不是人的智能，但能像人那样思考、也可能超过人的智能。
        
        第二章：机器学习的基本原理
        
        机器学习是人工智能的一个重要分支，它是一种通过算法使计算机系统能够自动学习和改进的技术。机器学习的核心思想是让计算机通过大量数据的训练，自动发现数据中的模式和规律，从而能够对新的数据进行预测或分类。机器学习主要分为监督学习、无监督学习和强化学习三大类。监督学习是指在已知输入和输出的情况下，训练模型学习输入到输出的映射关系。无监督学习则是在没有标签数据的情况下，让模型自动发现数据的内在结构。强化学习是通过与环境的交互，学习如何在特定环境中采取行动以获得最大的累积奖励。
        
        第三章：深度学习的革命性突破
        
        深度学习是机器学习的一个子领域，它基于人工神经网络的研究，特别是利用多层神经网络来进行学习和表示。深度学习的突破性进展主要体现在图像识别、语音识别、自然语言处理等领域。卷积神经网络（CNN）在图像识别任务中取得了巨大成功，循环神经网络（RNN）和长短期记忆网络（LSTM）在序列数据处理方面表现出色，而Transformer架构的出现更是推动了自然语言处理领域的快速发展。深度学习的成功离不开大数据、强大的计算能力和优化算法的进步。
        
        第四章：自然语言处理的应用
        
        自然语言处理（Natural Language Processing，简称NLP）是人工智能和语言学领域的分支学科。在此领域中探讨如何处理及运用自然语言。自然语言处理包括多个子任务，如词法分析、句法分析、语义分析、语用分析等。现代NLP技术广泛应用于机器翻译、情感分析、文本摘要、问答系统、聊天机器人等领域。随着预训练语言模型如BERT、GPT等的出现，NLP技术取得了突破性进展，在很多任务上已经接近或超过人类水平。这些技术的发展为智能客服、内容生成、语言教学等应用提供了强大的技术支撑。
        
        第五章：计算机视觉的进展
        
        计算机视觉是一门研究如何使机器"看"的科学，更进一步的说，就是指用摄影机和电脑代替人眼对目标进行识别、跟踪和测量等机器视觉，并进一步做图形处理，使电脑处理成为更适合人眼观察或传送给仪器检测的图像。计算机视觉技术在自动驾驶、医疗影像分析、安防监控、工业检测等领域有着广泛的应用。深度学习的发展极大地推动了计算机视觉技术的进步，使得机器在图像分类、目标检测、语义分割等任务上取得了显著的成果。
        """
        
        # 创建临时文本文件
        temp_dir = Path(settings.temp_dir)
        temp_dir.mkdir(parents=True, exist_ok=True)
        
        test_file = temp_dir / "test_multi_segment.txt"
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(long_text)
        
        print(f"创建测试文件: {test_file}")
        print(f"文本长度: {len(long_text)} 字符")
        
        # 模拟任务参数
        task_id = 998
        user_id = 1
        file_path = str(test_file)
        file_type = "txt"
        
        print(f"开始多分段音频转换任务...")
        print(f"任务ID: {task_id}")
        print(f"用户ID: {user_id}")
        print(f"文件路径: {file_path}")
        
        # 执行音频转换
        segment_count = 0
        async for result in audio_conversion_service.convert_file_to_audio(
            task_id=task_id,
            user_id=user_id,
            file_path=file_path,
            file_type=file_type
        ):
            print(f"进度更新: {result}")
            
            # 统计分段数量
            if result.get("type") == "progress" and "转换段落" in result.get("message", ""):
                segment_count += 1
            
            if result.get("type") == "complete":
                final_result = result.get("result", {})
                print("\n=== 多分段转换完成 ===")
                print(f"检测到的分段数: {segment_count}")
                print(f"最终音频文件: {final_result.get('audio_file', {})}")
                print(f"总时长: {final_result.get('total_duration', 0):.1f}秒")
                print(f"字数: {final_result.get('word_count', 0)}")
                print(f"处理的分段数: {final_result.get('segments_processed', 0)}")
                print(f"标题: {final_result.get('title', 'N/A')}")
                
                # 验证最终文件是否存在
                audio_file_info = final_result.get('audio_file', {})
                if audio_file_info:
                    print(f"\n音频文件信息:")
                    print(f"  文件名: {audio_file_info.get('filename')}")
                    print(f"  URL: {audio_file_info.get('url')}")
                    print(f"  大小: {audio_file_info.get('size', 0)} bytes")
                    print(f"  时长: {audio_file_info.get('duration', 0):.1f}秒")
                    print(f"  合并的分段数: {audio_file_info.get('segments_merged', 0)}")
                    
                    # 验证是否真的进行了多分段合并
                    if audio_file_info.get('segments_merged', 0) > 1:
                        print(f"\n✅ 成功合并了 {audio_file_info.get('segments_merged')} 个音频分段！")
                    else:
                        print(f"\n⚠️  只有 {audio_file_info.get('segments_merged')} 个分段，可能文本不够长或分段阈值设置过高")
                
                break
            elif result.get("type") == "error":
                print(f"转换失败: {result.get('message')}")
                return False
        
        # 清理测试文件
        try:
            test_file.unlink()
            print(f"\n清理测试文件: {test_file}")
        except:
            pass
        
        print("\n多分段测试完成！")
        return True
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("多分段音频合并功能测试")
    print("=" * 50)
    
    # 运行测试
    success = asyncio.run(test_multi_segment_audio_conversion())
    
    if success:
        print("\n✅ 多分段测试成功！音频合并功能正常工作。")
    else:
        print("\n❌ 多分段测试失败！请检查错误信息。")
        sys.exit(1)
