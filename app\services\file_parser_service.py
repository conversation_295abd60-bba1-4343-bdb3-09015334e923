"""
统一文件解析服务
根据文件扩展名自动选择合适的解析器（EPUB或TXT），提供统一的解析接口
基于原始参考代码完整迁移，保持所有算法逻辑不变
"""

import os
from typing import List, Tuple, Union
from pathlib import Path

from app.services.epub_parser import EpubParser, EpubMetadata, Chapter
from app.services.txt_parser import TxtParser, TxtMetadata
from app.core.logging import get_logger

logger = get_logger(__name__)

# 类型别名，用于统一元数据类型
Metadata = Union[EpubMetadata, TxtMetadata]


class FileParserService:
    """统一文件解析服务"""
    
    def __init__(self):
        self.epub_parser = EpubParser()
        self.txt_parser = TxtParser()
    
    async def parse_from_file(self, file_path: str) -> Tuple[Metadata, List[Chapter]]:
        """
        从文件路径解析文件，自动选择合适的解析器
        
        Args:
            file_path: 文件路径
            
        Returns:
            Tuple[Metadata, List[Chapter]]: 元数据和章节列表
            
        Raises:
            ValueError: 不支持的文件格式
            Exception: 解析失败
        """
        try:
            file_extension = self._get_file_extension(file_path)
            
            logger.info(f"开始解析文件: {file_path}, 格式: {file_extension}")
            
            if file_extension == '.epub':
                return await self.epub_parser.parse_from_file(file_path)
            elif file_extension == '.txt':
                return await self.txt_parser.parse_from_file(file_path)
            else:
                raise ValueError(f"不支持的文件格式: {file_extension}")
                
        except Exception as e:
            logger.error(f"文件解析失败: {str(e)}")
            raise
    
    async def parse_from_url(self, file_url: str) -> Tuple[Metadata, List[Chapter]]:
        """
        从URL下载并解析文件，自动选择合适的解析器
        
        Args:
            file_url: 文件URL
            
        Returns:
            Tuple[Metadata, List[Chapter]]: 元数据和章节列表
            
        Raises:
            ValueError: 不支持的文件格式
            Exception: 解析失败
        """
        try:
            file_extension = self._get_file_extension(file_url)
            
            logger.info(f"开始从URL解析文件: {file_url}, 格式: {file_extension}")
            
            if file_extension == '.epub':
                return await self.epub_parser.parse_from_url(file_url)
            elif file_extension == '.txt':
                return await self.txt_parser.parse_from_url(file_url)
            else:
                raise ValueError(f"不支持的文件格式: {file_extension}")
                
        except Exception as e:
            logger.error(f"从URL解析文件失败: {str(e)}")
            raise
    
    def get_supported_formats(self) -> List[str]:
        """
        获取支持的文件格式列表
        
        Returns:
            List[str]: 支持的文件扩展名列表
        """
        return ['.epub', '.txt']
    
    def is_supported_format(self, file_path_or_url: str) -> bool:
        """
        检查文件格式是否支持
        
        Args:
            file_path_or_url: 文件路径或URL
            
        Returns:
            bool: 是否支持该格式
        """
        try:
            file_extension = self._get_file_extension(file_path_or_url)
            return file_extension in self.get_supported_formats()
        except:
            return False
    
    def get_file_type(self, file_path_or_url: str) -> str:
        """
        获取文件类型
        
        Args:
            file_path_or_url: 文件路径或URL
            
        Returns:
            str: 文件类型 ('epub', 'txt', 'unknown')
        """
        try:
            file_extension = self._get_file_extension(file_path_or_url)
            if file_extension == '.epub':
                return 'epub'
            elif file_extension == '.txt':
                return 'txt'
            else:
                return 'unknown'
        except:
            return 'unknown'
    
    def _get_file_extension(self, file_path_or_url: str) -> str:
        """
        获取文件扩展名
        
        Args:
            file_path_or_url: 文件路径或URL
            
        Returns:
            str: 文件扩展名（小写，包含点号）
        """
        # 移除URL参数
        if '?' in file_path_or_url:
            file_path_or_url = file_path_or_url.split('?')[0]
        
        # 获取扩展名
        path = Path(file_path_or_url)
        extension = path.suffix.lower()
        
        return extension
    
    async def get_file_info(self, file_path_or_url: str) -> dict:
        """
        获取文件基本信息（不进行完整解析）
        
        Args:
            file_path_or_url: 文件路径或URL
            
        Returns:
            dict: 文件信息
        """
        try:
            file_type = self.get_file_type(file_path_or_url)
            is_supported = self.is_supported_format(file_path_or_url)
            
            # 获取文件名
            filename = os.path.basename(file_path_or_url)
            if '?' in filename:
                filename = filename.split('?')[0]
            
            info = {
                'filename': filename,
                'file_type': file_type,
                'is_supported': is_supported,
                'extension': self._get_file_extension(file_path_or_url),
                'parser_type': file_type if is_supported else None
            }
            
            return info
            
        except Exception as e:
            logger.error(f"获取文件信息失败: {str(e)}")
            return {
                'filename': 'unknown',
                'file_type': 'unknown',
                'is_supported': False,
                'extension': '',
                'parser_type': None,
                'error': str(e)
            }
    
    def get_parser_for_file(self, file_path_or_url: str):
        """
        获取文件对应的解析器实例
        
        Args:
            file_path_or_url: 文件路径或URL
            
        Returns:
            Union[EpubParser, TxtParser]: 对应的解析器实例
            
        Raises:
            ValueError: 不支持的文件格式
        """
        file_type = self.get_file_type(file_path_or_url)
        
        if file_type == 'epub':
            return self.epub_parser
        elif file_type == 'txt':
            return self.txt_parser
        else:
            raise ValueError(f"不支持的文件格式: {file_type}")


# 创建全局实例
file_parser_service = FileParserService()


# 便捷函数
async def parse_file(file_path: str) -> Tuple[Metadata, List[Chapter]]:
    """
    解析文件的便捷函数
    
    Args:
        file_path: 文件路径
        
    Returns:
        Tuple[Metadata, List[Chapter]]: 元数据和章节列表
    """
    return await file_parser_service.parse_from_file(file_path)


async def parse_file_from_url(file_url: str) -> Tuple[Metadata, List[Chapter]]:
    """
    从URL解析文件的便捷函数
    
    Args:
        file_url: 文件URL
        
    Returns:
        Tuple[Metadata, List[Chapter]]: 元数据和章节列表
    """
    return await file_parser_service.parse_from_url(file_url)


def get_file_info(file_path_or_url: str) -> dict:
    """
    获取文件信息的便捷函数
    
    Args:
        file_path_or_url: 文件路径或URL
        
    Returns:
        dict: 文件信息
    """
    import asyncio
    return asyncio.run(file_parser_service.get_file_info(file_path_or_url))


def is_supported_file(file_path_or_url: str) -> bool:
    """
    检查文件是否支持的便捷函数
    
    Args:
        file_path_or_url: 文件路径或URL
        
    Returns:
        bool: 是否支持该格式
    """
    return file_parser_service.is_supported_format(file_path_or_url)
