"""
电子书转音频单体应用 - 主应用入口
集成前后端，提供完整的电子书转音频服务
"""

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
import uvicorn
import os
from pathlib import Path
from contextlib import asynccontextmanager

# 导入配置和核心模块
from app.core.config import settings
from app.core.logging import setup_logging
from app.core.error_handler import setup_error_handlers
from app.core.database import init_database

# 导入API路由
from app.api import auth, convert, files, health

# 设置日志
setup_logging()

# 应用根目录
BASE_DIR = Path(__file__).parent.parent
STATIC_DIR = BASE_DIR / "app" / "static"
TEMPLATES_DIR = BASE_DIR / "app" / "templates"

# 确保必要目录存在
os.makedirs(STATIC_DIR, exist_ok=True)
os.makedirs(TEMPLATES_DIR, exist_ok=True)
os.makedirs(settings.upload_dir, exist_ok=True)
os.makedirs(settings.audio_dir, exist_ok=True)


# 生命周期管理
@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化
    await init_database()

    # 启动任务队列
    from app.services.task_queue import task_queue
    await task_queue.start()

    yield

    # 关闭时清理
    await task_queue.stop()


# 创建FastAPI应用实例
app = FastAPI(
    title="电子书转音频服务",
    description="将电子书转换为音频文件的单体应用",
    version="1.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc",
    lifespan=lifespan,
)

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 设置错误处理器
setup_error_handlers(app)

# 挂载静态文件
app.mount("/static", StaticFiles(directory=str(STATIC_DIR)), name="static")

# 挂载文件服务 - 用于访问用户上传和生成的文件
from pathlib import Path
DATA_DIR = Path(settings.data_dir)
app.mount("/files", StaticFiles(directory=str(DATA_DIR)), name="files")

# 设置模板引擎
templates = Jinja2Templates(directory=str(TEMPLATES_DIR))

# 注册API路由
app.include_router(health.router, prefix="/api", tags=["健康检查"])
app.include_router(auth.router, prefix="/api/auth", tags=["用户认证"])
app.include_router(convert.router, prefix="/api", tags=["音频转换"])
app.include_router(files.router, prefix="/api", tags=["文件管理"])

# 导入并注册音频API
from app.api import audio
app.include_router(audio.router, prefix="/api", tags=["音频库"])


# 前端页面路由
@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    """首页"""
    return templates.TemplateResponse("index.html", {"request": request})


@app.get("/workshop", response_class=HTMLResponse)
async def workshop(request: Request):
    """创作工坊页面"""
    return templates.TemplateResponse("workshop.html", {"request": request})


@app.get("/library", response_class=HTMLResponse)
async def library(request: Request):
    """音频库页面"""
    return templates.TemplateResponse("library.html", {"request": request})


@app.get("/tasks", response_class=HTMLResponse)
async def tasks(request: Request):
    """任务管理页面"""
    return templates.TemplateResponse("tasks.html", {"request": request})


# API信息端点
@app.get("/api/info")
async def api_info():
    """API信息"""
    return {
        "service": "电子书转音频服务",
        "version": "1.0.0",
        "status": "运行中",
        "architecture": "单体应用",
        "supported_formats": ["epub", "txt", "pdf", "docx"],
        "output_formats": ["mp3", "wav"],
        "max_file_size": "50MB",
        "supported_languages": ["zh-CN", "en-US"],
        "features": [
            "本地用户认证",
            "本地文件存储",
            "Docker部署支持",
            "响应式界面"
        ]
    }


if __name__ == "__main__":
    # 开发环境启动配置
    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        reload=True,
        log_level="info",
    )
