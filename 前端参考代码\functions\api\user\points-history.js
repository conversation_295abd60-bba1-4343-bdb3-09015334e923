// 用户积分记录 API
import { formatDateTime } from '../../utils/time-formatter.js';

export async function onRequestPost({ request, env }) {
    try {
        const { email } = await request.json();
        
        if (!email) {
            return Response.json({ error: '邮箱不能为空' }, { status: 400 });
        }

        // 验证用户是否存在
        const userKey = `user:${email}`;
        const userData = await env.KV.get(userKey, { type: 'json' });
        
        if (!userData) {
            return Response.json({ error: '用户不存在' }, { status: 404 });
        }

        // 获取用户的所有积分记录
        const recordsPrefix = `points_record:${email}:`;
        const recordsList = await env.KV.list({ prefix: recordsPrefix });
        const pointsRecords = [];
        
        for (const key of recordsList.keys) {
            const record = await env.KV.get(key.name, { type: 'json' });
            if (record) {
                // 格式化创建时间
                record.createdAtFormatted = formatDateTime(record.createdAt);
                
                // 添加显示类型
                record.typeDisplay = record.type === 'EARN' ? '获得' : '消费';
                
                // 添加颜色类别
                record.colorClass = record.type === 'EARN' ? 'positive' : 'negative';
                
                // 格式化积分变化
                record.amountDisplay = record.type === 'EARN' ? `+${record.amount}` : `${record.amount}`;
                
                pointsRecords.push(record);
            }
        }

        // 按创建时间降序排序
        pointsRecords.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        // 计算统计信息
        const stats = {
            totalEarned: 0,
            totalConsumed: 0,
            totalRecords: pointsRecords.length,
            currentBalance: userData.points
        };

        pointsRecords.forEach(record => {
            if (record.type === 'EARN') {
                stats.totalEarned += record.amount;
            } else {
                stats.totalConsumed += Math.abs(record.amount);
            }
        });

        return Response.json({
            success: true,
            records: pointsRecords,
            stats: stats
        });

    } catch (error) {
        console.error('获取积分记录错误:', error);
        return Response.json({ error: '服务器内部错误' }, { status: 500 });
    }
} 