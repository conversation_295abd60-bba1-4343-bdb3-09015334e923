// 用户存储管理 API
import { R2StorageManager } from '../../utils/r2-storage.js';

export async function onRequestPost({ request, env }) {
    try {
        const { email, action } = await request.json();
        
        if (!email) {
            return Response.json({ error: '邮箱不能为空' }, { status: 400 });
        }

        // 验证用户是否存在
        const userKey = `user:${email}`;
        const userData = await env.KV.get(userKey, { type: 'json' });
        
        if (!userData) {
            return Response.json({ error: '用户不存在' }, { status: 404 });
        }

        const r2Storage = new R2StorageManager(env.R2);

        switch (action) {
            case 'usage':
                return await getStorageUsage(email, r2Storage);
            
            case 'cleanup':
                return await cleanupOldFiles(email, r2Storage, env);
            
            case 'list':
                return await listUserFiles(email, r2Storage, env);
            
            default:
                return Response.json({ error: '无效的操作' }, { status: 400 });
        }

    } catch (error) {
        console.error('存储管理错误:', error);
        return Response.json({ error: '服务器内部错误' }, { status: 500 });
    }
}

// 获取存储使用情况
async function getStorageUsage(email, r2Storage) {
    try {
        const usage = await r2Storage.getUserStorageUsage(email);
        
        return Response.json({
            success: true,
            usage: usage
        });
    } catch (error) {
        console.error('获取存储使用情况失败:', error);
        throw error;
    }
}

// 清理旧文件
async function cleanupOldFiles(email, r2Storage, env) {
    try {
        // 获取用户的所有任务
        const taskList = await env.KV.list({ prefix: 'task:' });
        const userTasks = [];
        
        for (const key of taskList.keys) {
            const task = await env.KV.get(key.name, { type: 'json' });
            if (task && task.email === email) {
                userTasks.push(task);
            }
        }

        // 筛选出30天前完成的任务
        const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        const oldTasks = userTasks.filter(task => {
            if (task.completedAt || task.failedAt) {
                const completedDate = new Date(task.completedAt || task.failedAt);
                return completedDate < thirtyDaysAgo;
            }
            return false;
        });

        // 删除旧任务的文件
        let deletedCount = 0;
        let errors = [];

        for (const task of oldTasks) {
            try {
                await r2Storage.deleteTaskFiles(email, task.id);
                deletedCount++;
            } catch (error) {
                console.error(`删除任务 ${task.id} 文件失败:`, error);
                errors.push(`任务 ${task.id}: ${error.message}`);
            }
        }

        return Response.json({
            success: true,
            message: `清理完成，删除了 ${deletedCount} 个任务的文件`,
            deletedCount,
            errors: errors.length > 0 ? errors : undefined
        });

    } catch (error) {
        console.error('清理文件失败:', error);
        throw error;
    }
}

// 列出用户文件
async function listUserFiles(email, r2Storage, env) {
    try {
        // 获取用户的任务
        const taskList = await env.KV.list({ prefix: 'task:' });
        const userTasks = [];
        
        for (const key of taskList.keys) {
            const task = await env.KV.get(key.name, { type: 'json' });
            if (task && task.email === email) {
                userTasks.push({
                    id: task.id,
                    type: task.type,
                    status: task.status,
                    filename: task.filename || task.title,
                    audioSize: task.audioSize,
                    wordCount: task.wordCount,
                    createdAt: task.createdAt,
                    completedAt: task.completedAt,
                    audioPath: task.audioPath,
                    filePath: task.filePath
                });
            }
        }

        // 按创建时间降序排序
        userTasks.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        // 获取存储使用情况
        const usage = await r2Storage.getUserStorageUsage(email);

        return Response.json({
            success: true,
            tasks: userTasks,
            usage: usage,
            total: userTasks.length
        });

    } catch (error) {
        console.error('列出用户文件失败:', error);
        throw error;
    }
} 