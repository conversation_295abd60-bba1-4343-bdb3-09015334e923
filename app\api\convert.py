"""
音频转换API路由
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional
import uuid

from app.core.database import get_db
from app.core.auth import get_current_active_user, require_points
from app.core.config import settings
from app.core.logging import get_logger
from app.models.user import User
from app.models.task import Task, TaskCreate, TaskResponse, TaskSummary, TaskStatus, ConversionTask
from app.services.task_queue import task_queue
from app.services.storage_service import storage_service

logger = get_logger(__name__)
router = APIRouter()


@router.post("/convert", response_model=TaskResponse)
async def create_conversion_task(
    task_create: TaskCreate,
    file_url: Optional[str] = None,
    current_user: User = Depends(require_points(settings.points_per_conversion)),
    db: Session = Depends(get_db)
):
    """创建音频转换任务"""
    try:
        # 创建数据库任务记录
        db_task = Task(
            user_id=current_user.id,
            title=task_create.title,
            task_type=task_create.task_type,
            processing_mode=task_create.processing_mode,
            voice_settings=task_create.voice_settings,
            file_url=file_url,
            status=TaskStatus.PENDING,
            progress=0
        )

        db.add(db_task)
        db.commit()
        db.refresh(db_task)

        # 创建转换任务对象
        conversion_task = ConversionTask()
        conversion_task.task_id = str(db_task.id)
        conversion_task.user_id = str(current_user.id)
        conversion_task.file_url = file_url or ""
        conversion_task.voice_settings = task_create.voice_settings
        conversion_task.original_filename = getattr(task_create, 'original_filename', '')

        # 添加到任务队列
        success = task_queue.add_task(conversion_task)

        if not success:
            # 如果添加到队列失败，更新数据库状态
            db_task.status = TaskStatus.FAILED
            db_task.error_message = "任务队列已满，请稍后重试"
            db.commit()

            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="任务队列已满，请稍后重试"
            )

        logger.info(f"转换任务创建成功并已加入队列: {db_task.id}")

        return TaskResponse.model_validate(db_task)

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"创建转换任务失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建转换任务失败"
        )


@router.get("/tasks", response_model=List[TaskSummary])
async def get_user_tasks(
    skip: int = 0,
    limit: int = 20,
    status_filter: Optional[TaskStatus] = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取用户任务列表"""
    try:
        query = db.query(Task).filter(Task.user_id == current_user.id)
        
        if status_filter:
            query = query.filter(Task.status == status_filter)
        
        tasks = query.order_by(Task.created_at.desc()).offset(skip).limit(limit).all()
        
        return [TaskSummary.model_validate(task) for task in tasks]
        
    except Exception as e:
        logger.error(f"获取用户任务失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取任务列表失败"
        )


@router.get("/tasks/{task_id}", response_model=TaskResponse)
async def get_task_detail(
    task_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取任务详情"""
    try:
        task = db.query(Task).filter(
            Task.id == task_id,
            Task.user_id == current_user.id
        ).first()
        
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在"
            )
        
        return TaskResponse.model_validate(task)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务详情失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取任务详情失败"
        )


@router.delete("/tasks/{task_id}")
async def delete_task(
    task_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """删除任务"""
    try:
        task = db.query(Task).filter(
            Task.id == task_id,
            Task.user_id == current_user.id
        ).first()
        
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在"
            )
        
        # 删除相关文件
        try:
            await storage_service.delete_task_files(str(task_id), str(current_user.id))
        except Exception as e:
            logger.warning(f"删除任务文件失败: {str(e)}")
            # 继续删除数据库记录，即使文件删除失败
        
        db.delete(task)
        db.commit()
        
        return {"message": "任务删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"删除任务失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除任务失败"
        )


@router.post("/tasks/{task_id}/cancel")
async def cancel_task(
    task_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """取消任务"""
    try:
        task = db.query(Task).filter(
            Task.id == task_id,
            Task.user_id == current_user.id
        ).first()
        
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在"
            )
        
        if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="任务已完成或已取消，无法取消"
            )
        
        # 尝试从任务队列中取消任务
        queue_cancelled = task_queue.cancel_task(str(task.id))

        # 更新数据库状态
        task.status = TaskStatus.CANCELLED
        db.commit()

        if queue_cancelled:
            return {"message": "任务已从队列中取消"}
        else:
            return {"message": "任务状态已更新为取消（可能已在处理中）"}
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"取消任务失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="取消任务失败"
        )


@router.get("/tasks/{task_id}/status")
async def get_task_status(
    task_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取任务状态"""
    try:
        task = db.query(Task).filter(
            Task.id == task_id,
            Task.user_id == current_user.id
        ).first()
        
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在"
            )
        
        return {
            "task_id": task.id,
            "status": task.status,
            "progress": task.progress,
            "error_message": task.error_message,
            "updated_at": task.updated_at
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务状态失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取任务状态失败"
        )


@router.get("/stats")
async def get_conversion_stats(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取转换统计信息"""
    try:
        from app.models.task import TaskStats
        
        # 用户任务统计
        user_tasks = db.query(Task).filter(Task.user_id == current_user.id)
        
        total_tasks = user_tasks.count()
        pending_tasks = user_tasks.filter(Task.status == TaskStatus.PENDING).count()
        processing_tasks = user_tasks.filter(Task.status == TaskStatus.PROCESSING).count()
        completed_tasks = user_tasks.filter(Task.status == TaskStatus.COMPLETED).count()
        failed_tasks = user_tasks.filter(Task.status == TaskStatus.FAILED).count()
        
        return TaskStats(
            total_tasks=total_tasks,
            pending_tasks=pending_tasks,
            processing_tasks=processing_tasks,
            completed_tasks=completed_tasks,
            failed_tasks=failed_tasks
        )
        
    except Exception as e:
        logger.error(f"获取转换统计失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取统计信息失败"
        )
