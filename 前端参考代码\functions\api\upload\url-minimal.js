// 极简版文件上传 - 只存储上传成功状态到KV
import { MinimalTaskManager } from '../../utils/minimal-task-manager.js';
import { R2StorageManager } from '../../utils/r2-storage.js';

export async function onRequestPost({ request, env }) {
    try {
        const { filename, fileSize, contentType, email } = await request.json();
        
        // 基础验证
        if (!filename || !fileSize || !email) {
            return Response.json({ error: '缺少必要参数' }, { status: 400 });
        }

        // 验证文件格式
        const allowedExtensions = ['epub', 'txt'];
        const fileExtension = filename.toLowerCase().split('.').pop();
        
        if (!allowedExtensions.includes(fileExtension)) {
            return Response.json({ 
                error: `不支持的文件格式: ${fileExtension}，支持格式: ${allowedExtensions.join(', ')}` 
            }, { status: 400 });
        }

        // 验证文件大小（50MB限制）
        const maxSize = 50 * 1024 * 1024; // 50MB
        if (fileSize > maxSize) {
            return Response.json({ 
                error: `文件过大，最大支持 ${Math.round(maxSize / (1024 * 1024))}MB` 
            }, { status: 400 });
        }

        // 生成任务ID
        const taskId = generateTaskId();
        console.log(`创建极简上传任务: ${taskId} (${filename})`);

        // 初始化R2存储管理器
        if (!env.R2) {
            return Response.json({ error: 'R2存储服务不可用' }, { status: 503 });
        }

        const r2Storage = new R2StorageManager(env.R2);

        // 生成R2存储路径
        const emailHash = email.replace('@', '_').replace(/\./g, '_');
        const filePath = `users/${emailHash}/tasks/${taskId}/${filename}`;

        console.log(`生成R2文件路径: ${filePath}`);

        // 生成预签名上传URL
        const uploadUrl = await r2Storage.generatePresignedUploadUrl(filePath, {
            contentType: contentType,
            maxFileSize: fileSize,
            expiresIn: 3600 // 1小时有效期
        });

        // 生成公开访问URL（用于后续转换）
        const publicUrl = await r2Storage.getPublicUrl(filePath);

        console.log('预签名URL生成成功');

        return Response.json({
            success: true,
            taskId: taskId,
            uploadUrl: uploadUrl,
            publicUrl: publicUrl,
            filePath: filePath,
            expiresIn: 3600,
            message: '上传URL已生成，请上传文件后调用确认接口'
        });

    } catch (error) {
        console.error('生成上传URL失败:', error);
        return Response.json({ error: error.message }, { status: 500 });
    }
}

// 文件上传确认接口
export async function onRequestPut({ request, env }) {
    try {
        const { taskId, filePath, email, filename, fileSize } = await request.json();
        
        if (!taskId || !filePath || !email || !filename) {
            return Response.json({ error: '缺少必要参数' }, { status: 400 });
        }

        console.log(`确认文件上传: ${taskId}`);

        // 验证文件是否确实已上传到R2
        if (env.R2) {
            const r2Storage = new R2StorageManager(env.R2);
            const fileExists = await r2Storage.fileExists(filePath);
            
            if (!fileExists) {
                return Response.json({ error: '文件上传失败或文件不存在' }, { status: 400 });
            }
            
            console.log(`文件确认存在: ${filePath}`);
        }

        // 记录上传成功状态到KV
        const taskManager = new MinimalTaskManager(taskId, env);
        const task = await taskManager.markUploadSuccess({
            email: email,
            filename: filename,
            filePath: filePath,
            fileSize: fileSize
        });

        console.log(`任务上传成功状态已记录: ${taskId}`);

        return Response.json({
            success: true,
            taskId: taskId,
            status: task.status,
            message: '文件上传成功，可以开始转换',
            nextStep: '调用 /api/process-task-minimal 开始转换'
        });

    } catch (error) {
        console.error('确认文件上传失败:', error);
        
        // 如果是因为任务管理器失败，尝试记录失败状态
        try {
            const taskManager = new MinimalTaskManager(request.taskId || 'unknown', env);
            await taskManager.markFailed(error, 'upload');
        } catch (markError) {
            console.error('记录上传失败状态也失败:', markError);
        }

        return Response.json({ error: error.message }, { status: 500 });
    }
}

// 生成任务ID
function generateTaskId() {
    return 'task_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

// 检查用户是否有足够积分（可选的预检查）
async function checkUserPoints(email, env) {
    try {
        const userKey = `user:${email}`;
        const userData = await env.KV.get(userKey, { type: 'json' });
        
        if (!userData) {
            return { hasEnoughPoints: false, points: 0, required: 50 };
        }

        const requiredPoints = 50; // 最低积分要求
        const hasEnoughPoints = userData.points >= requiredPoints;

        return {
            hasEnoughPoints,
            points: userData.points,
            required: requiredPoints
        };
    } catch (error) {
        console.error('检查用户积分失败:', error);
        return { hasEnoughPoints: true, points: 0, required: 0 }; // 默认允许
    }
}

// 获取文件上传状态
export async function onRequestGet({ request, env }) {
    try {
        const url = new URL(request.url);
        const taskId = url.searchParams.get('taskId');
        
        if (!taskId) {
            return Response.json({ error: '缺少taskId参数' }, { status: 400 });
        }

        const taskManager = new MinimalTaskManager(taskId, env);
        const status = await taskManager.getStatus();
        
        if (!status) {
            return Response.json({ error: '任务不存在' }, { status: 404 });
        }

        return Response.json({
            success: true,
            taskId: taskId,
            status: status.status,
            createdAt: status.createdAt,
            metadata: status.metadata,
            message: getStatusMessage(status.status)
        });

    } catch (error) {
        console.error('获取上传状态失败:', error);
        return Response.json({ error: error.message }, { status: 500 });
    }
}

// 获取状态消息
function getStatusMessage(status) {
    switch (status) {
        case 'UPLOAD_SUCCESS':
            return '文件上传成功，可以开始转换';
        case 'UPLOAD_FAILED':
            return '文件上传失败';
        case 'CONVERSION_SUCCESS':
            return '转换完成';
        case 'CONVERSION_FAILED':
            return '转换失败';
        default:
            return '状态未知';
    }
} 