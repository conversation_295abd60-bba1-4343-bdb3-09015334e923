// 调试R2存储检查的API
import { UserIdResolver } from '../utils/user-id-resolver.js';

export async function onRequestPost({ request, env }) {
    try {
        const { email, taskId, expectedUserId, expectedPath } = await request.json();
        
        if (!email || !taskId) {
            return Response.json({ error: '邮箱和任务ID参数不能为空' }, { status: 400 });
        }
        
        console.log(`🔍 调试R2存储检查 - 邮箱: ${email}, 任务ID: ${taskId}`);
        
        const result = {
            success: false,
            filesFound: false,
            pathChecks: [],
            files: [],
            details: {
                userIdResolution: null,
                totalFilesFound: 0,
                totalPathsChecked: 0
            }
        };
        
        // 1. 解析用户ID
        const resolver = new UserIdResolver(env);
        const userIdResult = await resolver.resolveUserId(email, taskId);
        result.details.userIdResolution = userIdResult;
        
        if (!userIdResult.userId) {
            return Response.json({
                ...result,
                error: '无法解析用户ID'
            });
        }
        
        const userId = userIdResult.userId;
        console.log(`✅ 解析到用户ID: ${userId}`);
        
        // 2. 检查多个可能的路径
        const pathPatterns = [
            `users/${userId}/audios/${taskId}/`,
            `users/${userId}/audiobooks/${taskId}/`,
            `users/${userId}/audio/${taskId}/`,
            `audiobooks/${userId}/${taskId}/`,
            `audios/${userId}/${taskId}/`,
            // 如果提供了预期路径，也检查它
            ...(expectedPath ? [expectedPath] : [])
        ];
        
        // 去重
        const uniquePaths = [...new Set(pathPatterns)];
        result.details.totalPathsChecked = uniquePaths.length;
        
        for (const path of uniquePaths) {
            const pathCheck = {
                path: path,
                found: false,
                fileCount: 0,
                files: [],
                error: null
            };
            
            try {
                console.log(`🔍 检查路径: ${path}`);
                
                const listResult = await env.R2.list({
                    prefix: path,
                    limit: 50
                });
                
                if (listResult.objects && listResult.objects.length > 0) {
                    pathCheck.found = true;
                    pathCheck.fileCount = listResult.objects.length;
                    pathCheck.files = listResult.objects.map(obj => ({
                        key: obj.key,
                        size: obj.size,
                        lastModified: obj.lastModified,
                        type: getFileType(obj.key)
                    }));
                    
                    result.files.push(...pathCheck.files);
                    result.details.totalFilesFound += pathCheck.fileCount;
                    result.filesFound = true;
                    
                    console.log(`✅ 在 ${path} 找到 ${pathCheck.fileCount} 个文件`);
                } else {
                    console.log(`❌ 在 ${path} 未找到文件`);
                }
                
            } catch (error) {
                pathCheck.error = error.message;
                console.warn(`检查路径 ${path} 失败:`, error);
            }
            
            result.pathChecks.push(pathCheck);
        }
        
        // 3. 分析结果
        result.success = true;
        
        // 检查是否找到了预期的文件
        const audioFiles = result.files.filter(f => f.type === 'audio');
        const metadataFiles = result.files.filter(f => f.type === 'metadata');
        
        result.details.audioFilesFound = audioFiles.length;
        result.details.metadataFilesFound = metadataFiles.length;
        result.details.hasExpectedFiles = audioFiles.length > 0 && metadataFiles.length > 0;
        
        // 检查用户ID是否匹配预期
        if (expectedUserId) {
            result.details.userIdMatches = userId === expectedUserId;
        }
        
        // 检查是否在预期路径找到文件
        if (expectedPath) {
            const expectedPathCheck = result.pathChecks.find(check => check.path === expectedPath);
            result.details.expectedPathHasFiles = expectedPathCheck ? expectedPathCheck.found : false;
        }
        
        console.log(`📊 R2检查完成 - 找到文件: ${result.filesFound}, 总文件数: ${result.details.totalFilesFound}`);
        
        return Response.json(result);
        
    } catch (error) {
        console.error('调试R2存储检查失败:', error);
        return Response.json({ 
            error: '检查失败: ' + error.message,
            success: false
        }, { status: 500 });
    }
}

// 根据文件扩展名判断文件类型
function getFileType(filename) {
    const ext = filename.toLowerCase().split('.').pop();
    
    if (['mp3', 'wav', 'm4a', 'aac', 'ogg', 'flac'].includes(ext)) {
        return 'audio';
    } else if (['json'].includes(ext)) {
        if (filename.toLowerCase().includes('metadata')) {
            return 'metadata';
        } else if (filename.toLowerCase().includes('playlist')) {
            return 'playlist';
        } else {
            return 'json';
        }
    } else if (['m3u8', 'm3u'].includes(ext)) {
        return 'playlist';
    } else if (['txt', 'xml'].includes(ext)) {
        return 'text';
    } else {
        return 'other';
    }
}

export async function onRequestOptions() {
    return new Response(null, {
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type',
            'Access-Control-Max-Age': '86400'
        }
    });
}
