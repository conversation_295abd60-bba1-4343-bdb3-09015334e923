// 测试用户ID获取功能的API端点
export async function onRequestPost({ request, env }) {
    try {
        const { email } = await request.json();
        
        if (!email) {
            return Response.json({ error: '邮箱参数不能为空' }, { status: 400 });
        }

        const result = {
            email: email,
            methods: {},
            finalUserId: null,
            r2StorageCheck: {}
        };

        // 方法1：从用户数据获取
        try {
            const userKey = `user:${email}`;
            const userData = await env.KV.get(userKey, { type: 'json' });
            result.methods.fromUserData = {
                success: !!(userData && userData.userId),
                userId: userData?.userId || null,
                userData: userData
            };
        } catch (error) {
            result.methods.fromUserData = {
                success: false,
                error: error.message
            };
        }

        // 方法2：从email推断
        try {
            const emailParts = email.split('@');
            const potentialUserId = emailParts[0];
            const isValid = potentialUserId.match(/^[a-z0-9]+$/i);
            result.methods.fromEmail = {
                success: !!isValid,
                userId: isValid ? potentialUserId : null,
                emailPart: potentialUserId,
                regexMatch: !!isValid
            };
        } catch (error) {
            result.methods.fromEmail = {
                success: false,
                error: error.message
            };
        }

        // 方法3：从其他任务推断
        try {
            const allUserTasks = await env.KV.list({ prefix: 'task:', limit: 100 });
            const userTasks = [];
            
            for (const key of allUserTasks.keys) {
                try {
                    const task = await env.KV.get(key.name, { type: 'json' });
                    if (task && task.email === email) {
                        userTasks.push({
                            id: task.id,
                            audioUrl: task.audioUrl,
                            status: task.status
                        });
                        
                        if (task.audioUrl) {
                            const match = task.audioUrl.match(/users\/([^\/]+)\/audios/);
                            if (match) {
                                result.methods.fromOtherTasks = {
                                    success: true,
                                    userId: match[1],
                                    sourceUrl: task.audioUrl,
                                    taskId: task.id
                                };
                                break;
                            }
                        }
                    }
                } catch (error) {
                    continue;
                }
            }
            
            if (!result.methods.fromOtherTasks) {
                result.methods.fromOtherTasks = {
                    success: false,
                    userTasksFound: userTasks.length,
                    userTasks: userTasks
                };
            }
        } catch (error) {
            result.methods.fromOtherTasks = {
                success: false,
                error: error.message
            };
        }

        // 方法4：从R2存储推断
        try {
            const usersList = await env.R2.list({
                prefix: 'users/',
                delimiter: '/'
            });
            
            const allUserIds = [];
            if (usersList.delimitedPrefixes) {
                for (const prefix of usersList.delimitedPrefixes) {
                    const userId = prefix.replace('users/', '').replace('/', '');
                    allUserIds.push(userId);
                }
            }

            result.r2StorageCheck = {
                totalUsers: allUserIds.length,
                allUserIds: allUserIds
            };

            // 检查是否有特定任务的文件来确认用户ID
            const testTaskIds = ['1750254844754_dlv7fqlzb', '1750253279691_d5xead751'];
            const foundUserIds = [];

            for (const userId of allUserIds) {
                for (const taskId of testTaskIds) {
                    try {
                        const taskList = await env.R2.list({
                            prefix: `users/${userId}/audios/${taskId}/`,
                            limit: 5
                        });
                        
                        if (taskList.objects && taskList.objects.length > 0) {
                            foundUserIds.push({
                                userId: userId,
                                taskId: taskId,
                                filesFound: taskList.objects.length,
                                files: taskList.objects.map(obj => ({
                                    key: obj.key,
                                    size: obj.size,
                                    lastModified: obj.lastModified
                                }))
                            });
                        }
                    } catch (error) {
                        continue;
                    }
                }
            }

            result.methods.fromR2Storage = {
                success: foundUserIds.length > 0,
                foundUserIds: foundUserIds,
                searchedUserIds: allUserIds
            };

        } catch (error) {
            result.methods.fromR2Storage = {
                success: false,
                error: error.message
            };
        }

        // 确定最终用户ID（调整优先级，优先使用R2存储）
        if (result.methods.fromR2Storage.success && result.methods.fromR2Storage.foundUserIds.length > 0) {
            result.finalUserId = result.methods.fromR2Storage.foundUserIds[0].userId;
            result.finalMethod = 'fromR2Storage';
        } else if (result.methods.fromUserData.success) {
            result.finalUserId = result.methods.fromUserData.userId;
            result.finalMethod = 'fromUserData';
        } else if (result.methods.fromOtherTasks.success) {
            result.finalUserId = result.methods.fromOtherTasks.userId;
            result.finalMethod = 'fromOtherTasks';
        } else if (result.methods.fromEmail.success && !result.methods.fromEmail.userId.match(/^\d+$/)) {
            // 只有当不是纯数字时才使用email推断
            result.finalUserId = result.methods.fromEmail.userId;
            result.finalMethod = 'fromEmail';
        }

        return Response.json({
            success: true,
            result: result
        });

    } catch (error) {
        console.error('测试用户ID获取失败:', error);
        return Response.json({ error: '测试失败: ' + error.message }, { status: 500 });
    }
} 