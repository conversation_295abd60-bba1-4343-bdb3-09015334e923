// 播放列表 API
import { EnhancedR2StorageManager } from '../../../utils/enhanced-r2-storage.js';
import { UserIdResolver } from '../../../utils/user-id-resolver.js';

export async function onRequestGet({ request, env, params }) {
    try {
        const { taskId } = params;
        
        if (!taskId) {
            return Response.json({ error: '缺少任务ID参数' }, { status: 400 });
        }

        console.log(`播放列表API: 开始处理请求 - 任务ID: ${taskId}`);

        // 从请求头获取用户邮箱
        let email = request.headers.get('X-User-Email');
        console.log(`播放列表API: 请求头邮箱 = ${email}`);
        
        // 如果没有请求头，尝试通过任务ID获取用户邮箱
        if (!email) {
            console.log('播放列表API: 请求头中没有邮箱，尝试从任务数据获取');
            const taskKey = `task:${taskId}`;
            const task = await env.KV.get(taskKey, { type: 'json' });
            
            if (!task) {
                console.error(`播放列表API: 任务不存在 - ${taskKey}`);
                return Response.json({ error: '任务不存在' }, { status: 404 });
            }
            
            console.log(`播放列表API: 任务状态 = ${task.status}, 增强版 = ${task.isEnhanced}`);
            
            // 验证任务状态
            if (task.status !== 'SUCCESS') {
                console.error(`播放列表API: 任务未完成 - 状态: ${task.status}`);
                return Response.json({ error: '任务未完成' }, { status: 403 });
            }
            
            // 验证是否为增强版任务
            if (!task.isEnhanced && !task.totalChapters) {
                console.error('播放列表API: 非增强版任务');
                return Response.json({ error: '非增强版任务' }, { status: 403 });
            }
            
            email = task.email;
            console.log(`播放列表API: 从任务获取邮箱 = ${email}`);
        }
        
        if (!email) {
            console.error('播放列表API: 无法确定用户身份');
            return Response.json({ error: '无法确定用户身份' }, { status: 401 });
        }

        // 使用统一的用户ID解析器
        const userIdResolver = new UserIdResolver(env);
        const userIdResult = await userIdResolver.resolveUserId(email, taskId);
        
        if (!userIdResult.userId) {
            console.error(`播放列表API: 无法解析用户ID - 邮箱: ${email}`, userIdResult);
            return Response.json({ 
                error: '无法解析用户ID',
                details: userIdResult
            }, { status: 500 });
        }
        
        console.log(`播放列表API: 用户ID解析成功 - 用户ID: ${userIdResult.userId}, 方法: ${userIdResult.method}, 置信度: ${userIdResult.confidence}%`);

        // 初始化存储管理器
        const r2Storage = new EnhancedR2StorageManager(env.R2);

        console.log(`播放列表API: 尝试获取播放列表 - 邮箱: ${email}, 任务: ${taskId}`);

        // 构建预期路径用于调试
        const expectedPath = `users/${userIdResult.userId}/audiobooks/${taskId}/playlist.json`;
        console.log(`播放列表API: 预期播放列表路径 = ${expectedPath}`);

        // 获取播放列表
        let playlist = await r2Storage.getBookPlaylist(email, taskId);
        
        if (!playlist) {
            console.error(`播放列表API: 播放列表不存在，尝试生成 - 邮箱: ${email}, 任务: ${taskId}`);
            
            // 先检查R2存储中是否有相关文件
            console.log(`播放列表API: 检查R2存储中的文件...`);
            const listResult = await env.R2.list({ prefix: `users/${userIdResult.userId}/audiobooks/${taskId}` });
            console.log(`播放列表API: 找到 ${listResult.objects.length} 个文件:`);
            listResult.objects.forEach(obj => {
                console.log(`  - ${obj.key} (${Math.round(obj.size / 1024)} KB)`);
            });
            
            // 直接尝试读取播放列表文件
            const playlistPath = `users/${userIdResult.userId}/audiobooks/${taskId}/playlist.json`;
            console.log(`播放列表API: 尝试直接读取播放列表文件: ${playlistPath}`);
            
            try {
                const playlistObject = await env.R2.get(playlistPath);
                if (playlistObject) {
                    console.log(`播放列表API: 播放列表文件存在，大小: ${playlistObject.size} 字节`);
                    const playlistContent = await playlistObject.text();
                    console.log(`播放列表API: 播放列表内容前500字符: ${playlistContent.substring(0, 500)}`);
                    
                    try {
                        playlist = JSON.parse(playlistContent);
                        console.log(`播放列表API: 成功解析播放列表JSON，章节数: ${playlist.chapters?.length || 0}`);
                    } catch (parseError) {
                        console.error(`播放列表API: JSON解析失败: ${parseError.message}`);
                        console.log(`播放列表API: 完整内容: ${playlistContent}`);
                    }
                } else {
                    console.error(`播放列表API: 播放列表文件不存在: ${playlistPath}`);
                }
            } catch (directReadError) {
                console.error(`播放列表API: 直接读取播放列表失败: ${directReadError.message}`);
            }
            
            // 如果直接读取也失败，尝试从元数据重新生成播放列表
            if (!playlist) {
                const metadata = await r2Storage.getBookMetadata(email, taskId);
                if (metadata && metadata.chapters) {
                    console.log(`播放列表API: 找到元数据，尝试重新生成播放列表 - 章节数: ${metadata.chapters.length}`);
                    
                    // 重新生成播放列表
                    const basePath = `users/${userIdResult.userId}/audiobooks/${taskId}`;
                    
                    const jsonPlaylist = {
                        title: metadata.title || '未知标题',
                        totalChapters: metadata.chapters.length,
                        totalDuration: metadata.totalDuration || 0,
                        chapters: metadata.chapters.map((chapter, index) => ({
                            index: index + 1,
                            title: chapter.title || `第${index + 1}章`,
                            duration: chapter.duration || 0,
                            url: `/api/audio/chapter/${taskId}/${index + 1}.mp3`,
                            wordCount: chapter.wordCount || 0,
                            isPartial: chapter.isPartial || false,
                            originalTitle: chapter.originalTitle || chapter.title,
                            partIndex: chapter.partIndex || 1,
                            totalParts: chapter.totalParts || 1
                        }))
                    };

                    // 保存播放列表
                    const jsonPlaylistPath = `${basePath}/playlist.json`;
                    await env.R2.put(jsonPlaylistPath, JSON.stringify(jsonPlaylist, null, 2), {
                        httpMetadata: {
                            contentType: 'application/json'
                        }
                    });

                    console.log(`播放列表API: 播放列表重新生成成功`);
                    playlist = jsonPlaylist;
                } else {
                    console.error(`播放列表API: 无法找到元数据或章节信息`);
                    
                    // 提供更详细的错误信息
                    const metadataPath = `users/${userIdResult.userId}/audiobooks/${taskId}/metadata.json`;
                    const metadataExists = await env.R2.head(metadataPath);
                    console.log(`播放列表API: 元数据文件存在: ${!!metadataExists}`);
                    
                    if (metadataExists) {
                        try {
                            const metadataObj = await env.R2.get(metadataPath);
                            const metadataContent = await metadataObj.text();
                            console.log(`播放列表API: 元数据内容: ${metadataContent.substring(0, 500)}...`);
                        } catch (e) {
                            console.error(`播放列表API: 读取元数据失败: ${e.message}`);
                        }
                    }
                    
                    return Response.json({ 
                        error: '播放列表不存在且无法重新生成',
                        debug: {
                            taskId,
                            email,
                            userId: userIdResult.userId,
                            userIdMethod: userIdResult.method,
                            userIdConfidence: userIdResult.confidence,
                            expectedPath,
                            filesFound: listResult.objects.length,
                            metadataExists: !!metadataExists
                        }
                    }, { status: 404 });
                }
            }
        }

        console.log(`播放列表API: 成功获取播放列表 - 章节数: ${playlist.chapters?.length || 0}`);

        // 添加用户邮箱到每个章节URL的请求头中
        const enhancedPlaylist = {
            ...playlist,
            chapters: playlist.chapters.map(chapter => ({
                ...chapter,
                url: chapter.url,
                // 为前端提供额外的元数据
                metadata: {
                    estimatedDuration: `${Math.ceil((chapter.duration || 0) / 60)} 分钟`,
                    wordCount: chapter.wordCount || 0,
                    isPartial: chapter.isPartial || false
                }
            })),
            // 添加用户ID解析信息用于调试
            debug: {
                userId: userIdResult.userId,
                userIdMethod: userIdResult.method,
                userIdConfidence: userIdResult.confidence
            }
        };

        return Response.json(enhancedPlaylist, {
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, OPTIONS',
                'Access-Control-Allow-Headers': 'X-User-Email',
                'Cache-Control': 'public, max-age=300' // 5分钟缓存
            }
        });

    } catch (error) {
        console.error('播放列表API: 获取播放列表失败:', error);
        return Response.json({ 
            error: '服务器内部错误',
            details: error.message 
        }, { status: 500 });
    }
}

// 处理OPTIONS请求（CORS预检）
export async function onRequestOptions() {
    return new Response(null, {
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, OPTIONS',
            'Access-Control-Allow-Headers': 'X-User-Email',
            'Access-Control-Max-Age': '86400'
        }
    });
} 