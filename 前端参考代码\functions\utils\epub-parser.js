/**
 * EPUB解析器 - 借鉴epub_extractor.py的思路
 * 正确解析EPUB文件结构，提取章节内容
 * 增强版：添加详细日志、超时机制和进度反馈
 */

export class EpubParser {
    constructor() {
        this.mimeTypes = {
            XHTML: 'application/xhtml+xml',
            HTML: 'text/html',
            NCX: 'application/x-dtbncx+xml',
            OPF: 'application/oebps-package+xml'
        };
        
        // 超时配置
        this.timeouts = {
            totalParsing: 10 * 60 * 1000, // 总解析超时：10分钟
            singleDecompression: 30 * 1000, // 单个解压缩超时：30秒
            zipStructureParsing: 60 * 1000, // ZIP结构解析超时：1分钟
            textExtraction: 120 * 1000 // 文本提取超时：2分钟
        };
    }

    /**
     * 解析EPUB文件，提取章节内容
     * @param {ArrayBuffer} epubBuffer - EPUB文件的ArrayBuffer
     * @returns {Promise<Array>} 章节数组
     */
    async parseEpub(epubBuffer) {
        const startTime = Date.now();
        console.log('=== EPUB解析开始 ===');
        console.log(`文件大小: ${epubBuffer.byteLength} bytes`);
        
        try {
            // 设置总体超时
            return await this.withTimeout(
                this._parseEpubInternal(epubBuffer, startTime),
                this.timeouts.totalParsing,
                'EPUB解析总体超时'
            );
        } catch (error) {
            const duration = Date.now() - startTime;
            console.error(`=== EPUB解析失败 (耗时: ${Math.round(duration/1000)}秒) ===`);
            console.error('错误详情:', error);
            throw new Error(`EPUB解析失败: ${error.message}`);
        }
    }

    /**
     * 内部解析逻辑
     */
    async _parseEpubInternal(epubBuffer, startTime) {
        // 1. 解析ZIP结构
        console.log('📁 步骤1: 解析ZIP文件结构...');
        const zipStartTime = Date.now();
        
        const zipEntries = await this.withTimeout(
            this.parseZipStructure(epubBuffer),
            this.timeouts.zipStructureParsing,
            'ZIP结构解析超时'
        );
        
        const zipDuration = Date.now() - zipStartTime;
        console.log(`✅ ZIP结构解析完成 (耗时: ${zipDuration}ms)`);
        console.log(`📊 找到 ${zipEntries.length} 个ZIP条目`);
        
        // 调试：列出所有文件
        console.log('📋 ZIP文件列表:');
        zipEntries.forEach((entry, index) => {
            console.log(`  ${index + 1}. ${entry.filename} (${entry.uncompressedSize} bytes, 压缩: ${entry.compressedSize} bytes)`);
        });
        
        // 2. 查找并解析OPF文件
        console.log('🔍 步骤2: 查找OPF文件...');
        const opfStartTime = Date.now();
        
        const opfEntry = await this.findOpfFile(zipEntries);
        if (!opfEntry) {
            throw new Error('未找到OPF文件，这可能不是有效的EPUB文件');
        }
        
        console.log(`✅ 找到OPF文件: ${opfEntry.filename}`);
        
        const opfContent = await this.withTimeout(
            this.extractTextFromZipEntry(opfEntry, 'OPF文件'),
            this.timeouts.textExtraction,
            'OPF文件提取超时'
        );
        
        const opfDuration = Date.now() - opfStartTime;
        console.log(`✅ OPF文件解析完成 (耗时: ${opfDuration}ms)`);
        console.log(`📄 OPF文件内容长度: ${opfContent.length} 字符`);
        console.log(`📖 OPF文件内容预览: ${opfContent.substring(0, 300)}...`);
        
        const manifest = this.parseOpfManifest(opfContent);
        console.log(`📚 从OPF文件中找到 ${manifest.length} 个内容项目`);
        
        // 3. 提取所有XHTML/HTML文档
        console.log('📝 步骤3: 提取章节内容...');
        const chapters = [];
        let chapterNum = 1;
        const totalContentDocs = manifest.filter(item => this.isContentDocument(item.mediaType)).length;
        
        console.log(`📊 需要处理 ${totalContentDocs} 个内容文档`);
        
        for (let i = 0; i < manifest.length; i++) {
            const item = manifest[i];
            
            if (this.isContentDocument(item.mediaType)) {
                const docStartTime = Date.now();
                console.log(`📄 处理文档 ${chapterNum}/${totalContentDocs}: ${item.href} (${item.mediaType})`);
                
                const docEntry = this.findDocumentEntry(zipEntries, item.href, opfEntry.filename);
                
                if (docEntry) {
                    try {
                        const htmlContent = await this.withTimeout(
                            this.extractTextFromZipEntry(docEntry, `章节${chapterNum}`),
                            this.timeouts.textExtraction,
                            `章节${chapterNum}文本提取超时`
                        );
                        
                        const cleanText = this.extractTextFromHtml(htmlContent);
                        const docDuration = Date.now() - docStartTime;
                        
                        if (cleanText.trim().length > 50) {
                            chapters.push({
                                chapterNum: chapterNum,
                                title: item.title || this.extractTitleFromHtml(htmlContent) || `第${chapterNum}章`,
                                filename: `chapter_${chapterNum.toString().padStart(3, '0')}.html`,
                                htmlContent: htmlContent,
                                textContent: cleanText,
                                href: item.href,
                                mediaType: item.mediaType
                            });
                            
                            console.log(`✅ 章节 ${chapterNum} 提取成功 (耗时: ${docDuration}ms)`);
                            console.log(`   标题: ${item.title || '无标题'}`);
                            console.log(`   文本长度: ${cleanText.length} 字符`);
                            console.log(`   预览: ${cleanText.substring(0, 100)}...`);
                            chapterNum++;
                        } else {
                            console.log(`⚠️  跳过过短的文档: ${item.href} (${cleanText.length}字符, 耗时: ${docDuration}ms)`);
                        }
                    } catch (error) {
                        const docDuration = Date.now() - docStartTime;
                        console.warn(`❌ 处理文档 ${item.href} 时出错 (耗时: ${docDuration}ms):`, error.message);
                        continue;
                    }
                } else {
                    console.warn(`⚠️  未找到文档文件: ${item.href}`);
                }
                
                // 报告进度
                const progress = Math.round((chapterNum - 1) / totalContentDocs * 100);
                console.log(`📊 章节提取进度: ${progress}% (${chapterNum - 1}/${totalContentDocs})`);
            }
        }
        
        if (chapters.length === 0) {
            throw new Error('未能从EPUB文件中提取到任何章节内容');
        }
        
        const totalDuration = Date.now() - startTime;
        console.log('=== EPUB解析完成 ===');
        console.log(`✅ 总耗时: ${Math.round(totalDuration/1000)}秒`);
        console.log(`📚 共提取 ${chapters.length} 个章节`);
        console.log(`📊 总文本长度: ${chapters.reduce((sum, ch) => sum + ch.textContent.length, 0)} 字符`);
        
        return chapters;
    }

    /**
     * 超时包装器
     */
    async withTimeout(promise, timeoutMs, timeoutMessage) {
        return Promise.race([
            promise,
            new Promise((_, reject) => {
                setTimeout(() => {
                    reject(new Error(`${timeoutMessage} (${timeoutMs}ms)`));
                }, timeoutMs);
            })
        ]);
    }

    /**
     * 解析ZIP文件结构
     * @param {ArrayBuffer} buffer - ZIP文件buffer
     * @returns {Array} ZIP条目数组
     */
    async parseZipStructure(buffer) {
        const view = new DataView(buffer);
        const entries = [];
        
        // 查找中央目录结束记录
        let eocdOffset = -1;
        for (let i = buffer.byteLength - 22; i >= 0; i--) {
            if (view.getUint32(i, true) === 0x06054b50) {
                eocdOffset = i;
                break;
            }
        }
        
        if (eocdOffset === -1) {
            throw new Error('无效的ZIP文件：未找到中央目录结束记录');
        }
        
        // 读取中央目录信息
        const centralDirEntries = view.getUint16(eocdOffset + 10, true);
        const centralDirOffset = view.getUint32(eocdOffset + 16, true);
        
        // 解析中央目录条目
        let offset = centralDirOffset;
        for (let i = 0; i < centralDirEntries; i++) {
            if (view.getUint32(offset, true) !== 0x02014b50) {
                break;
            }
            
            const filenameLength = view.getUint16(offset + 28, true);
            const extraFieldLength = view.getUint16(offset + 30, true);
            const commentLength = view.getUint16(offset + 32, true);
            const localHeaderOffset = view.getUint32(offset + 42, true);
            const compressedSize = view.getUint32(offset + 20, true);
            const uncompressedSize = view.getUint32(offset + 24, true);
            
            // 读取文件名
            const filenameBytes = new Uint8Array(buffer, offset + 46, filenameLength);
            const filename = new TextDecoder('utf-8').decode(filenameBytes);
            
            entries.push({
                filename: filename,
                localHeaderOffset: localHeaderOffset,
                compressedSize: compressedSize,
                uncompressedSize: uncompressedSize,
                buffer: buffer
            });
            
            offset += 46 + filenameLength + extraFieldLength + commentLength;
        }
        
        return entries;
    }

    /**
     * 从ZIP条目中提取文本内容
     * @param {Object} entry - ZIP条目
     * @param {string} entryName - 条目名称（用于日志）
     * @returns {string} 文本内容
     */
    async extractTextFromZipEntry(entry, entryName = '未知文件') {
        const extractStartTime = Date.now();
        console.log(`🔧 开始提取: ${entryName} (${entry.filename})`);
        
        const view = new DataView(entry.buffer);
        const localHeaderOffset = entry.localHeaderOffset;
        
        // 读取本地文件头
        if (view.getUint32(localHeaderOffset, true) !== 0x04034b50) {
            throw new Error(`${entryName}: 无效的本地文件头`);
        }
        
        const filenameLength = view.getUint16(localHeaderOffset + 26, true);
        const extraFieldLength = view.getUint16(localHeaderOffset + 28, true);
        const compressionMethod = view.getUint16(localHeaderOffset + 8, true);
        
        const dataOffset = localHeaderOffset + 30 + filenameLength + extraFieldLength;
        
        console.log(`📋 ${entryName} 文件信息:`);
        console.log(`   压缩方法: ${compressionMethod} (0=无压缩, 8=Deflate)`);
        console.log(`   压缩大小: ${entry.compressedSize} bytes`);
        console.log(`   未压缩大小: ${entry.uncompressedSize} bytes`);
        console.log(`   数据偏移: ${dataOffset}`);

        try {
            let result;
            
            if (compressionMethod === 0) {
                // 无压缩
                console.log(`✅ ${entryName}: 文件未压缩，直接读取`);
                const data = new Uint8Array(entry.buffer, dataOffset, entry.uncompressedSize);
                result = new TextDecoder('utf-8').decode(data);
            } else if (compressionMethod === 8) {
                // Deflate压缩
                console.log(`🔄 ${entryName}: 开始Deflate解压缩`);
                const compressedData = new Uint8Array(entry.buffer, dataOffset, entry.compressedSize);
                
                // 使用超时机制进行解压缩
                const decompressedData = await this.withTimeout(
                    this.tryMultipleDecompressionMethods(compressedData, entry.uncompressedSize, entryName),
                    this.timeouts.singleDecompression,
                    `${entryName} 解压缩超时`
                );
                
                result = new TextDecoder('utf-8', { fatal: false }).decode(decompressedData);
            } else {
                // 其他压缩方法
                console.warn(`⚠️  ${entryName}: 不支持的压缩方法 ${compressionMethod}，尝试直接解码`);
                const data = new Uint8Array(entry.buffer, dataOffset, entry.compressedSize);
                result = new TextDecoder('utf-8', { fatal: false }).decode(data);
            }
            
            const extractDuration = Date.now() - extractStartTime;
            console.log(`✅ ${entryName} 提取完成:`);
            console.log(`   耗时: ${extractDuration}ms`);
            console.log(`   文本长度: ${result.length} 字符`);
            console.log(`   文本预览: ${result.substring(0, 150)}...`);
            
            return result;
            
        } catch (error) {
            const extractDuration = Date.now() - extractStartTime;
            console.error(`❌ ${entryName} 提取失败 (耗时: ${extractDuration}ms):`, error.message);
            throw new Error(`${entryName} 提取失败: ${error.message}`);
        }
    }

    /**
     * 尝试多种解压缩方法（增强版）
     * @param {Uint8Array} compressedData - 压缩数据
     * @param {number} uncompressedSize - 未压缩大小
     * @param {string} entryName - 条目名称
     * @returns {Uint8Array} 解压缩后的数据
     */
    async tryMultipleDecompressionMethods(compressedData, uncompressedSize, entryName) {
        console.log(`🔧 ${entryName}: 尝试多种解压缩方法`);
        
        const methods = [
            {
                name: 'DecompressionStream(deflate)',
                method: () => this.useDecompressionStream(compressedData, 'deflate')
            },
            {
                name: 'DecompressionStream(deflate-raw)',
                method: () => this.useDecompressionStreamRaw(compressedData)
            },
            {
                name: 'DecompressionStream(gzip)',
                method: () => this.useDecompressionStreamGzip(compressedData)
            },
            {
                name: '简化inflate',
                method: () => this.simpleInflate(compressedData, uncompressedSize)
            }
        ];

        for (let i = 0; i < methods.length; i++) {
            const methodInfo = methods[i];
            const methodStartTime = Date.now();
            
            try {
                console.log(`🔄 ${entryName}: 尝试方法 ${i + 1}/${methods.length} - ${methodInfo.name}`);
                
                const result = await this.withTimeout(
                    methodInfo.method(),
                    this.timeouts.singleDecompression / 2, // 每个方法的超时时间减半
                    `${methodInfo.name} 超时`
                );
                
                const methodDuration = Date.now() - methodStartTime;
                
                if (result && result.length > 0) {
                    // 验证结果
                    const text = new TextDecoder('utf-8', { fatal: false }).decode(result.slice(0, Math.min(500, result.length)));
                    console.log(`📊 ${entryName}: 方法 ${i + 1} 结果验证:`);
                    console.log(`   耗时: ${methodDuration}ms`);
                    console.log(`   输出大小: ${result.length} bytes`);
                    console.log(`   文本预览: ${text.substring(0, 100)}...`);
                    
                    if (this.isValidDecompressedContent(text)) {
                        console.log(`✅ ${entryName}: 方法 ${i + 1} (${methodInfo.name}) 成功！`);
                        return result;
                    } else {
                        console.log(`⚠️  ${entryName}: 方法 ${i + 1} 结果验证失败，尝试下一个方法`);
                    }
                } else {
                    console.log(`⚠️  ${entryName}: 方法 ${i + 1} 返回空结果`);
                }
            } catch (error) {
                const methodDuration = Date.now() - methodStartTime;
                console.warn(`❌ ${entryName}: 方法 ${i + 1} (${methodInfo.name}) 失败 (耗时: ${methodDuration}ms): ${error.message}`);
            }
        }

        throw new Error(`${entryName}: 所有解压缩方法都失败了`);
    }

    /**
     * 使用DecompressionStream (deflate)
     */
    async useDecompressionStream(compressedData, format = 'deflate') {
        if (typeof DecompressionStream === 'undefined') {
            throw new Error('DecompressionStream API不可用（可能在旧版浏览器或Node.js环境中）');
        }

        console.log(`🔧 使用DecompressionStream(${format})，数据大小: ${compressedData.length} bytes`);
        
        try {
            const readable = new ReadableStream({
                start(controller) {
                    controller.enqueue(compressedData);
                    controller.close();
                }
            });

            const decompressedStream = readable.pipeThrough(new DecompressionStream(format));
            const result = await this.readStreamToUint8Array(decompressedStream);
            
            console.log(`✅ DecompressionStream(${format}) 成功，输出大小: ${result.length} bytes`);
            return result;
        } catch (error) {
            console.warn(`❌ DecompressionStream(${format}) 失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 使用DecompressionStream (deflate-raw)
     */
    async useDecompressionStreamRaw(compressedData) {
        return this.useDecompressionStream(compressedData, 'deflate-raw');
    }

    /**
     * 使用DecompressionStream (gzip)
     */
    async useDecompressionStreamGzip(compressedData) {
        return this.useDecompressionStream(compressedData, 'gzip');
    }

    /**
     * 从流中读取数据到Uint8Array（增强版）
     */
    async readStreamToUint8Array(stream) {
        console.log('🔄 开始从流中读取数据...');
        const startTime = Date.now();
        
        try {
            const reader = stream.getReader();
            const chunks = [];
            let totalLength = 0;
            let chunkCount = 0;

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;
                
                chunks.push(value);
                totalLength += value.length;
                chunkCount++;
                
                // 每100个chunk报告一次进度
                if (chunkCount % 100 === 0) {
                    console.log(`📊 已读取 ${chunkCount} 个数据块，总大小: ${totalLength} bytes`);
                }
            }

            const result = new Uint8Array(totalLength);
            let offset = 0;
            for (const chunk of chunks) {
                result.set(chunk, offset);
                offset += chunk.length;
            }

            const duration = Date.now() - startTime;
            console.log(`✅ 流读取完成: ${chunkCount} 个块，${totalLength} bytes，耗时: ${duration}ms`);
            
            return result;
        } catch (error) {
            const duration = Date.now() - startTime;
            console.error(`❌ 流读取失败 (耗时: ${duration}ms): ${error.message}`);
            throw error;
        }
    }

    /**
     * 验证解压缩内容是否有效（增强版）
     */
    isValidDecompressedContent(text) {
        if (!text || text.length < 10) {
            console.log('❌ 内容验证失败: 文本过短或为空');
            return false;
        }
        
        // 检查是否包含XML/HTML标记
        const hasXmlHtml = text.includes('<') && (
            text.includes('xml') || 
            text.includes('html') || 
            text.includes('manifest') || 
            text.includes('item') ||
            text.includes('package') ||
            text.includes('metadata')
        );
        
        if (hasXmlHtml) {
            console.log('✅ 内容验证通过: 检测到XML/HTML结构');
            return true;
        }
        
        // 检查是否包含可读的文本内容
        const readableChars = text.match(/[a-zA-Z\u4e00-\u9fff]/g);
        const readableRatio = readableChars ? readableChars.length / text.length : 0;
        
        if (readableRatio > 0.1) {
            console.log(`✅ 内容验证通过: 可读字符比例 ${Math.round(readableRatio * 100)}%`);
            return true;
        }
        
        console.log(`❌ 内容验证失败: 可读字符比例仅 ${Math.round(readableRatio * 100)}%`);
        return false;
    }

    /**
     * 简化的inflate实现（降级方案）- 增强版
     * @param {Uint8Array} compressedData - 压缩数据
     * @param {number} expectedSize - 期望的解压缩大小
     * @returns {Uint8Array} 解压缩后的数据
     */
    simpleInflate(compressedData, expectedSize) {
        console.log('🔧 使用简化inflate作为降级方案');
        console.log(`   输入大小: ${compressedData.length} bytes`);
        console.log(`   期望输出大小: ${expectedSize} bytes`);
        
        // 检查是否有zlib头部
        let dataStart = 0;
        if (compressedData.length >= 2) {
            const header = (compressedData[0] << 8) | compressedData[1];
            // 检查zlib头部标识
            if ((header & 0x0f00) === 0x0800 && (header % 31) === 0) {
                console.log('📋 检测到zlib头部，跳过前2字节');
                dataStart = 2;
            }
        }
        
        // 简单策略：查找可能的文本内容
        const input = compressedData.slice(dataStart);
        const output = [];
        let validCharCount = 0;
        
        console.log(`🔄 开始简化解压缩，处理 ${input.length} bytes...`);
        
        // 查找可能的文本模式
        for (let i = 0; i < input.length; i++) {
            const byte = input[i];
            
            // 如果是可打印ASCII字符或常见控制字符
            if ((byte >= 32 && byte <= 126) || byte === 10 || byte === 13 || byte === 9) {
                output.push(byte);
                validCharCount++;
            }
            // 如果是UTF-8字符的开始
            else if (byte >= 0xC0 && byte <= 0xFD) {
                output.push(byte);
                validCharCount++;
                // 尝试复制后续字节
                let seqLen = 1;
                if (byte >= 0xE0) seqLen = 2;
                if (byte >= 0xF0) seqLen = 3;
                
                for (let j = 0; j < seqLen && i + 1 < input.length; j++) {
                    const nextByte = input[i + 1];
                    if (nextByte >= 0x80 && nextByte <= 0xBF) {
                        output.push(nextByte);
                        i++;
                    } else {
                        break;
                    }
                }
            }
            // 如果是UTF-8的后续字节
            else if (byte >= 0x80 && byte <= 0xBF) {
                output.push(byte);
            }
            
            // 每处理10000字节报告一次进度
            if (i % 10000 === 0 && i > 0) {
                const progress = Math.round(i / input.length * 100);
                console.log(`📊 简化解压缩进度: ${progress}% (${i}/${input.length})`);
            }
        }
        
        const result = new Uint8Array(output);
        const validRatio = validCharCount / result.length;
        
        console.log(`✅ 简化inflate完成:`);
        console.log(`   输出大小: ${result.length} bytes`);
        console.log(`   有效字符数: ${validCharCount}`);
        console.log(`   有效字符比例: ${Math.round(validRatio * 100)}%`);
        
        if (result.length === 0) {
            throw new Error('简化inflate未能提取到任何内容');
        }
        
        return result;
    }

    /**
     * 查找OPF文件
     * @param {Array} zipEntries - ZIP条目数组
     * @returns {Object|null} OPF文件条目
     */
    async findOpfFile(zipEntries) {
        // 首先查找container.xml文件
        const containerEntry = zipEntries.find(entry => 
            entry.filename === 'META-INF/container.xml'
        );
        
        if (containerEntry) {
            try {
                const containerXml = await this.extractTextFromZipEntry(containerEntry);
                const opfPathMatch = containerXml.match(/full-path\s*=\s*["']([^"']+)["']/);
                if (opfPathMatch) {
                    const opfPath = opfPathMatch[1];
                    return zipEntries.find(entry => entry.filename === opfPath);
                }
            } catch (error) {
                console.warn('解析container.xml失败:', error.message);
            }
        }
        
        // 如果找不到container.xml，直接查找.opf文件
        return zipEntries.find(entry => entry.filename.endsWith('.opf'));
    }

    /**
     * 查找文档条目（支持相对路径解析）
     * @param {Array} zipEntries - ZIP条目数组
     * @param {string} href - 文档href
     * @param {string} opfPath - OPF文件路径
     * @returns {Object|null} 文档条目
     */
    findDocumentEntry(zipEntries, href, opfPath) {
        // 首先尝试直接匹配
        let docEntry = zipEntries.find(entry => entry.filename === href);
        if (docEntry) return docEntry;
        
        // 尝试相对于OPF文件的路径
        const opfDir = opfPath.substring(0, opfPath.lastIndexOf('/') + 1);
        const fullPath = opfDir + href;
        docEntry = zipEntries.find(entry => entry.filename === fullPath);
        if (docEntry) return docEntry;
        
        // 尝试只匹配文件名
        const filename = href.split('/').pop();
        docEntry = zipEntries.find(entry => entry.filename.endsWith(filename));
        if (docEntry) return docEntry;
        
        // 尝试包含匹配
        docEntry = zipEntries.find(entry => entry.filename.includes(href));
        if (docEntry) return docEntry;
        
        return null;
    }

    /**
     * 从HTML内容中提取标题
     * @param {string} htmlContent - HTML内容
     * @returns {string} 标题
     */
    extractTitleFromHtml(htmlContent) {
        // 尝试提取title标签
        const titleMatch = htmlContent.match(/<title[^>]*>([^<]+)<\/title>/i);
        if (titleMatch) {
            return titleMatch[1].trim();
        }
        
        // 尝试提取h1标签
        const h1Match = htmlContent.match(/<h1[^>]*>([^<]+)<\/h1>/i);
        if (h1Match) {
            return h1Match[1].replace(/<[^>]*>/g, '').trim();
        }
        
        // 尝试提取h2标签
        const h2Match = htmlContent.match(/<h2[^>]*>([^<]+)<\/h2>/i);
        if (h2Match) {
            return h2Match[1].replace(/<[^>]*>/g, '').trim();
        }
        
        return '';
    }

    /**
     * 解析OPF文件的manifest部分（增强版）
     * @param {string} opfContent - OPF文件内容
     * @returns {Array} manifest项目数组
     */
    parseOpfManifest(opfContent) {
        const manifest = [];
        
        console.log('开始解析OPF manifest...');
        
        // 清理OPF内容，移除命名空间前缀可能造成的问题
        let cleanedContent = opfContent
            .replace(/\s+/g, ' ')  // 规范化空白字符
            .replace(/\n/g, ' ')   // 移除换行符
            .trim();
        
        // 尝试多种manifest匹配模式
        const manifestPatterns = [
            /<manifest[^>]*>([\s\S]*?)<\/manifest>/i,
            /<[^:]*:manifest[^>]*>([\s\S]*?)<\/[^:]*:manifest>/i,  // 带命名空间
            /<manifest[^>]*\/>/i,  // 自闭合标签
        ];
        
        let manifestMatch = null;
        let manifestContent = '';
        
        for (const pattern of manifestPatterns) {
            manifestMatch = cleanedContent.match(pattern);
            if (manifestMatch) {
                manifestContent = manifestMatch[1] || manifestMatch[0];
                console.log(`使用模式匹配到manifest: ${pattern.toString()}`);
                break;
            }
        }
        
        if (!manifestMatch) {
            // 如果还是找不到，尝试更宽松的搜索
            console.log('尝试宽松的manifest搜索...');
            const manifestStart = cleanedContent.toLowerCase().indexOf('<manifest');
            const manifestEnd = cleanedContent.toLowerCase().indexOf('</manifest>');
            
            if (manifestStart !== -1 && manifestEnd !== -1) {
                manifestContent = cleanedContent.substring(manifestStart, manifestEnd + 11);
                console.log('使用位置搜索找到manifest');
            } else {
                // 最后的尝试：查找所有item标签
                console.log('直接搜索item标签...');
                const itemMatches = cleanedContent.match(/<item[^>]*>/gi) || [];
                if (itemMatches.length > 0) {
                    manifestContent = itemMatches.join(' ');
                    console.log(`直接找到 ${itemMatches.length} 个item标签`);
                } else {
                    console.error('OPF内容调试信息:');
                    console.error('内容长度:', cleanedContent.length);
                    console.error('内容预览:', cleanedContent.substring(0, 1000));
                    console.error('是否包含manifest:', cleanedContent.toLowerCase().includes('manifest'));
                    console.error('是否包含item:', cleanedContent.toLowerCase().includes('item'));
                    throw new Error('OPF文件中未找到manifest部分');
                }
            }
        }
        
        console.log(`Manifest内容长度: ${manifestContent.length}`);
        console.log(`Manifest内容预览: ${manifestContent.substring(0, 300)}...`);
        
        // 解析每个item，支持多种格式
        const itemPatterns = [
            /<item[^>]*>/gi,
            /<[^:]*:item[^>]*>/gi,  // 带命名空间
        ];
        
        let itemMatches = [];
        for (const pattern of itemPatterns) {
            itemMatches = manifestContent.match(pattern) || [];
            if (itemMatches.length > 0) {
                console.log(`使用模式找到 ${itemMatches.length} 个item: ${pattern.toString()}`);
                break;
            }
        }
        
        console.log(`找到 ${itemMatches.length} 个item标签`);
        
        for (const itemMatch of itemMatches) {
            console.log(`解析item: ${itemMatch}`);
            
            const idMatch = itemMatch.match(/id\s*=\s*["']([^"']+)["']/i);
            const hrefMatch = itemMatch.match(/href\s*=\s*["']([^"']+)["']/i);
            const mediaTypeMatch = itemMatch.match(/media-type\s*=\s*["']([^"']+)["']/i);
            
            if (hrefMatch && mediaTypeMatch) {
                const item = {
                    id: idMatch ? idMatch[1] : '',
                    href: hrefMatch[1],
                    mediaType: mediaTypeMatch[1],
                    title: '' // 标题需要从实际内容中提取
                };
                
                manifest.push(item);
                console.log(`添加manifest项目: ${item.href} (${item.mediaType})`);
            } else {
                console.warn(`item标签缺少必要属性: ${itemMatch}`);
            }
        }
        
        if (manifest.length === 0) {
            throw new Error('manifest中没有找到有效的item项目');
        }
        
        console.log(`解析完成，共找到 ${manifest.length} 个manifest项目`);
        return manifest;
    }

    /**
     * 判断是否为内容文档
     * @param {string} mediaType - MIME类型
     * @returns {boolean}
     */
    isContentDocument(mediaType) {
        return mediaType === this.mimeTypes.XHTML || 
               mediaType === this.mimeTypes.HTML ||
               mediaType === 'text/html';
    }

    /**
     * 从HTML内容中提取纯文本
     * @param {string} htmlContent - HTML内容
     * @returns {string} 纯文本
     */
    extractTextFromHtml(htmlContent) {
        let text = htmlContent;
        
        // 去除EPUB特有的标记和元数据
        text = text.replace(/<\?xml[^>]*\?>/gi, '');
        text = text.replace(/<!DOCTYPE[^>]*>/gi, '');
        text = text.replace(/<meta[^>]*>/gi, '');
        text = text.replace(/<link[^>]*>/gi, '');
        text = text.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '');
        text = text.replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '');
        
        // 保留段落结构但去除其他HTML标签
        text = text.replace(/<\/p>/gi, '\n\n');
        text = text.replace(/<\/div>/gi, '\n');
        text = text.replace(/<br[^>]*>/gi, '\n');
        text = text.replace(/<\/h[1-6]>/gi, '\n\n');
        
        // 去除所有HTML标签
        text = text.replace(/<[^>]*>/g, ' ');
        
        // 解码HTML实体
        text = text.replace(/&nbsp;/g, ' ')
                  .replace(/&lt;/g, '<')
                  .replace(/&gt;/g, '>')
                  .replace(/&amp;/g, '&')
                  .replace(/&quot;/g, '"')
                  .replace(/&#39;/g, "'")
                  .replace(/&hellip;/g, '...')
                  .replace(/&mdash;/g, '—')
                  .replace(/&ndash;/g, '–')
                  .replace(/&#8220;/g, '"')
                  .replace(/&#8221;/g, '"')
                  .replace(/&#8216;/g, "'")
                  .replace(/&#8217;/g, "'")
                  .replace(/&#12288;/g, '　')  // 中文全角空格
                  .replace(/&#65292;/g, '，')  // 中文逗号
                  .replace(/&#65306;/g, '：')  // 中文冒号
                  .replace(/&#65281;/g, '！')  // 中文感叹号
                  .replace(/&#65311;/g, '？')  // 中文问号
                  .replace(/&#65288;/g, '（')  // 中文左括号
                  .replace(/&#65289;/g, '）'); // 中文右括号
        
        // 清理多余空白
        text = text
            .replace(/\r\n/g, '\n')
            .replace(/\r/g, '\n')
            .replace(/\n{3,}/g, '\n\n')
            .replace(/[ \t]+/g, ' ')
            .trim();
        
        return text;
    }

    /**
     * 将章节保存为HTML文件（模拟epub_extractor.py的行为）
     * @param {Array} chapters - 章节数组
     * @param {string} bookTitle - 书籍标题
     * @returns {Object} 包含所有章节的对象
     */
    createHtmlChapters(chapters, bookTitle) {
        const htmlChapters = {};
        
        chapters.forEach(chapter => {
            htmlChapters[chapter.filename] = {
                title: chapter.title,
                content: chapter.htmlContent,
                textContent: chapter.textContent,
                chapterNum: chapter.chapterNum
            };
        });
        
        return {
            bookTitle: bookTitle,
            chapters: htmlChapters,
            totalChapters: chapters.length
        };
    }
} 