/**
 * 音频文件访问端点
 * 根据任务ID提供音频文件流
 */
import { R2StorageManager } from '../../utils/r2-storage.js';

export async function onRequestGet({ request, env, params }) {
    try {
        const taskId = params.taskId;
        
        if (!taskId) {
            return Response.json({ error: '任务ID不能为空' }, { status: 400 });
        }

        // 获取任务信息
        const taskKey = `task:${taskId}`;
        const task = await env.KV.get(taskKey, { type: 'json' });
        
        if (!task) {
            return Response.json({ error: '任务不存在' }, { status: 404 });
        }

        if (task.status !== 'SUCCESS') {
            return Response.json({ error: '任务未完成或失败' }, { status: 400 });
        }

        if (!task.audioPath) {
            return Response.json({ error: '音频文件不存在' }, { status: 404 });
        }

        // 从R2存储获取音频文件
        const r2Storage = new R2StorageManager(env.R2);
        const audioObject = await r2Storage.getFile(task.audioPath);
        
        if (!audioObject) {
            return Response.json({ error: '音频文件未找到' }, { status: 404 });
        }

        // 返回音频文件流
        return new Response(audioObject.body, {
            headers: {
                'Content-Type': 'audio/mpeg',
                'Content-Length': audioObject.size.toString(),
                'Cache-Control': 'public, max-age=31536000', // 缓存1年
                'Accept-Ranges': 'bytes',
                'Content-Disposition': `inline; filename="${task.filename}.mp3"`
            }
        });

    } catch (error) {
        console.error('获取音频文件失败:', error);
        return Response.json({ error: '获取音频文件失败' }, { status: 500 });
    }
} 