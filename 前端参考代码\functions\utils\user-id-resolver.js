// 通用用户ID解析器 - 解决用户ID获取不一致的问题
export class UserIdResolver {
    constructor(env) {
        this.env = env;
        this.cache = new Map(); // 缓存已解析的用户ID
    }

    /**
     * 生成邮箱哈希值 - 与R2StorageManager保持一致
     */
    hashEmail(email) {
        if (!email || typeof email !== 'string' || email.trim() === '') {
            throw new Error('邮箱地址不能为空或无效');
        }
        
        const cleanEmail = email.trim().toLowerCase();
        
        // 验证邮箱格式
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(cleanEmail)) {
            throw new Error(`邮箱格式无效: ${email}`);
        }
        
        // 使用与R2StorageManager相同的哈希算法
        let hash = 0;
        for (let i = 0; i < cleanEmail.length; i++) {
            const char = cleanEmail.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        
        const hashResult = Math.abs(hash).toString(36);
        console.log(`邮箱哈希生成: ${email} -> ${hashResult}`);
        return hashResult;
    }

    /**
     * 通用用户ID获取方法 - 解决所有现有问题
     */
    async resolveUserId(email, taskId = null) {
        const cacheKey = `${email}:${taskId || 'global'}`;
        
        // 检查缓存
        if (this.cache.has(cacheKey)) {
            const cached = this.cache.get(cacheKey);
            console.log(`✅ 从缓存获取用户ID: ${cached.userId} (方法: ${cached.method})`);
            return cached;
        }

        const result = {
            email: email,
            userId: null,
            method: null,
            confidence: 0, // 0-100的置信度
            details: {}
        };

        console.log(`🔍 开始解析用户ID: 邮箱=${email}, 任务=${taskId || '无'}`);

        // 方法1：邮箱哈希算法（最可靠，与R2存储一致）
        try {
            const hashedUserId = this.hashEmail(email);
            
            // 如果有任务ID，验证该哈希用户ID是否在R2中有对应文件
            if (taskId && this.env.AUDIO_BUCKET) {
                const verified = await this.verifyUserIdInR2(hashedUserId, taskId);
                if (verified.exists) {
                    result.userId = hashedUserId;
                    result.method = 'emailHash';
                    result.confidence = 95;
                    result.details = {
                        hashGenerated: hashedUserId,
                        r2Verification: verified
                    };
                    console.log(`✅ 邮箱哈希验证成功: ${hashedUserId}`);
                    this.cache.set(cacheKey, result);
                    return result;
                }
            } else {
                // 没有任务ID时，直接使用哈希结果
                result.userId = hashedUserId;
                result.method = 'emailHash';
                result.confidence = 90;
                result.details = { hashGenerated: hashedUserId };
                console.log(`✅ 使用邮箱哈希: ${hashedUserId}`);
                this.cache.set(cacheKey, result);
                return result;
            }
        } catch (error) {
            console.warn(`邮箱哈希失败: ${error.message}`);
        }

        // 方法2：从R2存储反向查找（最准确）
        if (taskId && this.env.AUDIO_BUCKET) {
            try {
                const r2Result = await this.findUserIdFromR2(taskId);
                if (r2Result.userId) {
                    result.userId = r2Result.userId;
                    result.method = 'r2Lookup';
                    result.confidence = 100;
                    result.details = r2Result;
                    console.log(`✅ 从R2存储找到用户ID: ${r2Result.userId}`);
                    
                    // 同时更新用户数据中的userId
                    await this.updateUserDataWithId(email, r2Result.userId);
                    
                    this.cache.set(cacheKey, result);
                    return result;
                }
            } catch (error) {
                console.warn(`R2存储查找失败: ${error.message}`);
            }
        }

        // 方法3：从用户KV数据获取
        if (this.env.KV) {
            try {
                const userKey = `user:${email}`;
                const userData = await this.env.KV.get(userKey, { type: 'json' });
                
                if (userData && userData.userId) {
                    result.userId = userData.userId;
                    result.method = 'kvStore';
                    result.confidence = 85;
                    result.details = { userData: userData };
                    console.log(`✅ 从KV存储获取用户ID: ${userData.userId}`);
                    this.cache.set(cacheKey, result);
                    return result;
                }
            } catch (error) {
                console.warn(`KV存储查询失败: ${error.message}`);
            }
        }

        // 方法4：从其他任务推断
        if (this.env.KV) {
            try {
                const taskResult = await this.findUserIdFromTasks(email);
                if (taskResult.userId) {
                    result.userId = taskResult.userId;
                    result.method = 'taskInference';
                    result.confidence = 80;
                    result.details = taskResult;
                    console.log(`✅ 从任务推断用户ID: ${taskResult.userId}`);
                    
                    // 更新用户数据
                    await this.updateUserDataWithId(email, taskResult.userId);
                    
                    this.cache.set(cacheKey, result);
                    return result;
                }
            } catch (error) {
                console.warn(`任务推断失败: ${error.message}`);
            }
        }

        // 方法5：从邮箱推断（最后的备用方案，置信度低）
        try {
            const emailParts = email.split('@');
            const potentialUserId = emailParts[0];
            
            // 只有当邮箱部分不是纯数字且符合用户ID格式时才使用
            if (potentialUserId.match(/^[a-z0-9]+$/i) && 
                !potentialUserId.match(/^\d+$/) && 
                potentialUserId.length >= 3) {
                
                result.userId = potentialUserId;
                result.method = 'emailInference';
                result.confidence = 30;
                result.details = { emailPart: potentialUserId };
                console.log(`⚠️ 从邮箱推断用户ID: ${potentialUserId} (低置信度)`);
                this.cache.set(cacheKey, result);
                return result;
            }
        } catch (error) {
            console.warn(`邮箱推断失败: ${error.message}`);
        }

        // 所有方法都失败
        result.method = 'failed';
        result.confidence = 0;
        result.details = { error: '所有用户ID解析方法都失败' };
        console.error(`❌ 无法解析用户ID: ${email}`);
        
        this.cache.set(cacheKey, result);
        return result;
    }

    /**
     * 在R2存储中验证用户ID是否有对应的任务文件
     */
    async verifyUserIdInR2(userId, taskId) {
        const paths = [
            `users/${userId}/audios/${taskId}/`,
            `users/${userId}/audiobooks/${taskId}/`,
            `users/${userId}/uploads/${taskId}/`
        ];

        const result = {
            exists: false,
            foundPaths: [],
            totalFiles: 0
        };

        for (const path of paths) {
            try {
                const listResult = await this.env.R2.list({
                    prefix: path,
                    limit: 5
                });

                if (listResult.objects && listResult.objects.length > 0) {
                    result.exists = true;
                    result.foundPaths.push({
                        path: path,
                        files: listResult.objects.length,
                        firstFile: listResult.objects[0].key
                    });
                    result.totalFiles += listResult.objects.length;
                }
            } catch (error) {
                continue;
            }
        }

        return result;
    }

    /**
     * 从R2存储中反向查找用户ID
     */
    async findUserIdFromR2(taskId) {
        try {
            const usersList = await this.env.AUDIO_BUCKET.list({ 
                prefix: 'users/',
                delimiter: '/'
            });

            const result = {
                userId: null,
                foundUsers: [],
                totalUsers: usersList.delimitedPrefixes ? usersList.delimitedPrefixes.length : 0
            };

            if (usersList.delimitedPrefixes) {
                for (const prefix of usersList.delimitedPrefixes) {
                    const potentialUserId = prefix.replace('users/', '').replace('/', '');
                    
                    // 检查这个用户ID下是否有当前任务的文件
                    const verification = await this.verifyUserIdInR2(potentialUserId, taskId);
                    
                    if (verification.exists) {
                        result.foundUsers.push({
                            userId: potentialUserId,
                            verification: verification
                        });
                        
                        if (!result.userId) {
                            result.userId = potentialUserId; // 使用第一个找到的
                        }
                    }
                }
            }

            return result;
        } catch (error) {
            console.error('R2存储查找失败:', error);
            return { userId: null, error: error.message };
        }
    }

    /**
     * 从其他任务推断用户ID
     */
    async findUserIdFromTasks(email) {
        try {
            const allUserTasks = await this.env.KV.list({ prefix: 'task:', limit: 100 });
            
            const result = {
                userId: null,
                foundTasks: [],
                totalTasks: allUserTasks.keys.length
            };

            for (const key of allUserTasks.keys) {
                try {
                    const task = await this.env.KV.get(key.name, { type: 'json' });
                    if (task && task.email === email && task.audioUrl) {
                        // 从audioUrl中提取用户ID
                        const match = task.audioUrl.match(/users\/([^\/]+)\/audios/);
                        if (match) {
                            const foundUserId = match[1];
                            result.foundTasks.push({
                                taskId: task.id,
                                userId: foundUserId,
                                audioUrl: task.audioUrl
                            });
                            
                            if (!result.userId) {
                                result.userId = foundUserId; // 使用第一个找到的
                            }
                        }
                    }
                } catch (error) {
                    continue;
                }
            }

            return result;
        } catch (error) {
            console.error('任务推断失败:', error);
            return { userId: null, error: error.message };
        }
    }

    /**
     * 更新用户数据中的userId
     */
    async updateUserDataWithId(email, userId) {
        if (!this.env.KV) return;

        try {
            const userKey = `user:${email}`;
            const userData = await this.env.KV.get(userKey, { type: 'json' });
            
            if (userData && !userData.userId) {
                userData.userId = userId;
                userData.userIdUpdatedAt = new Date().toISOString();
                await this.env.KV.put(userKey, JSON.stringify(userData));
                console.log(`📝 已更新用户数据中的userId: ${userId}`);
            }
        } catch (error) {
            console.warn(`更新用户数据失败: ${error.message}`);
        }
    }

    /**
     * 测试特定邮箱的哈希结果
     */
    testEmailHash(email) {
        try {
            const hash = this.hashEmail(email);
            return {
                email: email,
                hash: hash,
                success: true
            };
        } catch (error) {
            return {
                email: email,
                hash: null,
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 批量测试邮箱哈希（用于找出产生特定哈希的邮箱）
     */
    findEmailForHash(targetHash, possibleEmails) {
        const results = [];
        
        for (const email of possibleEmails) {
            const test = this.testEmailHash(email);
            if (test.success && test.hash === targetHash) {
                results.push(email);
            }
        }
        
        return results;
    }

    /**
     * 清除缓存
     */
    clearCache() {
        this.cache.clear();
        console.log('🗑️ 用户ID解析器缓存已清空');
    }

    /**
     * 获取统计信息
     */
    getStats() {
        return {
            cacheSize: this.cache.size,
            cachedKeys: Array.from(this.cache.keys())
        };
    }
} 