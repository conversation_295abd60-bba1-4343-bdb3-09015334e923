// 修复音频URL API - 专门修复缺少audioUrl的成功任务
export async function onRequestPost({ request, env }) {
    try {
        const { email, taskIds } = await request.json();
        
        if (!email) {
            return Response.json({ error: '邮箱参数不能为空' }, { status: 400 });
        }

        // 验证用户是否存在
        const userKey = `user:${email}`;
        const userData = await env.KV.get(userKey, { type: 'json' });
        
        if (!userData) {
            return Response.json({ error: '用户不存在' }, { status: 404 });
        }

        const fixResults = [];
        let totalFixed = 0;

        // 如果指定了特定的任务ID，只修复这些任务
        if (taskIds && Array.isArray(taskIds)) {
            for (const taskId of taskIds) {
                const result = await fixSingleTask(env, email, taskId);
                if (result.fixed) totalFixed++;
                fixResults.push(result);
            }
        } else {
            // 否则，查找所有需要修复的任务
            const tasksToFix = await findTasksNeedingFix(env, email);
            
            for (const task of tasksToFix) {
                const result = await fixSingleTask(env, email, task.id);
                if (result.fixed) totalFixed++;
                fixResults.push(result);
            }
        }

        return Response.json({
            success: true,
            totalFixed,
            fixResults,
            message: `成功修复 ${totalFixed} 个任务的音频URL`
        });

    } catch (error) {
        console.error('修复音频URL失败:', error);
        return Response.json({ error: '修复失败: ' + error.message }, { status: 500 });
    }
}

// 查找需要修复的任务
async function findTasksNeedingFix(env, email) {
    const tasks = [];
    
    try {
        // 获取用户数据
        const userKey = `user:${email}`;
        const userData = await env.KV.get(userKey, { type: 'json' });
        
        let taskIds = [];
        
        if (userData?.taskIds && Array.isArray(userData.taskIds)) {
            taskIds = userData.taskIds;
        } else {
            // 后备方案：扫描所有任务
            const taskList = await env.KV.list({ prefix: 'task:', limit: 1000 });
            taskIds = taskList.keys
                .map(key => key.name.replace('task:', ''))
                .filter(id => id);
        }

        for (const taskId of taskIds) {
            try {
                const task = await env.KV.get(`task:${taskId}`, { type: 'json' });
                
                if (task && task.email === email && task.status === 'SUCCESS') {
                    const needsFix = isTaskNeedingFix(task);
                    if (needsFix) {
                        tasks.push({
                            id: task.id,
                            filename: task.filename,
                            title: task.title,
                            isEnhanced: task.isEnhanced,
                            totalChapters: task.totalChapters,
                            hasAudioUrl: !!task.audioUrl,
                            hasPlaylistUrl: !!task.playlistUrl,
                            hasAudioPath: !!task.audioPath
                        });
                    }
                }
            } catch (error) {
                console.warn(`检查任务 ${taskId} 失败:`, error);
            }
        }
    } catch (error) {
        console.error('查找需要修复的任务失败:', error);
    }
    
    return tasks;
}

// 判断任务是否需要修复
function isTaskNeedingFix(task) {
    const hasAudioUrl = !!task.audioUrl;
    const hasPlaylistUrl = !!task.playlistUrl;
    const hasAudioPath = !!task.audioPath;
    const isEnhanced = task.isEnhanced || task.totalChapters > 1;
    
    // 如果是增强版且没有audioUrl但有其他音频相关字段
    if (isEnhanced && !hasAudioUrl && (hasPlaylistUrl || task.totalChapters > 0)) {
        return true;
    }
    
    // 如果有audioPath但没有audioUrl
    if (hasAudioPath && !hasAudioUrl) {
        return true;
    }
    
    // 如果是成功的任务但完全没有音频相关字段
    if (!hasAudioUrl && !hasPlaylistUrl && !hasAudioPath && task.status === 'SUCCESS') {
        return true;
    }
    
    return false;
}

// 修复单个任务
async function fixSingleTask(env, email, taskId) {
    const result = {
        taskId,
        fixed: false,
        changes: [],
        error: null
    };
    
    try {
        const taskKey = `task:${taskId}`;
        const task = await env.KV.get(taskKey, { type: 'json' });
        
        if (!task) {
            result.error = '任务不存在';
            return result;
        }
        
        if (task.email !== email) {
            result.error = '无权修复此任务';
            return result;
        }
        
        if (task.status !== 'SUCCESS') {
            result.error = '任务未成功完成';
            return result;
        }
        
        let needsSave = false;
        const isEnhanced = task.isEnhanced || task.totalChapters > 1;
        
        // 修复audioUrl
        if (!task.audioUrl) {
            if (isEnhanced) {
                task.audioUrl = `/api/audio/playlist/${taskId}`;
                result.changes.push('设置增强版audioUrl');
            } else {
                task.audioUrl = `/api/audio/${taskId}.mp3`;
                result.changes.push('设置普通版audioUrl');
            }
            needsSave = true;
        }
        
        // 修复增强版相关字段
        if (isEnhanced) {
            if (!task.playlistUrl) {
                task.playlistUrl = `/api/audio/playlist/${taskId}`;
                result.changes.push('设置playlistUrl');
                needsSave = true;
            }
            
            if (!task.metadataUrl) {
                task.metadataUrl = `/api/audio/metadata/${taskId}`;
                result.changes.push('设置metadataUrl');
                needsSave = true;
            }
            
            if (!task.isEnhanced) {
                task.isEnhanced = true;
                result.changes.push('标记为增强版');
                needsSave = true;
            }
        }
        
        // 保存修改后的任务
        if (needsSave) {
            await env.KV.put(taskKey, JSON.stringify(task));
            result.fixed = true;
        }
        
        result.taskInfo = {
            filename: task.filename,
            title: task.title,
            isEnhanced,
            newAudioUrl: task.audioUrl
        };
        
    } catch (error) {
        result.error = error.message;
    }
    
    return result;
}

// GET方法：查询需要修复的任务
export async function onRequestGet({ request, env }) {
    try {
        const url = new URL(request.url);
        const email = url.searchParams.get('email');
        
        if (!email) {
            return Response.json({ error: '邮箱参数不能为空' }, { status: 400 });
        }

        // 验证用户是否存在
        const userKey = `user:${email}`;
        const userData = await env.KV.get(userKey, { type: 'json' });
        
        if (!userData) {
            return Response.json({ error: '用户不存在' }, { status: 404 });
        }

        const tasksNeedingFix = await findTasksNeedingFix(env, email);

        return Response.json({
            success: true,
            tasksNeedingFix,
            total: tasksNeedingFix.length,
            message: tasksNeedingFix.length > 0 ? 
                `发现 ${tasksNeedingFix.length} 个任务需要修复` : 
                '没有发现需要修复的任务'
        });

    } catch (error) {
        console.error('查询需要修复的任务失败:', error);
        return Response.json({ error: '查询失败: ' + error.message }, { status: 500 });
    }
} 