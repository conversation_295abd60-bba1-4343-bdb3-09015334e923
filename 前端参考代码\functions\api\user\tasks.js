// 用户任务列表 API
import { formatDateTime } from '../../utils/time-formatter.js';

export async function onRequestPost({ request, env }) {
    try {
        const { email } = await request.json();
        
        if (!email) {
            return Response.json({ error: '邮箱不能为空' }, { status: 400 });
        }

        // 验证用户是否存在
        const userKey = `user:${email}`;
        const userData = await env.KV.get(userKey, { type: 'json' });
        
        if (!userData) {
            return Response.json({ error: '用户不存在' }, { status: 404 });
        }

        // 获取用户的所有任务
        const taskList = await env.KV.list({ prefix: 'task:' });
        const userTasks = [];
        
        for (const key of taskList.keys) {
            const task = await env.KV.get(key.name, { type: 'json' });
            if (task && task.email === email) {
                // 添加任务类型友好显示名称
                task.typeDisplay = task.type === 'file' ? '文件上传' : '网页链接';
                
                // 格式化创建时间
                task.createdAtFormatted = formatDateTime(task.createdAt);
                
                // 格式化完成时间
                if (task.completedAt) {
                    task.completedAtFormatted = formatDateTime(task.completedAt);
                }
                
                // 格式化文件大小
                if (task.audioSize) {
                    task.audioSizeFormatted = formatFileSize(task.audioSize);
                }
                
                userTasks.push(task);
            }
        }

        // 按创建时间降序排序
        userTasks.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        return Response.json({
            success: true,
            tasks: userTasks,
            total: userTasks.length
        });

    } catch (error) {
        console.error('获取任务列表错误:', error);
        return Response.json({ error: '服务器内部错误' }, { status: 500 });
    }
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
} 