import { R2StorageManager } from './r2-storage.js';

/**
 * 增强版R2存储管理器 - 支持多章节音频存储
 */
export class EnhancedR2StorageManager extends R2StorageManager {
    constructor(r2Bucket) {
        super(r2Bucket);
    }

    /**
     * 保存多章节音频文件
     * @param {string} email - 用户邮箱
     * @param {string} taskId - 任务ID
     * @param {Array} audioChapters - 音频章节数组
     * @param {Object} bookMetadata - 书籍元数据
     * @returns {Promise<Object>} 保存结果
     */
    async saveMultiChapterAudio(email, taskId, audioChapters, bookMetadata = {}) {
        // 参数验证 - 防止undefined路径
        if (!email || typeof email !== 'string' || email.trim() === '') {
            throw new Error('用户邮箱不能为空或无效');
        }
        
        if (!taskId || typeof taskId !== 'string' || taskId.trim() === '') {
            throw new Error('任务ID不能为空或无效');
        }
        
        if (!audioChapters || !Array.isArray(audioChapters) || audioChapters.length === 0) {
            throw new Error('音频章节数组不能为空');
        }
        
        // 验证每个章节数据
        for (let i = 0; i < audioChapters.length; i++) {
            const chapter = audioChapters[i];
            if (!chapter.audioBuffer || !(chapter.audioBuffer instanceof ArrayBuffer)) {
                throw new Error(`章节 ${i + 1} 的音频数据无效`);
            }
            if (!chapter.title || typeof chapter.title !== 'string') {
                chapter.title = `第${i + 1}章`; // 提供默认标题
            }
        }
        
        console.log(`开始保存多章节音频: 邮箱=${email}, 任务ID=${taskId}, 章节数=${audioChapters.length}`);
        
        try {
            const emailHash = this.hashEmail(email);
            const basePath = `users/${emailHash}/audiobooks/${taskId}`;
            
            // 验证生成的路径不包含undefined
            if (basePath.includes('undefined')) {
                throw new Error(`生成的存储路径包含undefined: ${basePath}, 邮箱哈希=${emailHash}, 任务ID=${taskId}`);
            }
            
            console.log(`生成的存储基础路径: ${basePath}`);
            
            const chapterPaths = [];
            let totalSize = 0;
            let totalDuration = 0;

            // 保存每个章节的音频文件
            for (let i = 0; i < audioChapters.length; i++) {
                const chapter = audioChapters[i];
                const chapterFileName = `chapter_${String(i + 1).padStart(3, '0')}.mp3`;
                const chapterPath = `${basePath}/${chapterFileName}`;

                // 上传章节音频到R2
                await this.r2.put(chapterPath, chapter.audioBuffer, {
                    httpMetadata: {
                        contentType: 'audio/mpeg',
                        cacheControl: 'public, max-age=31536000'
                    },
                    customMetadata: {
                        email: email,
                        taskId: taskId,
                        chapterIndex: (i + 1).toString(),
                        chapterTitle: chapter.title,
                        duration: chapter.duration.toString(),
                        wordCount: chapter.wordCount.toString(),
                        isPartial: chapter.isPartial ? 'true' : 'false',
                        originalTitle: chapter.originalTitle || '',
                        partIndex: chapter.partIndex ? chapter.partIndex.toString() : '',
                        totalParts: chapter.totalParts ? chapter.totalParts.toString() : '',
                        createdAt: new Date().toISOString()
                    }
                });

                chapterPaths.push({
                    index: i + 1,
                    title: chapter.title,
                    path: chapterPath,
                    duration: chapter.duration,
                    wordCount: chapter.wordCount,
                    size: chapter.audioBuffer.byteLength,
                    isPartial: chapter.isPartial || false,
                    originalTitle: chapter.originalTitle,
                    partIndex: chapter.partIndex,
                    totalParts: chapter.totalParts
                });

                totalSize += chapter.audioBuffer.byteLength;
                totalDuration += chapter.duration;

                console.log(`章节 ${i + 1} 已保存: ${chapterPath} (${chapter.audioBuffer.byteLength} bytes)`);
            }

            // 保存书籍元数据
            const metadata = {
                taskId: taskId,
                email: email,
                title: bookMetadata.title || '未命名书籍',
                totalChapters: audioChapters.length,
                totalDuration: totalDuration,
                totalSize: totalSize,
                totalWordCount: audioChapters.reduce((sum, ch) => sum + ch.wordCount, 0),
                chapters: chapterPaths,
                createdAt: new Date().toISOString(),
                ...bookMetadata
            };

            const metadataPath = `${basePath}/metadata.json`;
            await this.r2.put(metadataPath, JSON.stringify(metadata, null, 2), {
                httpMetadata: {
                    contentType: 'application/json'
                }
            });

            // 生成播放列表文件
            const playlistPath = await this.generatePlaylist(basePath, chapterPaths, metadata);

            console.log(`多章节音频书籍已保存完成: ${audioChapters.length} 个章节, 总大小: ${totalSize} bytes`);

            return {
                basePath: basePath,
                metadataPath: metadataPath,
                playlistPath: playlistPath,
                chapters: chapterPaths,
                totalChapters: audioChapters.length,
                totalDuration: totalDuration,
                totalSize: totalSize
            };

        } catch (error) {
            console.error('保存多章节音频失败:', error);
            throw new Error(`多章节音频保存失败: ${error.message}`);
        }
    }

    /**
     * 生成播放列表文件
     */
    async generatePlaylist(basePath, chapterPaths, metadata) {
        try {
            // 生成M3U8播放列表
            const m3u8Content = [
                '#EXTM3U',
                '#EXT-X-VERSION:3',
                `#EXT-X-PLAYLIST-TYPE:VOD`,
                ''
            ];

            chapterPaths.forEach(chapter => {
                m3u8Content.push(`#EXTINF:${chapter.duration},${chapter.title}`);
                m3u8Content.push(`/api/audio/chapter/${metadata.taskId}/${chapter.index}.mp3`);
            });

            const playlistPath = `${basePath}/playlist.m3u8`;
            await this.r2.put(playlistPath, m3u8Content.join('\n'), {
                httpMetadata: {
                    contentType: 'application/vnd.apple.mpegurl'
                }
            });

            // 生成JSON播放列表（用于前端）
            const jsonPlaylist = {
                title: metadata.title,
                totalChapters: chapterPaths.length,
                totalDuration: metadata.totalDuration,
                chapters: chapterPaths.map(chapter => ({
                    index: chapter.index,
                    title: chapter.title,
                    duration: chapter.duration,
                    url: `/api/audio/chapter/${metadata.taskId}/${chapter.index}.mp3`,
                    wordCount: chapter.wordCount,
                    isPartial: chapter.isPartial,
                    originalTitle: chapter.originalTitle,
                    partIndex: chapter.partIndex,
                    totalParts: chapter.totalParts
                }))
            };

            const jsonPlaylistPath = `${basePath}/playlist.json`;
            await this.r2.put(jsonPlaylistPath, JSON.stringify(jsonPlaylist, null, 2), {
                httpMetadata: {
                    contentType: 'application/json'
                }
            });

            console.log(`播放列表已生成: ${playlistPath}, ${jsonPlaylistPath}`);
            return jsonPlaylistPath;

        } catch (error) {
            console.error('生成播放列表失败:', error);
            throw new Error(`播放列表生成失败: ${error.message}`);
        }
    }

    /**
     * 获取书籍的章节列表
     */
    async getBookChapters(email, taskId) {
        try {
            const emailHash = this.hashEmail(email);
            const metadataPath = `users/${emailHash}/audiobooks/${taskId}/metadata.json`;
            console.log(`getBookChapters: 尝试读取元数据文件 ${metadataPath}`);
            
            const metadataObject = await this.getFile(metadataPath);
            
            if (!metadataObject) {
                console.log(`getBookChapters: 元数据文件不存在 ${metadataPath}`);
                return null;
            }

            console.log(`getBookChapters: 元数据文件存在，大小: ${metadataObject.size} 字节`);
            const metadataText = await metadataObject.text();
            const metadata = JSON.parse(metadataText);
            console.log(`getBookChapters: 成功解析元数据，章节数: ${metadata.chapters?.length || 0}`);
            
            return metadata.chapters || [];

        } catch (error) {
            console.error('getBookChapters: 获取章节列表失败:', error);
            console.error('getBookChapters: 错误详情:', error.stack);
            return null;
        }
    }

    /**
     * 获取指定章节的音频文件
     */
    async getChapterAudio(email, taskId, chapterIndex) {
        try {
            const chapterFileName = `chapter_${String(chapterIndex).padStart(3, '0')}.mp3`;
            const emailHash = this.hashEmail(email);
            const chapterPath = `users/${emailHash}/audiobooks/${taskId}/${chapterFileName}`;
            
            return await this.getFile(chapterPath);

        } catch (error) {
            console.error(`获取章节 ${chapterIndex} 音频失败:`, error);
            return null;
        }
    }

    /**
     * 获取指定范围的章节音频（用于Range请求）
     */
    async getChapterAudioRange(email, taskId, chapterIndex, start, end) {
        try {
            const chapterFileName = `chapter_${String(chapterIndex).padStart(3, '0')}.mp3`;
            const emailHash = this.hashEmail(email);
            const chapterPath = `users/${emailHash}/audiobooks/${taskId}/${chapterFileName}`;
            
            return await this.getFileRange(chapterPath, start, end);

        } catch (error) {
            console.error(`获取章节 ${chapterIndex} 音频范围失败:`, error);
            return null;
        }
    }

    /**
     * 获取书籍播放列表
     */
    async getBookPlaylist(email, taskId) {
        try {
            const emailHash = this.hashEmail(email);
            const playlistPath = `users/${emailHash}/audiobooks/${taskId}/playlist.json`;
            console.log(`getBookPlaylist: 尝试读取播放列表文件 ${playlistPath}`);
            
            const playlistObject = await this.getFile(playlistPath);
            
            if (!playlistObject) {
                console.log(`getBookPlaylist: 播放列表文件不存在 ${playlistPath}`);
                return null;
            }

            console.log(`getBookPlaylist: 播放列表文件存在，大小: ${playlistObject.size} 字节`);
            const playlistText = await playlistObject.text();
            console.log(`getBookPlaylist: 播放列表内容前200字符: ${playlistText.substring(0, 200)}`);
            
            const playlist = JSON.parse(playlistText);
            console.log(`getBookPlaylist: 成功解析播放列表，章节数: ${playlist.chapters?.length || 0}`);
            
            return playlist;

        } catch (error) {
            console.error('getBookPlaylist: 获取播放列表失败:', error);
            console.error('getBookPlaylist: 错误详情:', error.stack);
            return null;
        }
    }

    /**
     * 获取书籍元数据
     */
    async getBookMetadata(email, taskId) {
        try {
            const emailHash = this.hashEmail(email);
            const metadataPath = `users/${emailHash}/audiobooks/${taskId}/metadata.json`;
            console.log(`getBookMetadata: 尝试读取元数据文件 ${metadataPath}`);
            
            const metadataObject = await this.getFile(metadataPath);
            
            if (!metadataObject) {
                console.log(`getBookMetadata: 元数据文件不存在 ${metadataPath}`);
                return null;
            }

            console.log(`getBookMetadata: 元数据文件存在，大小: ${metadataObject.size} 字节`);
            const metadataText = await metadataObject.text();
            console.log(`getBookMetadata: 元数据内容前200字符: ${metadataText.substring(0, 200)}`);
            
            const metadata = JSON.parse(metadataText);
            console.log(`getBookMetadata: 成功解析元数据，章节数: ${metadata.chapters?.length || 0}`);
            
            return metadata;

        } catch (error) {
            console.error('getBookMetadata: 获取书籍元数据失败:', error);
            console.error('getBookMetadata: 错误详情:', error.stack);
            return null;
        }
    }

    /**
     * 删除多章节音频书籍
     */
    async deleteMultiChapterAudio(email, taskId) {
        try {
            const emailHash = this.hashEmail(email);
            const basePath = `users/${emailHash}/audiobooks/${taskId}`;
            
            // 获取所有相关文件
            const listResult = await this.r2.list({ prefix: basePath });
            
            // 删除所有文件
            const deletePromises = listResult.objects.map(obj => 
                this.r2.delete(obj.key)
            );
            
            await Promise.all(deletePromises);
            
            console.log(`已删除多章节音频书籍: ${taskId} (${deletePromises.length} 个文件)`);
            return true;

        } catch (error) {
            console.error('删除多章节音频失败:', error);
            return false;
        }
    }

    /**
     * 为外部API已保存的多章节音频创建元数据和播放列表
     * 这个方法用于外部API已经将音频文件保存到R2存储的场景
     */
    async saveMultiChapterAudioMetadata(email, taskId, audioChapters, bookMetadata = {}) {
        // 参数验证 - 防止undefined路径
        if (!email || typeof email !== 'string' || email.trim() === '') {
            throw new Error('用户邮箱不能为空或无效');
        }
        
        if (!taskId || typeof taskId !== 'string' || taskId.trim() === '') {
            throw new Error('任务ID不能为空或无效');
        }
        
        if (!audioChapters || !Array.isArray(audioChapters) || audioChapters.length === 0) {
            throw new Error('音频章节数组不能为空');
        }
        
        // 验证每个章节数据（这次验证的是audioPath而不是audioBuffer）
        for (let i = 0; i < audioChapters.length; i++) {
            const chapter = audioChapters[i];
            if (!chapter.audioPath || typeof chapter.audioPath !== 'string') {
                throw new Error(`章节 ${i + 1} 的音频路径无效: ${chapter.audioPath}`);
            }
            if (!chapter.title || typeof chapter.title !== 'string') {
                chapter.title = `第${i + 1}章`; // 提供默认标题
            }
        }
        
        console.log(`开始为外部API保存的音频创建元数据: 邮箱=${email}, 任务ID=${taskId}, 章节数=${audioChapters.length}`);
        
        try {
            const emailHash = this.hashEmail(email);
            const basePath = `users/${emailHash}/audiobooks/${taskId}`;
            
            // 验证生成的路径不包含undefined
            if (basePath.includes('undefined')) {
                throw new Error(`生成的存储路径包含undefined: ${basePath}, 邮箱哈希=${emailHash}, 任务ID=${taskId}`);
            }
            
            console.log(`生成的存储基础路径: ${basePath}`);
            
            const chapterPaths = [];
            let totalSize = 0;
            let totalDuration = 0;

            // 处理每个章节的元数据（音频文件已由外部API保存）
            for (let i = 0; i < audioChapters.length; i++) {
                const chapter = audioChapters[i];
                
                // 获取音频文件的实际大小
                let audioSize = 0;
                try {
                    const audioObject = await this.r2.head(chapter.audioPath);
                    audioSize = audioObject ? audioObject.size : 0;
                } catch (error) {
                    console.warn(`无法获取音频文件大小: ${chapter.audioPath}`, error);
                    audioSize = 0; // 默认大小
                }

                chapterPaths.push({
                    index: i + 1,
                    title: chapter.title,
                    path: chapter.audioPath, // 使用外部API保存的路径
                    duration: chapter.duration,
                    wordCount: chapter.wordCount,
                    size: audioSize,
                    isPartial: chapter.isPartial || false,
                    originalTitle: chapter.originalTitle,
                    partIndex: chapter.partIndex,
                    totalParts: chapter.totalParts,
                    externalUrl: chapter.externalUrl // 保存外部URL用于调试
                });

                totalSize += audioSize;
                totalDuration += chapter.duration;

                console.log(`章节 ${i + 1} 元数据已处理: ${chapter.audioPath} (${audioSize} bytes)`);
            }

            // 保存书籍元数据
            const metadata = {
                taskId: taskId,
                email: email,
                title: bookMetadata.title || '未命名书籍',
                totalChapters: audioChapters.length,
                totalDuration: totalDuration,
                totalSize: totalSize,
                totalWordCount: audioChapters.reduce((sum, ch) => sum + ch.wordCount, 0),
                chapters: chapterPaths,
                createdAt: new Date().toISOString(),
                savedByExternalAPI: true, // 标记为外部API保存
                ...bookMetadata
            };

            const metadataPath = `${basePath}/metadata.json`;
            await this.r2.put(metadataPath, JSON.stringify(metadata, null, 2), {
                httpMetadata: {
                    contentType: 'application/json'
                }
            });

            // 生成播放列表文件
            const playlistPath = await this.generatePlaylist(basePath, chapterPaths, metadata);

            console.log(`多章节音频元数据已保存完成: ${audioChapters.length} 个章节, 总大小: ${totalSize} bytes`);

            return {
                basePath: basePath,
                metadataPath: metadataPath,
                playlistPath: playlistPath,
                chapters: chapterPaths,
                totalChapters: audioChapters.length,
                totalDuration: totalDuration,
                totalSize: totalSize
            };

        } catch (error) {
            console.error('保存多章节音频元数据失败:', error);
            throw new Error(`多章节音频元数据保存失败: ${error.message}`);
        }
    }

    /**
     * 保存多章节音频（原始方法，用于本地TTS生成的音频）
     */
} 