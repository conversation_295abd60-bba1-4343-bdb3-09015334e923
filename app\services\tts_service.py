"""
TTS音频转换服务
基于现有text_to_speech.py实现批量音频转换功能
基于原始参考代码完整迁移，保持所有算法逻辑不变，包括智能文本分割和音频合并
"""

import aiofiles
import os
from typing import List, Optional, Callable
from dataclasses import dataclass
from pathlib import Path

from app.core.config import settings
from app.core.logging import get_logger
from app.services.epub_parser import Chapter

logger = get_logger(__name__)


@dataclass
class AudioSegment:
    """音频片段数据模型"""

    id: str
    chapter_id: str
    text: str
    audio_path: str
    duration: float = 0.0
    file_size: int = 0
    status: str = "pending"  # pending, processing, completed, failed


@dataclass
class VoiceSettings:
    """语音设置模型"""

    model: str = "zh-CN-XiaochenMultilingualNeural"
    speed: int = 0
    pitch: int = 0
    output_format: str = "audio-24khz-48kbitrate-mono-mp3"   # audio-24khz-160kbitrate-mono-mp3


class TTSService:
    """TTS音频转换服务"""

    def __init__(self):
        self.api_url = settings.tts_api_url
        self.api_key = settings.tts_api_key
        self.default_voice_settings = VoiceSettings(
            model=settings.tts_model,
            speed=settings.tts_speed,
            pitch=settings.tts_pitch,
            output_format=settings.tts_output_format,
        )
        self.temp_dir = Path(settings.temp_dir)
        self._ensure_temp_dir()

    def _ensure_temp_dir(self):
        """确保临时目录存在并具有正确权限"""
        try:
            # 创建目录
            self.temp_dir.mkdir(parents=True, exist_ok=True)

            # 设置目录权限
            os.chmod(str(self.temp_dir), 0o755)

            # 测试写入权限
            test_file = self.temp_dir / "test_write.tmp"
            try:
                test_file.write_text("test")
                test_file.unlink()
                logger.debug(f"临时目录权限验证成功: {self.temp_dir}")
            except Exception as e:
                logger.warning(f"临时目录写入测试失败: {e}")
                # 使用系统临时目录
                import tempfile
                self.temp_dir = Path(tempfile.mkdtemp(prefix="audiobooks_tts_"))
                logger.info(f"使用系统临时目录: {self.temp_dir}")

        except Exception as e:
            logger.error(f"创建临时目录失败: {e}")
            # 使用系统临时目录作为后备
            import tempfile
            self.temp_dir = Path(tempfile.mkdtemp(prefix="audiobooks_tts_"))
            logger.info(f"使用系统临时目录作为后备: {self.temp_dir}")

    async def convert_chapters_to_audio(
        self,
        chapters: List[Chapter],
        voice_settings: Optional[VoiceSettings] = None,
        progress_callback: Optional[Callable[[int, int, str], None]] = None,
    ) -> List[AudioSegment]:
        """将章节列表转换为音频文件"""

        if not voice_settings:
            voice_settings = self.default_voice_settings

        audio_segments = []
        total_chapters = len(chapters)

        logger.info(f"开始转换{total_chapters}个章节为音频")

        for i, chapter in enumerate(chapters):
            try:
                if progress_callback:
                    progress_callback(i, total_chapters, f"正在转换: {chapter.title}")

                # 分割长文本 - 使用智能分割算法（约1000字分段，优先段落边界，避免句子中间截断）
                text_segments = self._split_text(chapter.content)
                chapter_segments = []

                for j, text_segment in enumerate(text_segments):
                    segment_id = f"{chapter.id}_segment_{j+1}"
                    audio_path = self.temp_dir / f"{segment_id}.mp3"

                    # 转换单个文本片段
                    success = await self._convert_text_to_audio(
                        text_segment, str(audio_path), voice_settings
                    )

                    if success:
                        # 获取文件信息
                        file_size = (
                            os.path.getsize(audio_path)
                            if os.path.exists(audio_path)
                            else 0
                        )

                        segment = AudioSegment(
                            id=segment_id,
                            chapter_id=chapter.id,
                            text=text_segment,
                            audio_path=str(audio_path),
                            file_size=file_size,
                            status="completed",
                        )
                        chapter_segments.append(segment)
                    else:
                        logger.error(f"章节 {chapter.title} 的片段 {j+1} 转换失败")

                # 如果章节有多个片段，合并音频文件
                if len(chapter_segments) > 1:
                    merged_path = await self._merge_audio_segments(
                        chapter_segments, chapter.id
                    )
                    if merged_path:
                        # 创建合并后的音频片段
                        merged_segment = AudioSegment(
                            id=chapter.id,
                            chapter_id=chapter.id,
                            text=chapter.content,
                            audio_path=merged_path,
                            file_size=(
                                os.path.getsize(merged_path)
                                if os.path.exists(merged_path)
                                else 0
                            ),
                            status="completed",
                        )
                        audio_segments.append(merged_segment)

                        # 清理临时片段文件
                        for segment in chapter_segments:
                            if os.path.exists(segment.audio_path):
                                os.unlink(segment.audio_path)
                    else:
                        # 合并失败，保留原始片段
                        audio_segments.extend(chapter_segments)
                elif len(chapter_segments) == 1:
                    # 只有一个片段，直接使用
                    audio_segments.extend(chapter_segments)

                logger.info(f"章节 '{chapter.title}' 转换完成")

            except Exception as e:
                logger.error(f"转换章节 '{chapter.title}' 时发生错误: {str(e)}")
                continue

        if progress_callback:
            progress_callback(total_chapters, total_chapters, "音频转换完成")

        logger.info(f"音频转换完成，共生成{len(audio_segments)}个音频文件")
        return audio_segments

    async def _convert_text_to_audio(
        self, text: str, save_path: str, voice_settings: VoiceSettings
    ) -> bool:
        """将单个文本转换为音频文件 (基于原始text_to_speech.py)"""
        try:
            # 预处理文本，清理可能导致问题的字符
            cleaned_text = self._preprocess_text(text)

            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
            }

            payload = {
                "model": voice_settings.model,
                "input": cleaned_text,
                "voice_settings": {
                    "speed": voice_settings.speed,
                    "pitch": voice_settings.pitch,
                    "output_format": voice_settings.output_format,
                },
            }

            # 使用异步HTTP请求
            import httpx

            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.post(
                    self.api_url, headers=headers, json=payload
                )

                # 详细的错误处理
                if response.status_code == 500:
                    logger.error(f"TTS服务器内部错误 (500): 文本可能包含无法处理的内容")
                    logger.error(f"文本长度: {len(text)} 字符")
                    logger.error(f"文本前100字符: {text[:100]}...")
                    return False
                elif response.status_code == 400:
                    logger.error(f"TTS请求参数错误 (400): {response.text}")
                    return False
                elif response.status_code == 401:
                    logger.error(f"TTS API认证失败 (401): 请检查API密钥")
                    return False
                elif response.status_code == 429:
                    logger.error(f"TTS API请求频率限制 (429): 请稍后重试")
                    return False
                elif response.status_code != 200:
                    logger.error(f"TTS API返回错误状态码 {response.status_code}: {response.text}")
                    return False

                response.raise_for_status()

                # 异步保存文件，添加权限错误处理
                try:
                    # 确保目录存在
                    save_dir = os.path.dirname(save_path)
                    if save_dir:
                        os.makedirs(save_dir, exist_ok=True)
                        os.chmod(save_dir, 0o755)

                    async with aiofiles.open(save_path, "wb") as f:
                        await f.write(response.content)

                    # 设置文件权限
                    os.chmod(save_path, 0o644)

                except PermissionError as pe:
                    logger.error(f"文件保存权限错误: {pe}")
                    # 尝试使用临时文件
                    import tempfile
                    temp_fd, temp_path = tempfile.mkstemp(suffix=".mp3", prefix="tts_")
                    try:
                        with os.fdopen(temp_fd, 'wb') as f:
                            f.write(response.content)

                        # 尝试移动到目标位置
                        import shutil
                        shutil.move(temp_path, save_path)
                        os.chmod(save_path, 0o644)
                    except Exception as move_error:
                        logger.error(f"临时文件移动失败: {move_error}")
                        if os.path.exists(temp_path):
                            os.unlink(temp_path)
                        return False

            logger.debug(f"音频文件保存成功: {save_path}")
            return True

        except httpx.HTTPStatusError as e:
            logger.error(f"TTS HTTP错误: {e.response.status_code} - {e.response.text}")
            return False
        except httpx.TimeoutException:
            logger.error(f"TTS请求超时: 文本可能过长或服务器响应慢")
            return False
        except Exception as e:
            logger.error(f"TTS转换失败: {str(e)}")
            return False

    def _preprocess_text(self, text: str) -> str:
        """预处理文本，清理可能导致TTS API问题的内容"""
        import re

        # 移除或替换可能导致问题的字符和模式
        cleaned_text = text

        # 清理特殊字符和控制字符
        cleaned_text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', cleaned_text)

        # 替换敏感信息模式 - 启用更强的过滤
        cleaned_text = re.sub(r'\b\d{3}-\d{2}-\d{4}\b', '[社会保障号]', cleaned_text)  # SSN
        cleaned_text = re.sub(r'\b\d{4}-\d{4}-\d{4}-\d{4}\b', '[信用卡号]', cleaned_text)  # 信用卡
        cleaned_text = re.sub(r'\b\d{10,}\b', '[账户号码]', cleaned_text)  # 长数字（银行账户等）

        # 替换加密货币地址模式
        cleaned_text = re.sub(r'\b[13][a-km-zA-HJ-NP-Z1-9]{25,34}\b', '[比特币地址]', cleaned_text)  # Bitcoin
        cleaned_text = re.sub(r'\b0x[a-fA-F0-9]{40}\b', '[以太坊地址]', cleaned_text)  # Ethereum
        cleaned_text = re.sub(r'\br[a-zA-Z0-9]{24,34}\b', '[瑞波币地址]', cleaned_text)  # Ripple
        cleaned_text = re.sub(r'\b4[a-zA-Z0-9]{93}\b', '[门罗币地址]', cleaned_text)  # Monero

        # 替换IBAN
        cleaned_text = re.sub(r'\bUS\d{2}\d{12,}\b', '[国际银行账户号]', cleaned_text)

        # 替换路由号码
        cleaned_text = re.sub(r'\b\d{9}\b', '[路由号码]', cleaned_text)

        # 替换电话号码
        cleaned_text = re.sub(r'\b\(\d{3}\)\s*\d{3}-\d{4}\b', '[电话号码]', cleaned_text)
        cleaned_text = re.sub(r'\b\d{3}-\d{3}-\d{4}\b', '[电话号码]', cleaned_text)

        # 替换邮箱地址
        cleaned_text = re.sub(r'\b[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\b', '[邮箱地址]', cleaned_text)

        # 替换经纬度坐标
        cleaned_text = re.sub(r'\b-?\d+\.\d+,\s*-?\d+\.\d+\b', '[地理坐标]', cleaned_text)

        # 替换可能导致问题的特殊词汇和符号组合
        cleaned_text = re.sub(r'Latitude\s*&\s*longitude:', '纬度和经度:', cleaned_text)
        cleaned_text = re.sub(r'&', '和', cleaned_text)  # 替换&符号

        # 技术文档特殊处理
        # 处理Markdown标记
        cleaned_text = re.sub(r'#{1,6}\s*', '', cleaned_text)  # 移除Markdown标题标记
        cleaned_text = re.sub(r'\*{1,2}([^*]+)\*{1,2}', r'\1', cleaned_text)  # 移除加粗/斜体标记
        cleaned_text = re.sub(r'`([^`]+)`', r'\1', cleaned_text)  # 移除行内代码标记

        # 处理技术术语和英文缩写
        cleaned_text = re.sub(r'\bHTML\b', '网页标记语言', cleaned_text)
        cleaned_text = re.sub(r'\bCSS\b', '样式表', cleaned_text)
        cleaned_text = re.sub(r'\bJavaScript\b', '脚本语言', cleaned_text)
        cleaned_text = re.sub(r'\bRevealJS\b', '演示框架', cleaned_text)
        cleaned_text = re.sub(r'\bTailwind\s*CSS\b', '样式框架', cleaned_text)
        cleaned_text = re.sub(r'\bCDN\b', '内容分发网络', cleaned_text)
        cleaned_text = re.sub(r'\bAPI\b', '应用程序接口', cleaned_text)
        cleaned_text = re.sub(r'\bURL\b', '网址', cleaned_text)
        cleaned_text = re.sub(r'\bWCAG\b', '无障碍指南', cleaned_text)

        # 处理特殊符号和括号内容
        cleaned_text = re.sub(r'\[([^\]]+)\]', r'\1', cleaned_text)  # 移除方括号，保留内容
        cleaned_text = re.sub(r'\(([^)]+)\)', r'\1', cleaned_text)  # 移除圆括号，保留内容
        cleaned_text = re.sub(r'[<>{}]', '', cleaned_text)  # 移除尖括号和花括号

        # 注释掉版本号和比例的替换，保持原始数字内容
        # cleaned_text = re.sub(r'v?\d+\.\d+(\.\d+)?', '版本号', cleaned_text)  # 版本号
        # cleaned_text = re.sub(r'\d+:\d+', '比例', cleaned_text)  # 比例如16:9

        # 清理过长的连续字符
        cleaned_text = re.sub(r'(.)\1{10,}', r'\1\1\1', cleaned_text)

        # 规范化空白字符
        cleaned_text = re.sub(r'\s+', ' ', cleaned_text)
        cleaned_text = cleaned_text.strip()

        return cleaned_text

    def _split_text(self, text: str, max_length: int = 1000) -> List[str]:
        """将长文本分割为适合TTS处理的片段 - 智能分割算法

        优先级：
        1. 段落边界（双换行符）
        2. 句子边界（句号、问号、感叹号）
        3. 避免在句子中间硬切分
        """
        if len(text) <= max_length:
            return [text]

        segments = []

        # 第一步：按段落分割
        paragraphs = self._split_by_paragraphs(text)

        current_segment = ""

        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue

            # 如果单个段落就超过最大长度，需要进一步分割
            if len(paragraph) > max_length:
                # 保存当前片段（如果有内容）
                if current_segment:
                    segments.append(current_segment.strip())
                    current_segment = ""

                # 分割长段落
                long_paragraph_segments = self._split_long_paragraph(paragraph, max_length)
                segments.extend(long_paragraph_segments)
            else:
                # 检查是否可以添加到当前片段
                if current_segment:
                    potential_segment = current_segment + "\n\n" + paragraph
                else:
                    potential_segment = paragraph

                if len(potential_segment) <= max_length:
                    current_segment = potential_segment
                else:
                    # 保存当前片段，开始新片段
                    if current_segment:
                        segments.append(current_segment)
                    current_segment = paragraph

        # 添加最后一个片段
        if current_segment:
            segments.append(current_segment)

        return segments if segments else [text]

    def _split_by_paragraphs(self, text: str) -> List[str]:
        """按段落分割文本"""
        import re
        # 按双换行符分割段落，保留单换行符
        paragraphs = re.split(r'\n\s*\n', text)
        return [p.strip() for p in paragraphs if p.strip()]

    def _split_long_paragraph(self, paragraph: str, max_length: int) -> List[str]:
        """分割超长段落 - 按句子边界分割"""
        import re

        # 简化的句子分割：按句号、问号、感叹号分割
        sentences = re.split(r'([。！？])', paragraph)

        segments = []
        current_segment = ""

        i = 0
        while i < len(sentences):
            part = sentences[i]

            # 如果是标点符号，与前一部分合并
            if part in ['。', '！', '？'] and i > 0:
                current_segment += part
            else:
                # 如果不是标点符号，检查是否可以添加到当前片段
                potential_segment = current_segment + part

                if len(potential_segment) <= max_length:
                    current_segment = potential_segment
                else:
                    # 如果当前片段为空但单个部分就超长，需要硬切分
                    if not current_segment:
                        if len(part) > max_length:
                            # 硬切分长句子
                            hard_split_segments = self._hard_split_sentence(part, max_length)
                            segments.extend(hard_split_segments)
                        else:
                            current_segment = part
                    else:
                        # 保存当前片段，开始新片段
                        if current_segment.strip():
                            segments.append(current_segment.strip())
                        current_segment = part

            i += 1

        # 添加最后一个片段
        if current_segment and current_segment.strip():
            segments.append(current_segment.strip())

        return segments

    def _hard_split_sentence(self, sentence: str, max_length: int) -> List[str]:
        """硬切分超长句子 - 最后的手段"""
        segments = []
        start = 0

        while start < len(sentence):
            end = start + max_length
            if end >= len(sentence):
                segments.append(sentence[start:])
                break

            # 尝试在标点符号处切分
            cut_pos = end
            for i in range(end - 1, start + max_length // 2, -1):
                if sentence[i] in '，、；：':
                    cut_pos = i + 1
                    break

            segments.append(sentence[start:cut_pos])
            start = cut_pos

        return segments

    async def _merge_audio_segments(
        self, segments: List[AudioSegment], chapter_id: str
    ) -> Optional[str]:
        """合并音频片段 (需要pydub库)"""
        try:
            from pydub import AudioSegment as PyDubSegment

            merged_audio = PyDubSegment.empty()

            for segment in segments:
                if os.path.exists(segment.audio_path):
                    audio = PyDubSegment.from_mp3(segment.audio_path)
                    merged_audio += audio

            # 保存合并后的音频
            merged_path = self.temp_dir / f"{chapter_id}_merged.mp3"
            merged_audio.export(str(merged_path), format="mp3")

            logger.debug(f"音频片段合并完成: {merged_path}")
            return str(merged_path)

        except ImportError:
            logger.warning("pydub库未安装，无法合并音频片段")
            return None
        except Exception as e:
            logger.error(f"合并音频片段失败: {str(e)}")
            return None

    async def convert_single_text(
        self,
        text: str,
        output_path: str,
        voice_settings: Optional[VoiceSettings] = None,
    ) -> bool:
        """转换单个文本为音频 (兼容原始接口)"""
        if not voice_settings:
            voice_settings = self.default_voice_settings

        return await self._convert_text_to_audio(text, output_path, voice_settings)

    def cleanup_temp_files(self, audio_segments: List[AudioSegment]):
        """清理临时音频文件"""
        for segment in audio_segments:
            if os.path.exists(segment.audio_path):
                try:
                    os.unlink(segment.audio_path)
                    logger.debug(f"清理临时文件: {segment.audio_path}")
                except Exception as e:
                    logger.warning(f"清理临时文件失败: {str(e)}")


# 兼容原始text_to_speech.py接口
async def text_to_speech_zwei(text: str, save_path: str) -> bool:
    """兼容原始text_to_speech_zwei函数"""
    tts_service = TTSService()
    return await tts_service.convert_single_text(text, save_path)
