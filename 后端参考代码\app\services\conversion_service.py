"""
转换服务
整合文件解析（EPUB/TXT）、TTS转换、本地存储等功能
基于原始参考代码完整迁移，保持所有算法逻辑不变，只修改存储方式为本地存储
"""

import os
import tempfile
from typing import List, Dict, Optional
from app.models.task import ConversionTask
from app.services.file_parser_service import FileParserService
from app.services.tts_service import TTSService, VoiceSettings as TTSVoiceSettings
from app.services.storage_service import storage_service  # 使用本地存储服务
from app.services.playlist_service import PlaylistService
from app.core.logging import get_logger

logger = get_logger(__name__)


class ConversionService:
    """转换服务主类"""

    def __init__(self):
        self.file_parser_service = FileParserService()
        self.tts_service = TTSService()
        self.storage_service = storage_service  # 使用本地存储服务
        self.playlist_service = PlaylistService()
        self._status_service = None

    def _get_status_service(self):
        """获取状态服务实例（延迟初始化）"""
        if self._status_service is None:
            from app.services.status_service import status_service
            self._status_service = status_service
        return self._status_service

    async def _update_task_status(self, task: ConversionTask, force_update: bool = False):
        """
        更新任务状态到本地存储 (已优化：不再调用)

        优化说明：
        - 为了减少50%的本地存储写入操作，移除了转换过程中的状态更新
        - 状态流转简化为：PENDING → COMPLETED/FAILED
        - 只在任务队列的关键节点进行状态更新
        - 保留此方法以备将来需要时恢复中间状态更新

        Args:
            task: 任务对象
            force_update: 是否强制更新（用于关键状态节点）
        """
        # 优化：完全跳过转换服务中的状态更新
        logger.debug(f"转换服务状态更新已优化跳过: {task.task_id}")
        return

        # 以下代码保留以备将来恢复使用
        try:
            if not task.user_id:
                return

            # 只在关键状态或强制更新时才执行
            if not force_update:
                logger.debug(f"跳过非关键状态更新: {task.task_id}")
                return

            status_service = self._get_status_service()

            # 使用新的防抖更新方法
            await status_service.update_status_debounced(
                task.user_id,
                task.original_task_id or task.task_id,
                task.status.value.upper(),
                progress=task.progress,
                current_stage=task.current_stage
            )
        except Exception as e:
            logger.warning(f"更新任务状态失败: {task.task_id}, {str(e)}")

    async def process_conversion_task(self, task: ConversionTask) -> bool:
        """处理转换任务"""
        try:
            logger.info(f"开始处理转换任务: {task.task_id}")

            # 0. 解析文件URL或R2路径获取用户ID和任务ID
            if hasattr(task, 'r2_file_path') and task.r2_file_path:
                # 使用R2路径解析
                user_id, parsed_task_id = self.storage_service.parse_file_url_info(task.r2_file_path)
                logger.info(f"解析R2路径结果 - 用户ID: {user_id}, 解析的任务ID: {parsed_task_id}")

                # 对于R2路径，直接使用R2路径进行下载，不构建HTTP URL
                task.file_url = f"r2://{task.r2_file_path}"
            else:
                # 使用传统URL解析
                user_id, parsed_task_id = self.storage_service.parse_file_url_info(task.file_url)
                logger.info(f"解析URL结果 - 用户ID: {user_id}, 解析的任务ID: {parsed_task_id}")

            # 保存解析的信息到任务中
            task.user_id = user_id
            task.original_task_id = parsed_task_id

            # 将解析的信息存储到任务中
            task.user_id = user_id
            if parsed_task_id and not hasattr(task, 'original_task_id'):
                task.original_task_id = parsed_task_id

            # 1. 检测文件类型
            file_info = await self.file_parser_service.get_file_info(task.file_url)
            file_type = file_info.get('file_type', 'unknown')

            # 2. 下载文件
            task.update_progress(10, f"正在下载{file_type.upper()}文件")
            # 移除频繁的状态更新，只在关键节点更新
            file_path = await self._download_file(task, file_type)
            if not file_path:
                raise Exception(f"{file_type.upper()}文件下载失败")

            # 3. 解析文件
            task.update_progress(20, f"正在解析{file_type.upper()}文件")
            # 移除频繁的状态更新，只在关键节点更新
            metadata, chapters = await self.file_parser_service.parse_from_file(file_path)

            if not chapters:
                raise Exception(f"{file_type.upper()}文件解析失败或无有效章节")

            # 更新任务元数据
            task.metadata = {
                "title": metadata.title,
                "author": metadata.author,
                "language": metadata.language,
                "total_chapters": len(chapters),
                "total_words": metadata.total_words,
            }

            task.processing_details.total_segments = len(chapters)

            # 3. 转换为音频
            task.update_progress(30, "正在转换音频文件")
            # 移除频繁的状态更新，只在关键节点更新
            audio_segments = await self._convert_to_audio(task, chapters)

            if not audio_segments:
                raise Exception("音频转换失败")

            # 4. 上传音频文件
            task.update_progress(80, "正在上传音频文件")
            # 移除频繁的状态更新，只在关键节点更新
            uploaded_files = await self._upload_audio_files(task, audio_segments)

            # 4.5. 生成并上传元数据文件
            task.update_progress(85, "正在生成元数据文件")
            # 移除频繁的状态更新，只在关键节点更新
            await self._generate_and_upload_metadata(task, metadata, audio_segments)

            # 5. 生成播放列表（仅对多章节内容生成）
            playlist_urls = {}
            # 检查是否为单个TXT文件，如果是则跳过播放列表生成
            is_single_txt = (len(chapters) == 1 and
                           hasattr(task, 'original_filename') and
                           task.original_filename and
                           task.original_filename.lower().endswith('.txt'))

            if not is_single_txt:
                task.update_progress(90, "正在生成播放列表")
                # 移除频繁的状态更新，只在关键节点更新
                playlist_urls = await self._generate_and_upload_playlists(
                    task, metadata, chapters, uploaded_files
                )
            else:
                task.update_progress(90, "跳过播放列表生成（单个TXT文件）")
                # 移除频繁的状态更新，只在关键节点更新

            # 6. 整理结果
            task.audio_files = self._organize_results(
                uploaded_files, playlist_urls, chapters, task
            )

            # 7. 清理临时文件
            await self._cleanup_temp_files(file_path, audio_segments)

            # 8. 标记任务完成
            task.update_progress(100, "转换完成")
            task.mark_completed()
            logger.info(f"转换任务完成: {task.task_id}")

            return True

        except Exception as e:
            logger.error(f"转换任务失败: {task.task_id}, 错误: {str(e)}")
            task.mark_failed(str(e))
            return False

    async def _download_file(self, task: ConversionTask, file_type: str) -> Optional[str]:
        """下载文件到临时目录"""
        try:
            # 根据文件类型确定后缀
            suffix = f".{file_type}" if file_type in ['epub', 'txt'] else ".tmp"

            # 创建临时文件
            temp_file = tempfile.NamedTemporaryFile(suffix=suffix, delete=False)
            temp_path = temp_file.name
            temp_file.close()

            # 下载文件
            success = await self.storage_service.download_file(task.file_url, temp_path)

            if success:
                # 提取原始文件名
                if not task.original_filename:
                    task.original_filename = os.path.basename(task.file_url)

                return temp_path
            else:
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
                return None

        except Exception as e:
            logger.error(f"下载{file_type.upper()}文件失败: {str(e)}")
            return None

    async def _convert_to_audio(self, task: ConversionTask, chapters) -> List:
        """转换章节为音频文件"""
        try:
            # 转换语音设置
            tts_voice_settings = TTSVoiceSettings(
                model=task.voice_settings.voice_name,
                speed=int((task.voice_settings.speed - 1.0) * 10),  # 转换速度格式
                pitch=task.voice_settings.pitch,
                output_format="audio-24khz-160kbitrate-mono-mp3",
            )

            # 进度回调函数
            def progress_callback(current: int, total: int, stage: str):
                progress = 30 + int((current / total) * 50)  # 30-80%
                task.update_progress(progress, "正在转换音频", stage)
                task.processing_details.completed_segments = current

            # 执行转换
            audio_segments = await self.tts_service.convert_chapters_to_audio(
                chapters, tts_voice_settings, progress_callback
            )

            # 检查转换结果
            if not audio_segments:
                logger.error("音频转换失败：没有生成任何音频文件")
                raise Exception("音频转换失败")

            # 检查是否有成功的音频片段
            successful_segments = [seg for seg in audio_segments if seg.status == "completed"]
            if not successful_segments:
                logger.error("音频转换失败：所有音频片段转换都失败了")
                # 提供更详细的错误信息
                total_chars = sum(len(chapter.content) for chapter in chapters)
                if total_chars > 5000:
                    raise Exception("音频转换失败：文本内容可能过长或包含无法处理的特殊字符，请尝试使用较短的文本")
                else:
                    raise Exception("音频转换失败：文本内容可能包含TTS服务无法处理的特殊字符或敏感信息")

            return audio_segments

        except Exception as e:
            logger.error(f"音频转换失败: {str(e)}")
            raise

    async def _upload_audio_files(
        self, task: ConversionTask, audio_segments
    ) -> Dict[str, str]:
        """上传音频文件到存储服务"""
        try:
            upload_files = []

            for segment in audio_segments:
                if segment.status == "completed" and os.path.exists(segment.audio_path):
                    # 对于单个TXT文件，使用 audio.mp3 作为文件名
                    # 对于多章节EPUB，使用章节ID作为文件名
                    if len(audio_segments) == 1 and hasattr(task, 'original_filename') and task.original_filename and task.original_filename.lower().endswith('.txt'):
                        filename = "audio.mp3"
                    else:
                        filename = f"{segment.chapter_id}.mp3"

                    upload_files.append(
                        {
                            "local_path": segment.audio_path,
                            "filename": filename,
                            "chapter_id": segment.chapter_id,
                        }
                    )

            # 获取用户ID和原始任务ID
            user_id = getattr(task, 'user_id', None)
            original_task_id = getattr(task, 'original_task_id', task.task_id)

            # 批量上传，使用原始任务ID而不是转换任务ID
            uploaded_files = await self.storage_service.upload_audio_files(
                upload_files, original_task_id, user_id
            )

            return uploaded_files

        except Exception as e:
            logger.error(f"音频文件上传失败: {str(e)}")
            return {}

    async def _generate_and_upload_metadata(
        self, task: ConversionTask, metadata, audio_segments
    ) -> bool:
        """生成并上传元数据文件"""
        try:
            import json
            import tempfile

            # 计算总时长（这里先用估算，实际应该从音频文件获取）
            total_duration = 0
            for segment in audio_segments:
                if segment.status == "completed" and os.path.exists(segment.audio_path):
                    # 简单估算：每个字符约0.5秒（中文语音）
                    estimated_duration = len(segment.text) * 0.5
                    total_duration += estimated_duration

            # 获取语音设置
            voice_model = task.voice_settings.voice_name if hasattr(task, 'voice_settings') else "zh-CN-XiaochenMultilingualNeural"

            # 构建元数据
            metadata_content = {
                "wordCount": metadata.total_words if hasattr(metadata, 'total_words') else len(''.join([seg.text for seg in audio_segments])),
                "duration": int(total_duration),
                "language": metadata.language if hasattr(metadata, 'language') else "zh-CN",
                "voice": voice_model
            }

            # 创建临时文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as temp_file:
                json.dump(metadata_content, temp_file, ensure_ascii=False, indent=2)
                temp_path = temp_file.name

            try:
                # 获取存储路径信息
                user_id = getattr(task, 'user_id', None)
                original_task_id = getattr(task, 'original_task_id', task.task_id)

                # 生成存储路径
                remote_key = self.storage_service.generate_audio_storage_path(
                    user_id, original_task_id, "metadata.json"
                )

                # 上传元数据文件
                metadata_url = await self.storage_service.upload_file(
                    temp_path, remote_key, "application/json"
                )

                if metadata_url:
                    logger.info(f"元数据文件上传成功: {remote_key}")
                    return True
                else:
                    logger.error("元数据文件上传失败")
                    return False

            finally:
                # 清理临时文件
                if os.path.exists(temp_path):
                    os.unlink(temp_path)

        except Exception as e:
            logger.error(f"生成元数据文件失败: {str(e)}")
            return False

    async def _generate_and_upload_playlists(
        self, task: ConversionTask, metadata, chapters, uploaded_files: Dict[str, str]
    ) -> Dict[str, str]:
        """生成并上传多种格式的播放列表"""
        try:
            playlist_urls = {}

            # 创建播放列表包
            playlist_bundle = self.playlist_service.create_playlist_bundle(
                metadata, chapters, uploaded_files, task.task_id
            )

            # 获取用户ID和原始任务ID
            user_id = getattr(task, 'user_id', None)
            original_task_id = getattr(task, 'original_task_id', task.task_id)

            # 上传各种格式的播放列表
            for format_type, temp_file_path in playlist_bundle.items():
                try:
                    # 使用新的路径生成方法
                    playlist_filename = f"playlist.{format_type}"
                    if format_type == "hierarchical":
                        playlist_filename = "playlist_hierarchical.json"

                    remote_key = self.storage_service.generate_playlist_storage_path(
                        user_id, original_task_id, playlist_filename
                    )

                    # 确定内容类型
                    content_type = (
                        "application/json"
                        if format_type in ["json", "hierarchical"]
                        else "application/x-mpegURL"
                    )

                    # 上传文件
                    playlist_url = await self.storage_service.upload_file(
                        temp_file_path, remote_key, content_type
                    )

                    if playlist_url:
                        playlist_urls[format_type] = playlist_url
                        logger.info(
                            f"播放列表上传成功: {format_type} -> {playlist_url}"
                        )

                except Exception as e:
                    logger.error(f"上传{format_type}播放列表失败: {str(e)}")

            # 清理临时文件
            self.playlist_service.cleanup_temp_files(list(playlist_bundle.values()))

            return playlist_urls

        except Exception as e:
            logger.error(f"生成和上传播放列表失败: {str(e)}")
            return {}

    def _organize_results(
        self, uploaded_files: Dict[str, str], playlist_urls: Dict[str, str], chapters, task: ConversionTask
    ) -> Dict:
        """整理转换结果"""
        try:
            results = {"mp3": {"playlists": playlist_urls, "chapters": []}}

            # 获取用户ID和原始任务ID用于生成R2路径
            user_id = getattr(task, 'user_id', None)
            original_task_id = getattr(task, 'original_task_id', task.task_id)

            for chapter in chapters:
                if chapter.id in uploaded_files:
                    chapter_info = {
                        "title": chapter.title,
                        "url": uploaded_files[chapter.id],
                        "duration": "未知",  # TODO: 添加时长计算
                        "order": chapter.order,
                    }

                    # 添加R2路径信息
                    if user_id:
                        # 确定正确的文件名
                        # 对于单个TXT文件，使用 audio.mp3；对于多章节EPUB，使用章节ID
                        if len(chapters) == 1 and hasattr(task, 'original_filename') and task.original_filename and task.original_filename.lower().endswith('.txt'):
                            filename = "audio.mp3"
                        else:
                            filename = f"{chapter.id}.mp3"

                        # 生成R2路径，使用原始任务ID和正确的文件名
                        r2_path = self.storage_service.generate_audio_storage_path(
                            user_id, original_task_id, filename
                        )
                        chapter_info["r2_path"] = r2_path

                    results["mp3"]["chapters"].append(chapter_info)

            # 为播放列表也添加R2路径
            if user_id and playlist_urls:
                enhanced_playlists = {}
                for format_type, url in playlist_urls.items():
                    playlist_filename = f"playlist.{format_type}"
                    if format_type == "hierarchical":
                        playlist_filename = "playlist_hierarchical.json"

                    r2_path = self.storage_service.generate_playlist_storage_path(
                        user_id, original_task_id, playlist_filename
                    )

                    enhanced_playlists[format_type] = {
                        "url": url,
                        "r2_path": r2_path
                    }

                results["mp3"]["playlists"] = enhanced_playlists

            return results

        except Exception as e:
            logger.error(f"整理结果失败: {str(e)}")
            return {}

    async def _cleanup_temp_files(self, file_path: str, audio_segments):
        """清理临时文件"""
        try:
            # 删除文件临时文件
            if file_path and os.path.exists(file_path):
                os.unlink(file_path)

            # 清理音频临时文件
            if hasattr(self.tts_service, "cleanup_temp_files"):
                self.tts_service.cleanup_temp_files(audio_segments)

            logger.debug("临时文件清理完成")

        except Exception as e:
            logger.warning(f"清理临时文件失败: {str(e)}")
