// 文件上传 API
import { R2StorageManager } from '../../utils/r2-storage.js';
import { R2TaskStatusManager } from '../../utils/r2-task-status-manager.js';

export async function onRequestPost({ request, env }) {
    try {
        const formData = await request.formData();
        const file = formData.get('file');
        const email = formData.get('email');
        
        // 严格的参数验证
        if (!file) {
            return Response.json({ error: '文件不能为空' }, { status: 400 });
        }
        
        if (!email || typeof email !== 'string' || email.trim() === '') {
            return Response.json({ error: '邮箱不能为空' }, { status: 400 });
        }
        
        // 验证邮箱格式
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email.trim())) {
            return Response.json({ error: '邮箱格式无效' }, { status: 400 });
        }
        
        // 验证文件名
        if (!file.name || typeof file.name !== 'string' || file.name.trim() === '') {
            return Response.json({ error: '文件名无效' }, { status: 400 });
        }

        console.log(`文件上传请求: 邮箱=${email}, 文件名=${file.name}, 文件大小=${file.size}`);

        // 获取用户数据
        const userKey = `user:${email}`;
        const userData = await env.KV.get(userKey, { type: 'json' });
        
        if (!userData) {
            return Response.json({ error: '用户不存在' }, { status: 404 });
        }

        // 检查积分是否足够
        if (userData.points < 50) {
            return Response.json({ error: '积分不足，需要50积分' }, { status: 402 });
        }

        // 验证文件
        const allowedTypes = [
            'text/plain',                                                           // TXT
            'application/epub+zip',                                                // EPUB
            'application/pdf',                                                     // PDF
            'application/msword',                                                  // DOC
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'  // DOCX
        ];
        const maxSize = 50 * 1024 * 1024; // 50MB
        
        if (!allowedTypes.includes(file.type) && !file.name.match(/\.(txt|epub|pdf|doc|docx)$/i)) {
            return Response.json({ error: '支持的格式：TXT、EPUB、PDF、DOC、DOCX' }, { status: 400 });
        }
        
        if (file.size > maxSize) {
            return Response.json({ error: '文件大小不能超过 50MB' }, { status: 400 });
        }

        // 生成任务ID - 确保格式正确
        const taskId = generateTaskId();
        if (!taskId || typeof taskId !== 'string' || taskId.includes('undefined')) {
            throw new Error('任务ID生成失败');
        }
        
        console.log(`生成任务ID: ${taskId}`);
        
        const filename = file.name;
        
        // 初始化R2存储管理器
        const r2Storage = new R2StorageManager(env.R2);
        
        // 保存原始文件到R2
        const fileContent = await file.arrayBuffer();
        const filePath = await r2Storage.saveUploadedFile(
            email, 
            taskId, 
            filename, 
            fileContent, 
            file.type
        );
        
        // 创建任务记录（使用新的R2状态管理器）
        const statusManager = new R2TaskStatusManager(env.R2, env);

        const taskData = {
            id: taskId,
            email: email,
            filename: filename,
            type: 'file',
            fileSize: file.size,
            filePath: filePath
        };

        // 在R2中创建任务状态文件
        const taskStatus = await statusManager.createTaskStatus(taskData);

        console.log(`✅ 新任务已创建 (R2架构): ${taskId}`);

        // 为了向前兼容，暂时也保存到KV（后续可以移除）
        const legacyTask = {
            id: taskId,
            email: email,
            filename: filename,
            type: 'file',
            status: 'PENDING',
            createdAt: taskStatus.createdAt,
            progress: 0,
            fileSize: file.size,
            retryCount: 0,
            pointsCharged: false,
            filePath: filePath,
            useR2Status: true // 标记这个任务使用R2状态
        };

        const taskKey = `task:${taskId}`;
        await env.KV.put(taskKey, JSON.stringify(legacyTask));

        // 立即触发任务处理
        try {
            // 使用 fetch 调用处理API（异步执行）
            const processResponse = await fetch(new URL('/api/process-task', request.url), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ taskId })
            });
            
            console.log(`任务 ${taskId} 处理请求已发送, 状态: ${processResponse.status}`);
        } catch (processError) {
            console.error('触发任务处理失败:', processError);
            // 不影响上传结果，任务会在后续批量处理中被处理
        }

        return Response.json({
            success: true,
            taskId: taskId,
            points: userData.points, // 返回当前积分，未扣除
            message: '文件上传成功，开始转换'
        });

    } catch (error) {
        console.error('文件上传错误:', error);
        return Response.json({ error: '服务器内部错误' }, { status: 500 });
    }
}

// 生成任务ID
function generateTaskId() {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 9);
    return `${timestamp}_${random}`;
} 