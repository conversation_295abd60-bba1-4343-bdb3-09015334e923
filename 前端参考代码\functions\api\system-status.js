// 系统状态监控 API
export async function onRequestGet({ env }) {
    try {
        const timestamp = new Date().toISOString();
        
        // 获取所有任务进行分析
        const tasks = await getAllTasks(env);
        
        // 计算各种统计信息
        const statistics = calculateStatistics(tasks);
        const performance = calculatePerformance(tasks);
        const health = calculateHealthStatus(statistics, performance);
        
        // 新增：KV使用情况监控
        const kvUsage = await getKVUsageStats(env);
        
        const systemStatus = {
            timestamp,
            health,
            statistics,
            performance,
            kvUsage, // 新增KV使用统计
            storage: {
                provider: env.R2 ? 'Cloudflare R2' : 'Unknown',
                status: env.R2 ? 'AVAILABLE' : 'UNAVAILABLE'
            },
            recentTasks: tasks.slice(0, 10) // 最近10个任务
        };

        return Response.json({
            success: true,
            status: systemStatus
        });

    } catch (error) {
        console.error('获取系统状态失败:', error);
        return Response.json({ 
            error: '获取系统状态失败',
            timestamp: new Date().toISOString()
        }, { status: 500 });
    }
}

// 新增：获取KV使用统计
async function getKVUsageStats(env) {
    try {
        const now = Date.now();
        const oneHourAgo = now - (60 * 60 * 1000);
        
        // 估算KV使用情况（基于任务数量和API调用模式）
        const taskList = await env.KV.list({ prefix: 'task:', limit: 1000 });
        const totalTasks = taskList.keys.length;
        
        // 估算前端轮询的KV读取次数
        const avgTasksPerUser = totalTasks > 0 ? totalTasks / uniqueUsers : 0;
        const estimatedReadsPerPoll = Math.max(1, Math.min(21, Math.ceil(avgTasksPerUser) + 1)); // 1个list + N个get，最多21次
        
        // 新的优化轮询频率（120秒一次，且有严格条件限制）
        const pollsPerHour = 60 / 2; // 每120秒轮询一次 = 每小时30次
        const estimatedHourlyReads = pollsPerHour * estimatedReadsPerPoll;
        
        // 考虑条件限制：只有在有处理中任务且页面可见时才轮询
        const processingTasks = tasks.filter(t => t.status === 'PROCESSING').length;
        const activePollingReduction = processingTasks > 0 ? 1.0 : 0.1; // 如果没有处理中任务，轮询减少90%
        const finalEstimatedReads = Math.ceil(estimatedHourlyReads * activePollingReduction);
        
        return {
            totalTasks: totalTasks,
            estimatedActiveUsers: avgUsersActive,
            estimatedReadsPerHour: estimatedReadsPerHour,
            lastOptimizedAt: new Date().toISOString(),
            optimization: {
                status: 'ACTIVE',
                description: '前端轮询已从5秒优化为60秒',
                reductionRate: '92%', // 从5秒到60秒，减少了92%的请求
                maxTasksPerQuery: 20
            },
            alerts: estimatedReadsPerHour > 50000 ? [{
                level: 'WARNING',
                message: `预计每小时KV读取次数较高: ${estimatedReadsPerHour}`,
                suggestion: '考虑进一步优化查询频率或使用极简版API'
            }] : [],
            // 基础统计
            totalKVOperations: 'N/A (需要CF Analytics)',
            uniqueUsers,
            totalTasks,
            avgTasksPerUser: Math.round(avgTasksPerUser * 100) / 100,
            
            // 估算的轮询影响
            estimatedPollingReads: {
                readsPerPoll: estimatedReadsPerPoll,
                pollsPerHour,
                hourlyReads: finalEstimatedReads,
                dailyReads: finalEstimatedReads * 24,
                description: `每120秒轮询一次，每次读取约${estimatedReadsPerPoll}次KV，每小时约${finalEstimatedReads}次读取`
            },
        };
    } catch (error) {
        console.error('获取KV使用统计失败:', error);
        return {
            status: 'ERROR',
            error: error.message,
            estimatedReadsPerHour: 0
        };
    }
}

async function getAllTasks(env) {
    try {
        const tasks = [];
        const listResult = await env.KV.list({ prefix: 'task:', limit: 300 }); // 限制最多300个任务避免超时
        
        let processedCount = 0;
        const maxProcess = 100; // 最多处理100个任务以避免超时
        
        for (const key of listResult.keys) {
            if (processedCount >= maxProcess) break;
            
            try {
                const task = await env.KV.get(key.name, { type: 'json' });
                if (task) {
                    tasks.push({
                        ...task,
                        id: key.name.replace('task:', '')
                    });
                }
                processedCount++;
            } catch (error) {
                console.warn(`获取任务 ${key.name} 失败:`, error);
                processedCount++;
            }
        }
        
        console.log(`系统状态查询：处理了 ${processedCount} 个任务，返回 ${tasks.length} 个有效任务`);
        
        // 按创建时间排序
        return tasks.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    } catch (error) {
        console.error('获取所有任务失败:', error);
        return [];
    }
}

function calculateStats(tasks, oneDayAgo, oneWeekAgo) {
    const stats = {
        total: {
            tasks: tasks.length,
            success: 0,
            failed: 0,
            processing: 0,
            pending: 0
        },
        today: {
            tasks: 0,
            success: 0,
            failed: 0,
            processing: 0,
            pending: 0
        },
        week: {
            tasks: 0,
            success: 0,
            failed: 0,
            processing: 0,
            pending: 0
        },
        types: {
            epub: 0,
            url: 0,
            enhanced: 0
        },
        users: new Set(),
        totalAudioSize: 0,
        totalWordCount: 0,
        averageProcessingTime: 0
    };
    
    let totalProcessingTime = 0;
    let completedTasks = 0;
    
    tasks.forEach(task => {
        const createdAt = new Date(task.createdAt);
        const isToday = createdAt >= oneDayAgo;
        const isThisWeek = createdAt >= oneWeekAgo;
        
        // 总计统计
        stats.total[task.status.toLowerCase()]++;
        
        // 今日统计
        if (isToday) {
            stats.today.tasks++;
            stats.today[task.status.toLowerCase()]++;
        }
        
        // 本周统计
        if (isThisWeek) {
            stats.week.tasks++;
            stats.week[task.status.toLowerCase()]++;
        }
        
        // 任务类型统计
        if (task.type) {
            stats.types[task.type] = (stats.types[task.type] || 0) + 1;
        }
        
        // 用户统计
        if (task.email) {
            stats.users.add(task.email);
        }
        
        // 音频大小和字数统计
        if (task.audioSize) {
            stats.totalAudioSize += task.audioSize;
        }
        if (task.wordCount) {
            stats.totalWordCount += task.wordCount;
        }
        
        // 处理时间统计
        if (task.processingDuration && task.status === 'SUCCESS') {
            totalProcessingTime += task.processingDuration;
            completedTasks++;
        }
    });
    
    stats.users = stats.users.size;
    stats.averageProcessingTime = completedTasks > 0 ? Math.round(totalProcessingTime / completedTasks) : 0;
    
    return stats;
}

function checkSystemHealth(tasks, stats) {
    const health = {
        status: 'HEALTHY',
        issues: [],
        score: 100
    };
    
    // 检查失败率
    const recentTasks = tasks.slice(0, 50); // 最近50个任务
    const failureRate = recentTasks.length > 0 ? 
        (recentTasks.filter(t => t.status === 'FAILED').length / recentTasks.length) * 100 : 0;
    
    if (failureRate > 20) {
        health.issues.push({
            type: 'HIGH_FAILURE_RATE',
            message: `最近任务失败率过高: ${failureRate.toFixed(1)}%`,
            severity: 'CRITICAL'
        });
        health.score -= 30;
    } else if (failureRate > 10) {
        health.issues.push({
            type: 'MODERATE_FAILURE_RATE',
            message: `任务失败率偏高: ${failureRate.toFixed(1)}%`,
            severity: 'WARNING'
        });
        health.score -= 15;
    }
    
    // 检查卡住的任务
    const stuckTasks = tasks.filter(task => {
        if (task.status !== 'PROCESSING') return false;
        const now = new Date();
        const lastUpdate = new Date(task.lastProgressUpdate || task.createdAt);
        return (now - lastUpdate) > 30 * 60 * 1000; // 30分钟
    });
    
    if (stuckTasks.length > 0) {
        health.issues.push({
            type: 'STUCK_TASKS',
            message: `发现 ${stuckTasks.length} 个卡住的任务`,
            severity: stuckTasks.length > 5 ? 'CRITICAL' : 'WARNING'
        });
        health.score -= stuckTasks.length > 5 ? 25 : 10;
    }
    
    // 检查处理中任务数量
    const processingTasks = tasks.filter(t => t.status === 'PROCESSING').length;
    if (processingTasks > 10) {
        health.issues.push({
            type: 'HIGH_PROCESSING_LOAD',
            message: `当前有 ${processingTasks} 个任务正在处理`,
            severity: 'WARNING'
        });
        health.score -= 10;
    }
    
    // 检查平均处理时间
    if (stats.averageProcessingTime > 10 * 60 * 1000) { // 10分钟
        health.issues.push({
            type: 'SLOW_PROCESSING',
            message: `平均处理时间过长: ${Math.round(stats.averageProcessingTime / 60000)} 分钟`,
            severity: 'WARNING'
        });
        health.score -= 10;
    }
    
    // 确定整体健康状态
    if (health.score >= 90) {
        health.status = 'HEALTHY';
    } else if (health.score >= 70) {
        health.status = 'WARNING';
    } else {
        health.status = 'CRITICAL';
    }
    
    return health;
}

function calculatePerformanceMetrics(tasks) {
    const completedTasks = tasks.filter(t => t.status === 'SUCCESS');
    const failedTasks = tasks.filter(t => t.status === 'FAILED');
    
    if (completedTasks.length === 0) {
        return {
            averageProcessingTime: 0,
            averageWordsPerMinute: 0,
            successRate: 0,
            throughput: {
                hourly: 0,
                daily: 0
            }
        };
    }
    
    // 计算平均处理时间
    const totalProcessingTime = completedTasks.reduce((sum, task) => 
        sum + (task.processingDuration || 0), 0);
    const averageProcessingTime = totalProcessingTime / completedTasks.length;
    
    // 计算平均处理速度（字/分钟）
    const totalWords = completedTasks.reduce((sum, task) => sum + (task.wordCount || 0), 0);
    const totalTimeMinutes = totalProcessingTime / (1000 * 60);
    const averageWordsPerMinute = totalTimeMinutes > 0 ? totalWords / totalTimeMinutes : 0;
    
    // 计算成功率
    const totalTasks = completedTasks.length + failedTasks.length;
    const successRate = totalTasks > 0 ? (completedTasks.length / totalTasks) * 100 : 0;
    
    // 计算吞吐量
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    
    const hourlyTasks = tasks.filter(t => 
        t.status === 'SUCCESS' && new Date(t.completedAt) >= oneHourAgo).length;
    const dailyTasks = tasks.filter(t => 
        t.status === 'SUCCESS' && new Date(t.completedAt) >= oneDayAgo).length;
    
    return {
        averageProcessingTime: Math.round(averageProcessingTime),
        averageWordsPerMinute: Math.round(averageWordsPerMinute),
        successRate: Math.round(successRate * 100) / 100,
        throughput: {
            hourly: hourlyTasks,
            daily: dailyTasks
        }
    };
}

async function getStorageInfo(env) {
    try {
        // 这里可以添加R2存储使用情况的检查
        // 由于Cloudflare R2 API限制，我们只能提供基本信息
        return {
            provider: 'Cloudflare R2',
            status: 'AVAILABLE',
            // 实际使用情况需要通过其他方式获取
            usage: {
                note: '存储使用情况需要通过Cloudflare Dashboard查看'
            }
        };
    } catch (error) {
        return {
            provider: 'Cloudflare R2',
            status: 'ERROR',
            error: error.message
        };
    }
} 