// 极简任务处理器 - 只存储两个核心状态到KV，其余通过HF API获取
import { MinimalTaskManager } from '../utils/minimal-task-manager.js';
import { HuggingFaceApiClient } from '../utils/huggingface-api-client.js';
import { R2StorageManager } from '../utils/r2-storage.js';

export async function onRequestPost({ request, env }) {
    try {
        const { taskId } = await request.json();
        
        if (!taskId) {
            return Response.json({ error: '任务ID不能为空' }, { status: 400 });
        }

        // 异步处理任务，不阻塞响应
        processTaskMinimal(taskId, env).catch(error => {
            console.error(`任务 ${taskId} 处理失败:`, error);
        });

        return Response.json({
            success: true,
            message: '任务已开始处理，请通过状态API查询进度'
        });

    } catch (error) {
        console.error('启动任务处理失败:', error);
        return Response.json({ error: error.message }, { status: 500 });
    }
}

// 极简任务处理主函数
async function processTaskMinimal(taskId, env) {
    const taskManager = new MinimalTaskManager(taskId, env);
    const hfClient = new HuggingFaceApiClient();
    
    try {
        console.log(`开始极简处理任务: ${taskId}`);
        
        // 1. 获取任务基本信息（必须存在上传成功状态）
        const task = await taskManager.getStatus();
        if (!task) {
            throw new Error('任务不存在');
        }

        if (task.status !== 'UPLOAD_SUCCESS') {
            throw new Error(`任务状态错误: ${task.status}`);
        }

        console.log(`任务 ${taskId} 上传成功，开始转换处理`);

        // 2. 准备文件URL（生成R2的公开访问URL）
        const r2Storage = new R2StorageManager(env.R2);
        const fileUrl = await r2Storage.getPublicUrl(task.filePath);
        
        console.log(`文件URL已生成: ${fileUrl}`);

        // 3. 提交到Hugging Face进行转换
        console.log('向Hugging Face提交转换任务');
        const hfResult = await hfClient.submitConversionTask(fileUrl, {
            language: "zh-CN",
            voiceName: "zh-CN-XiaochenMultilingualNeural",
            speed: 1.0,
            audioQuality: "high",
            splitByChapter: true
        });

        const hfTaskId = hfResult.hfTaskId;
        console.log(`HF任务已提交: ${hfTaskId}, 开始轮询结果`);

        // 4. 轮询HF任务直到完成
        const result = await hfClient.pollTaskUntilComplete(hfTaskId, {
            pollInterval: 10000, // 10秒轮询一次
            maxPolls: 360, // 最多轮询1小时
            onProgress: async (status) => {
                // 只在控制台输出进度，不存储到KV
                console.log(`HF任务进度: ${status.progress}% - ${status.currentStage}`);
            }
        });

        console.log(`HF任务完成，处理结果:`, {
            status: result.status,
            audioFilesCount: Object.keys(result.audioFiles || {}).length
        });

        // 5. 处理转换结果
        const audioData = result.audioFiles?.mp3;
        if (!audioData) {
            throw new Error('转换结果中没有音频文件');
        }

        // 6. 保存音频文件路径和元数据到R2
        const audioPath = await saveAudioMetadata(taskId, result, env);

        // 7. 标记转换成功（只写入KV一次）
        await taskManager.markConversionSuccess({
            audioPath: audioPath,
            totalDuration: result.metadata?.total_duration || '未知',
            chapterCount: audioData.chapters?.length || 1
        });

        // 8. 处理用户积分扣除
        await processUserPoints(task, result, env);

        console.log(`任务 ${taskId} 极简处理完成`);

    } catch (error) {
        console.error(`任务 ${taskId} 处理失败:`, error);
        
        // 标记失败状态
        await taskManager.markFailed(error, 'conversion');
    }
}

// 保存音频元数据到R2
async function saveAudioMetadata(taskId, hfResult, env) {
    try {
        const r2Storage = new R2StorageManager(env.R2);
        
        // 构建音频元数据
        const audioMetadata = {
            taskId: taskId,
            hfTaskId: hfResult.taskId,
            audioFiles: hfResult.audioFiles,
            metadata: hfResult.metadata,
            createdAt: new Date().toISOString(),
            source: 'huggingface_api'
        };

        // 保存到R2
        const metadataPath = `audio-metadata/${taskId}/metadata.json`;
        await r2Storage.saveFile(metadataPath, JSON.stringify(audioMetadata, null, 2), 'application/json');
        
        console.log(`音频元数据已保存: ${metadataPath}`);
        return metadataPath;

    } catch (error) {
        console.error('保存音频元数据失败:', error);
        throw error;
    }
}

// 处理用户积分扣除
async function processUserPoints(task, hfResult, env) {
    try {
        const userKey = `user:${task.email}`;
        const userData = await env.KV.get(userKey, { type: 'json' });
        
        if (!userData) {
            console.log('用户数据不存在，跳过积分扣除');
            return;
        }

        // 计算积分（根据处理的内容长度）
        const wordCount = hfResult.metadata?.total_words || 1000; // 默认值
        const pointsToCharge = Math.max(50, Math.ceil(wordCount / 1000) * 10);

        if (userData.points >= pointsToCharge) {
            // 扣除积分
            userData.points -= pointsToCharge;
            userData.totalSpent = (userData.totalSpent || 0) + pointsToCharge;
            userData.lastSpentAt = new Date().toISOString();

            await env.KV.put(userKey, JSON.stringify(userData));

            // 记录积分使用记录
            const recordKey = `points_record:${task.email}:${Date.now()}`;
            const pointsRecord = {
                email: task.email,
                amount: -pointsToCharge,
                type: 'audio_conversion',
                taskId: task.id,
                description: `音频转换 - ${task.filename}`,
                wordCount: wordCount,
                timestamp: new Date().toISOString()
            };

            await env.KV.put(recordKey, JSON.stringify(pointsRecord));
            console.log(`用户积分已扣除: ${pointsToCharge} (剩余: ${userData.points})`);
        } else {
            console.log(`用户积分不足: 需要${pointsToCharge}, 余额${userData.points}`);
        }

    } catch (error) {
        console.error('处理用户积分失败:', error);
        // 积分处理失败不影响主流程
    }
}

// 获取任务列表（极简版）
export async function onRequestGet({ env }) {
    try {
        console.log('获取极简任务列表');
        
        const taskList = await env.KV.list({ prefix: 'task:' });
        const tasks = [];

        for (const key of taskList.keys) {
            try {
                const task = await env.KV.get(key.name, { type: 'json' });
                if (task) {
                    // 只返回基本状态信息
                    tasks.push({
                        id: task.id,
                        filename: task.filename,
                        email: task.email,
                        status: task.status,
                        createdAt: task.createdAt,
                        completedAt: task.completedAt,
                        failedAt: task.failedAt,
                        metadata: task.metadata
                    });
                }
            } catch (error) {
                console.error(`获取任务 ${key.name} 失败:`, error);
            }
        }

        // 按创建时间倒序排列
        tasks.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        return Response.json({
            success: true,
            tasks: tasks,
            total: tasks.length,
            message: '极简任务列表，详细进度请通过HF API获取'
        });

    } catch (error) {
        console.error('获取任务列表失败:', error);
        return Response.json({ error: error.message }, { status: 500 });
    }
}

// 获取单个任务的详细状态（结合KV和HF API）
export async function getTaskDetailedStatus(taskId, env) {
    try {
        const taskManager = new MinimalTaskManager(taskId, env);
        const detailedStatus = await taskManager.getDetailedStatus();
        
        if (!detailedStatus) {
            return { error: '任务不存在' };
        }

        return {
            success: true,
            task: detailedStatus
        };

    } catch (error) {
        console.error(`获取任务详细状态失败 (${taskId}):`, error);
        return { error: error.message };
    }
} 