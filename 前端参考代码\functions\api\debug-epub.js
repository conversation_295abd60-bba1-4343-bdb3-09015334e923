// EPUB文件调试工具 - 诊断EPUB解析问题（增强版）
import { EpubParser } from '../utils/epub-parser.js';

export async function onRequestPost({ request, env }) {
    const startTime = Date.now();
    
    try {
        const formData = await request.formData();
        const file = formData.get('file');
        
        if (!file) {
            return Response.json({ error: '请上传EPUB文件' }, { status: 400 });
        }

        console.log('=== EPUB调试开始 ===');
        console.log(`文件名: ${file.name}`);
        console.log(`文件大小: ${file.size} bytes`);
        console.log(`文件类型: ${file.type}`);
        
        const debugResults = {
            timestamp: new Date().toISOString(),
            filename: file.name,
            fileSize: file.size,
            fileType: file.type,
            steps: [],
            performance: {
                startTime: startTime,
                totalDuration: 0
            }
        };

        // 步骤1: 检查文件基本信息
        await addDebugStep(debugResults, 1, '检查文件基本信息', async () => {
            const isEpub = file.name.toLowerCase().endsWith('.epub') || file.type === 'application/epub+zip';
            
            const result = {
                isValidEpub: isEpub,
                success: true
            };
            
            if (!isEpub) {
                result.warning = '文件可能不是标准EPUB格式';
            }
            
            return result;
        });

        // 步骤2: 读取文件内容
        let fileContent;
        await addDebugStep(debugResults, 2, '读取文件内容', async () => {
            fileContent = await file.arrayBuffer();
            return {
                contentSize: fileContent.byteLength,
                success: true
            };
        });

        // 步骤3: 分析文件结构
        await addDebugStep(debugResults, 3, '分析EPUB文件结构', async () => {
            // 检查ZIP文件头
            const uint8Array = new Uint8Array(fileContent);
            const isZip = uint8Array[0] === 0x50 && uint8Array[1] === 0x4B;
            
            const result = {
                isZipFormat: isZip,
                fileHeader: Array.from(uint8Array.slice(0, 20)).map(b => b.toString(16).padStart(2, '0')).join(' '),
                fileHeaderAnalysis: analyzeFileHeader(uint8Array.slice(0, 20))
            };
            
            if (!isZip) {
                result.error = 'EPUB文件应该是ZIP格式，但检测到的文件头不符合ZIP格式';
                result.success = false;
            } else {
                result.success = true;
            }
            
            return result;
        });

        // 步骤4: 使用增强版EPUB解析器（带超时）
        await addDebugStep(debugResults, 4, '使用增强版EPUB解析器解析', async () => {
            const epubParser = new EpubParser();
            
            // 设置5分钟超时
            const parsePromise = epubParser.parseEpub(fileContent);
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => {
                    reject(new Error('EPUB解析超时（5分钟）'));
                }, 5 * 60 * 1000);
            });
            
            const chapters = await Promise.race([parsePromise, timeoutPromise]);
            
            return {
                success: true,
                chaptersCount: chapters.length,
                totalTextLength: chapters.reduce((sum, ch) => sum + ch.textContent.length, 0),
                chapters: chapters.map(chapter => ({
                    chapterNum: chapter.chapterNum,
                    title: chapter.title,
                    filename: chapter.filename,
                    textLength: chapter.textContent.length,
                    href: chapter.href,
                    mediaType: chapter.mediaType,
                    textPreview: chapter.textContent.substring(0, 200) + '...'
                }))
            };
        });

        // 步骤5: 性能分析
        await addDebugStep(debugResults, 5, '性能分析', async () => {
            const totalDuration = Date.now() - startTime;
            debugResults.performance.totalDuration = totalDuration;
            
            return {
                success: true,
                totalDuration: totalDuration,
                averageStepDuration: Math.round(totalDuration / debugResults.steps.length),
                performanceRating: getPerformanceRating(totalDuration, fileContent.byteLength),
                recommendations: getPerformanceRecommendations(totalDuration, fileContent.byteLength)
            };
        });

        // 生成诊断报告
        const diagnosis = generateEnhancedEpubDiagnosis(debugResults);
        debugResults.diagnosis = diagnosis;

        console.log('=== EPUB调试完成 ===');
        console.log(`总耗时: ${Math.round((Date.now() - startTime) / 1000)}秒`);
        console.log('诊断结果:', diagnosis);

        return Response.json(debugResults, {
            headers: {
                'Content-Type': 'application/json; charset=utf-8'
            }
        });

    } catch (error) {
        const totalDuration = Date.now() - startTime;
        console.error('EPUB调试失败:', error);
        
        return Response.json({
            error: error.message,
            stack: error.stack,
            duration: totalDuration,
            timestamp: new Date().toISOString()
        }, { 
            status: 500,
            headers: {
                'Content-Type': 'application/json; charset=utf-8'
            }
        });
    }
}

/**
 * 添加调试步骤的辅助函数
 */
async function addDebugStep(debugResults, stepNumber, stepName, stepFunction) {
    const stepStartTime = Date.now();
    
    const step = {
        step: stepNumber,
        name: stepName,
        status: 'running',
        startTime: stepStartTime
    };
    
    debugResults.steps.push(step);
    
    try {
        const result = await stepFunction();
        const stepDuration = Date.now() - stepStartTime;
        
        Object.assign(step, {
            status: 'completed',
            duration: stepDuration,
            ...result
        });
        
        console.log(`✅ 步骤${stepNumber} (${stepName}) 完成，耗时: ${stepDuration}ms`);
        
    } catch (error) {
        const stepDuration = Date.now() - stepStartTime;
        
        Object.assign(step, {
            status: 'failed',
            duration: stepDuration,
            error: error.message,
            stack: error.stack,
            success: false
        });
        
        console.error(`❌ 步骤${stepNumber} (${stepName}) 失败，耗时: ${stepDuration}ms:`, error);
        throw error; // 重新抛出错误以停止后续步骤
    }
}

/**
 * 分析文件头
 */
function analyzeFileHeader(headerBytes) {
    const analysis = {
        isZip: false,
        isPdf: false,
        isText: false,
        encoding: 'unknown'
    };
    
    // ZIP文件头: 50 4B (PK)
    if (headerBytes[0] === 0x50 && headerBytes[1] === 0x4B) {
        analysis.isZip = true;
        analysis.zipVersion = `${headerBytes[2]}.${headerBytes[3]}`;
    }
    
    // PDF文件头: 25 50 44 46 (%PDF)
    if (headerBytes[0] === 0x25 && headerBytes[1] === 0x50 && 
        headerBytes[2] === 0x44 && headerBytes[3] === 0x46) {
        analysis.isPdf = true;
    }
    
    // 检查是否为文本文件
    const textBytes = headerBytes.filter(b => b >= 32 && b <= 126).length;
    if (textBytes > headerBytes.length * 0.8) {
        analysis.isText = true;
    }
    
    // 检查UTF-8 BOM
    if (headerBytes[0] === 0xEF && headerBytes[1] === 0xBB && headerBytes[2] === 0xBF) {
        analysis.encoding = 'UTF-8 with BOM';
    }
    
    return analysis;
}

/**
 * 获取性能评级
 */
function getPerformanceRating(duration, fileSize) {
    const sizeInMB = fileSize / (1024 * 1024);
    const durationInSeconds = duration / 1000;
    const processingSpeed = sizeInMB / durationInSeconds; // MB/s
    
    if (processingSpeed > 5) return 'excellent';
    if (processingSpeed > 2) return 'good';
    if (processingSpeed > 0.5) return 'fair';
    return 'poor';
}

/**
 * 获取性能建议
 */
function getPerformanceRecommendations(duration, fileSize) {
    const recommendations = [];
    const sizeInMB = fileSize / (1024 * 1024);
    const durationInSeconds = duration / 1000;
    
    if (durationInSeconds > 300) { // 超过5分钟
        recommendations.push('处理时间过长，建议检查文件是否过大或包含大量图片');
    }
    
    if (sizeInMB > 50) {
        recommendations.push('文件较大，建议考虑将EPUB转换为纯文本格式以提高处理速度');
    }
    
    if (durationInSeconds > 60 && sizeInMB < 10) {
        recommendations.push('文件不大但处理时间较长，可能存在复杂的压缩或格式问题');
    }
    
    return recommendations;
}

/**
 * 生成增强版诊断报告
 */
function generateEnhancedEpubDiagnosis(debugResults) {
    const diagnosis = {
        overall: 'unknown',
        issues: [],
        recommendations: [],
        performance: {
            rating: 'unknown',
            bottlenecks: []
        }
    };
    
    const steps = debugResults.steps;
    const failedSteps = steps.filter(step => step.status === 'failed');
    const completedSteps = steps.filter(step => step.status === 'completed');
    
    // 整体状态评估
    if (failedSteps.length === 0) {
        diagnosis.overall = 'success';
    } else if (failedSteps.length < steps.length / 2) {
        diagnosis.overall = 'partial_success';
    } else {
        diagnosis.overall = 'failure';
    }
    
    // 分析具体问题
    failedSteps.forEach(step => {
        diagnosis.issues.push({
            step: step.step,
            name: step.name,
            error: step.error,
            duration: step.duration
        });
    });
    
    // 性能分析
    const performanceStep = steps.find(step => step.name === '性能分析');
    if (performanceStep && performanceStep.success) {
        diagnosis.performance.rating = performanceStep.performanceRating;
        diagnosis.recommendations.push(...performanceStep.recommendations);
    }
    
    // 识别性能瓶颈
    const slowSteps = completedSteps.filter(step => step.duration > 30000); // 超过30秒
    slowSteps.forEach(step => {
        diagnosis.performance.bottlenecks.push({
            step: step.name,
            duration: step.duration,
            impact: step.duration > 60000 ? 'high' : 'medium'
        });
    });
    
    // 生成建议
    if (diagnosis.overall === 'failure') {
        diagnosis.recommendations.push('EPUB文件解析完全失败，建议检查文件完整性或尝试转换为其他格式');
    } else if (diagnosis.overall === 'partial_success') {
        diagnosis.recommendations.push('EPUB文件部分解析成功，可能存在格式兼容性问题');
    }
    
    if (diagnosis.performance.bottlenecks.length > 0) {
        diagnosis.recommendations.push('检测到性能瓶颈，建议优化文件或使用更高性能的处理环境');
    }
    
    return diagnosis;
}

// GET请求处理 - 返回调试页面
export async function onRequestGet({ request, env }) {
    const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EPUB文件调试工具</title>
    <style>
        body { 
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', Arial, sans-serif; 
            max-width: 1200px; 
            margin: 0 auto; 
            padding: 20px; 
            line-height: 1.6;
            background-color: #f8f9fa;
        }
        .debug-section { 
            margin: 20px 0; 
            padding: 20px; 
            border: 1px solid #ddd; 
            border-radius: 8px; 
            background-color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .step { 
            margin: 15px 0; 
            padding: 15px; 
            background: #f8f9fa; 
            border-radius: 5px; 
            border-left: 4px solid #007bff;
        }
        .step.success { border-left-color: #28a745; }
        .step.error { border-left-color: #dc3545; }
        pre { 
            background: #f1f1f1; 
            padding: 15px; 
            border-radius: 5px; 
            overflow-x: auto; 
            font-size: 12px; 
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        button { 
            background: #007bff; 
            color: white; 
            border: none; 
            padding: 12px 24px; 
            border-radius: 5px; 
            cursor: pointer; 
            font-size: 16px;
            transition: background-color 0.3s;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .file-input { margin: 15px 0; }
        .file-input input[type="file"] {
            padding: 8px;
            border: 2px dashed #007bff;
            border-radius: 5px;
            background: #f8f9fa;
            width: 100%;
            max-width: 400px;
        }
        .preview-text { 
            max-height: 300px; 
            overflow-y: auto; 
            background: #f8f9fa; 
            padding: 15px; 
            border-radius: 5px; 
            border: 1px solid #e9ecef;
            font-family: 'Microsoft YaHei', 'PingFang SC', Arial, sans-serif;
            line-height: 1.8;
            word-wrap: break-word;
            white-space: pre-wrap;
        }
        .chapter-info {
            margin: 10px 0; 
            padding: 15px; 
            border: 1px solid #ddd; 
            border-radius: 5px;
            background: #ffffff;
        }
        .chapter-info h5 {
            margin: 0 0 10px 0;
            color: #007bff;
            font-size: 16px;
        }
        .comparison-box {
            margin-top: 15px; 
            padding: 15px; 
            background: #e9ecef; 
            border-radius: 5px;
            border-left: 4px solid #28a745;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #6c757d;
        }
        .loading::after {
            content: '...';
            animation: dots 1.5s steps(5, end) infinite;
        }
        @keyframes dots {
            0%, 20% { color: rgba(0,0,0,0); text-shadow: .25em 0 0 rgba(0,0,0,0), .5em 0 0 rgba(0,0,0,0); }
            40% { color: #6c757d; text-shadow: .25em 0 0 rgba(0,0,0,0), .5em 0 0 rgba(0,0,0,0); }
            60% { text-shadow: .25em 0 0 #6c757d, .5em 0 0 rgba(0,0,0,0); }
            80%, 100% { text-shadow: .25em 0 0 #6c757d, .5em 0 0 #6c757d; }
        }
    </style>
</head>
<body>
    <h1>📚 EPUB文件调试工具</h1>
    
    <div class="debug-section">
        <h2>上传EPUB文件进行诊断</h2>
        <p>这个工具可以帮助诊断EPUB文件解析和文本提取的问题，支持压缩和未压缩的EPUB格式。</p>
        
        <div class="file-input">
            <label for="epubFile"><strong>选择EPUB文件：</strong></label><br>
            <input type="file" id="epubFile" accept=".epub" />
        </div>
        
        <button onclick="runEpubDebug()" id="debugBtn">🔍 开始调试</button>
    </div>
    
    <div id="results"></div>

    <script>
        async function runEpubDebug() {
            const fileInput = document.getElementById('epubFile');
            const debugBtn = document.getElementById('debugBtn');
            const file = fileInput.files[0];
            
            if (!file) {
                alert('请选择一个EPUB文件');
                return;
            }
            
            const resultsDiv = document.getElementById('results');
            debugBtn.disabled = true;
            debugBtn.textContent = '🔄 分析中...';
            resultsDiv.innerHTML = '<div class="debug-section loading">正在分析EPUB文件，请稍候</div>';
            
            try {
                const formData = new FormData();
                formData.append('file', file);
                
                const response = await fetch('/api/debug-epub', {
                    method: 'POST',
                    body: formData
                });
                
                const results = await response.json();
                displayEpubResults(results);
                
            } catch (error) {
                resultsDiv.innerHTML = \`<div class="debug-section error">
                    <h3>❌ 调试失败</h3>
                    <p><strong>错误:</strong> \${escapeHtml(error.message)}</p>
                </div>\`;
            } finally {
                debugBtn.disabled = false;
                debugBtn.textContent = '🔍 开始调试';
            }
        }
        
        function displayEpubResults(results) {
            const resultsDiv = document.getElementById('results');
            
            let html = \`<div class="debug-section">
                <h2>EPUB调试结果</h2>
                <p><strong>文件名:</strong> \${results.filename}</p>
                <p><strong>文件大小:</strong> \${(results.fileSize / 1024).toFixed(2)} KB</p>
                <p><strong>文件类型:</strong> \${results.fileType}</p>
                <p><strong>分析时间:</strong> \${results.timestamp}</p>
            </div>\`;
            
            // 显示诊断结果
            if (results.diagnosis) {
                const diagClass = results.diagnosis.overall === 'excellent' ? 'success' : 
                                results.diagnosis.overall === 'good' ? 'success' :
                                results.diagnosis.overall === 'warning' ? 'warning' : 'error';
                
                html += \`<div class="debug-section \${diagClass}">
                    <h3>诊断结果</h3>
                    <p><strong>总体状态:</strong> \${results.diagnosis.overall}</p>\`;
                
                if (results.diagnosis.issues.length > 0) {
                    html += '<p><strong>发现的问题:</strong></p><ul>';
                    results.diagnosis.issues.forEach(issue => {
                        html += \`<li>\${escapeHtml(issue.name)}</li>\`;
                    });
                    html += '</ul>';
                }
                
                if (results.diagnosis.recommendations.length > 0) {
                    html += '<p><strong>建议:</strong></p><ul>';
                    results.diagnosis.recommendations.forEach(rec => {
                        html += \`<li>\${escapeHtml(rec)}</li>\`;
                    });
                    html += '</ul>';
                }
                
                // 显示性能分析
                if (results.performance) {
                    html += \`<div class="comparison-box">
                        <h4>性能分析</h4>
                        <p><strong>性能评级:</strong> \${results.performance.rating}</p>
                        <p><strong>总耗时:</strong> \${results.performance.totalDuration} ms</p>
                        <p><strong>平均每步骤耗时:</strong> \${results.performance.averageStepDuration} ms</p>
                    </div>\`;
                }
                
                html += '</div>';
            }
            
            // 显示详细步骤
            html += '<div class="debug-section"><h3>详细分析步骤</h3>';
            results.steps.forEach(step => {
                const stepClass = step.status === 'completed' && step.success ? 'success' : 
                                step.status === 'failed' ? 'error' : '';
                
                html += \`<div class="step \${stepClass}">
                    <h4>步骤 \${step.step}: \${step.name}</h4>
                    <p><strong>状态:</strong> \${step.status}</p>\`;
                
                if (step.error) {
                    html += \`<p><strong>错误:</strong> \${escapeHtml(step.error)}</p>\`;
                }
                
                if (step.warning) {
                    html += \`<p><strong>警告:</strong> \${escapeHtml(step.warning)}</p>\`;
                }
                
                if (step.rawContentPreview) {
                    html += \`<p><strong>原始内容预览:</strong></p>
                             <div class="preview-text"><pre>\${escapeHtml(step.rawContentPreview)}</pre></div>\`;
                }
                
                if (step.extractedTextPreview) {
                    html += \`<p><strong>提取文本预览 (当前方法, \${step.extractedTextLength}字符):</strong></p>
                             <div class="preview-text">\${escapeHtml(step.extractedTextPreview)}</div>\`;
                }
                
                // 显示新解析器的章节信息
                if (step.chapters && step.chapters.length > 0) {
                    html += \`<p><strong>提取的章节 (\${step.chaptersCount}个):</strong></p>\`;
                    html += '<div style="max-height: 400px; overflow-y: auto;">';
                    step.chapters.forEach((chapter, index) => {
                        html += \`<div class="chapter-info">
                            <h5>章节 \${chapter.chapterNum}: \${escapeHtml(chapter.title)}</h5>
                            <p><strong>文件名:</strong> \${escapeHtml(chapter.filename)}</p>
                            <p><strong>文本长度:</strong> \${chapter.textLength} 字符</p>
                            <p><strong>媒体类型:</strong> \${escapeHtml(chapter.mediaType)}</p>
                            <p><strong>内容预览:</strong></p>
                            <div class="preview-text" style="max-height: 150px; overflow-y: auto;">
                                \${escapeHtml(chapter.textPreview)}
                            </div>
                        </div>\`;
                    });
                    html += '</div>';
                }
                
                if (step.fileHeader) {
                    html += \`<p><strong>文件头:</strong> \${step.fileHeader}</p>\`;
                }
                
                html += '</div>';
            });
            html += '</div>';
            
            resultsDiv.innerHTML = html;
        }
        
        // HTML转义函数，防止XSS并正确显示特殊字符
        function escapeHtml(text) {
            if (typeof text !== 'string') {
                return text;
            }
            
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
    </script>
</body>
</html>`;

    return new Response(html, {
        headers: {
            'Content-Type': 'text/html; charset=utf-8'
        }
    });
} 