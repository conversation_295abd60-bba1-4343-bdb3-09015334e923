// 优化版任务状态管理器 - 减少KV写入频率
export class OptimizedTaskManager {
    constructor(taskId, env) {
        this.taskId = taskId;
        this.env = env;
        this.taskKey = `task:${taskId}`;
        
        // 内存中的任务状态缓存
        this.taskCache = null;
        this.lastKVWrite = 0;
        this.writeInterval = 10000; // 10秒最多写入一次
        
        // 需要立即写入KV的关键状态
        this.criticalStatuses = [
            'PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED'
        ];
        
        // 需要立即写入的关键进度点
        this.criticalProgressPoints = [0, 25, 50, 75, 100];
    }

    /**
     * 获取任务信息
     */
    async getTask() {
        if (!this.taskCache) {
            this.taskCache = await this.env.KV.get(this.taskKey, { type: 'json' });
        }
        return this.taskCache;
    }

    /**
     * 优化的任务更新 - 减少KV写入
     */
    async updateTask(updates, forceWrite = false) {
        // 更新内存缓存
        if (!this.taskCache) {
            this.taskCache = await this.env.KV.get(this.taskKey, { type: 'json' });
        }
        
        if (!this.taskCache) {
            throw new Error('任务不存在');
        }

        // 合并更新
        const oldStatus = this.taskCache.status;
        const oldProgress = this.taskCache.progress || 0;
        
        this.taskCache = {
            ...this.taskCache,
            ...updates,
            lastUpdated: new Date().toISOString()
        };

        // 判断是否需要立即写入KV
        const shouldWriteImmediately = this.shouldWriteImmediately(
            updates, 
            oldStatus, 
            oldProgress, 
            forceWrite
        );

        if (shouldWriteImmediately) {
            await this.writeToKV();
        } else {
            // 定期批量写入
            await this.writeIfNeeded();
        }

        return this.taskCache;
    }

    /**
     * 判断是否需要立即写入KV
     */
    shouldWriteImmediately(updates, oldStatus, oldProgress, forceWrite) {
        // 强制写入
        if (forceWrite) return true;

        // 状态发生关键变化
        if (updates.status && updates.status !== oldStatus && 
            this.criticalStatuses.includes(updates.status)) {
            return true;
        }

        // 进度达到关键点
        if (updates.progress !== undefined) {
            const newProgress = updates.progress;
            const crossedCriticalPoint = this.criticalProgressPoints.some(point => 
                oldProgress < point && newProgress >= point
            );
            if (crossedCriticalPoint) return true;
        }

        // 任务完成或失败
        if (updates.status === 'COMPLETED' || updates.status === 'FAILED') {
            return true;
        }

        // 发生错误
        if (updates.error) {
            return true;
        }

        return false;
    }

    /**
     * 定期写入检查
     */
    async writeIfNeeded() {
        const now = Date.now();
        if (now - this.lastKVWrite > this.writeInterval) {
            await this.writeToKV();
        }
    }

    /**
     * 写入KV存储
     */
    async writeToKV() {
        try {
            await this.env.KV.put(this.taskKey, JSON.stringify(this.taskCache));
            this.lastKVWrite = Date.now();
            console.log(`任务状态已写入KV: ${this.taskId}`, {
                status: this.taskCache.status,
                progress: this.taskCache.progress
            });
        } catch (error) {
            console.error('写入任务状态失败:', error);
            throw error;
        }
    }

    /**
     * 强制刷新到KV
     */
    async flush() {
        if (this.taskCache) {
            await this.writeToKV();
        }
    }

    /**
     * 更新进度 - 优化版本
     */
    async updateProgress(progress, stage = null, forceWrite = false) {
        const updates = {
            progress: Math.min(Math.max(progress, 0), 100), // 确保进度在0-100之间
            currentStage: stage,
            lastProgressUpdate: new Date().toISOString()
        };

        return await this.updateTask(updates, forceWrite);
    }

    /**
     * 更新状态
     */
    async updateStatus(status, additionalData = {}) {
        const updates = {
            status,
            ...additionalData
        };

        return await this.updateTask(updates, true); // 状态变化总是立即写入
    }

    /**
     * 标记任务开始
     */
    async markAsProcessing() {
        return await this.updateStatus('PROCESSING', {
            startedAt: new Date().toISOString(),
            progress: 0
        });
    }

    /**
     * 标记任务完成
     */
    async markAsCompleted(resultData = {}) {
        const result = await this.updateStatus('SUCCESS', {
            completedAt: new Date().toISOString(),
            progress: 100,
            ...resultData
        });

        // 创建任务完成标记文件（混合方案）
        try {
            const { R2StorageManager } = await import('./r2-storage.js');
            const r2Storage = new R2StorageManager(this.env.R2);

            const task = await this.getTask();
            if (task && task.email) {
                await r2Storage.createCompletionMarker(task.email, this.taskId, {
                    completedVia: 'optimized-task-manager',
                    ...resultData
                });
                console.log(`✅ 任务完成标记已创建 (优化版): ${this.taskId}`);
            }
        } catch (markerError) {
            console.error('创建完成标记失败 (优化版):', markerError);
            // 不影响主流程
        }

        return result;
    }

    /**
     * 标记任务失败
     */
    async markAsFailed(error, additionalData = {}) {
        return await this.updateStatus('FAILED', {
            error: error.message,
            failedAt: new Date().toISOString(),
            ...additionalData
        });
    }

    /**
     * 创建进度回调函数 - 优化版本
     */
    createProgressCallback() {
        let lastReportedProgress = -1;
        const progressThreshold = 5; // 只有进度变化超过5%才更新

        return async (progress, stage) => {
            // 减少频繁的进度更新
            if (Math.abs(progress - lastReportedProgress) >= progressThreshold || 
                progress === 100 || 
                this.criticalProgressPoints.includes(progress)) {
                
                await this.updateProgress(progress, stage);
                lastReportedProgress = progress;
                
                console.log(`进度更新: ${this.taskId} - ${progress}%${stage ? ` (${stage})` : ''}`);
            }
        };
    }

    /**
     * 获取任务统计信息
     */
    getTaskStats() {
        if (!this.taskCache) return null;

        const now = new Date();
        const createdAt = new Date(this.taskCache.createdAt);
        const startedAt = this.taskCache.startedAt ? new Date(this.taskCache.startedAt) : null;
        const completedAt = this.taskCache.completedAt ? new Date(this.taskCache.completedAt) : null;

        return {
            taskId: this.taskId,
            status: this.taskCache.status,
            progress: this.taskCache.progress || 0,
            totalDuration: completedAt ? completedAt - createdAt : now - createdAt,
            processingDuration: startedAt ? (completedAt || now) - startedAt : 0,
            waitingDuration: startedAt ? startedAt - createdAt : now - createdAt
        };
    }
} 