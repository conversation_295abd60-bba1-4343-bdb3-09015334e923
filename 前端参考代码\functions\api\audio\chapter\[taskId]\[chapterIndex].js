// 章节音频播放 API
import { EnhancedR2StorageManager } from '../../../../utils/enhanced-r2-storage.js';
import { UserIdResolver } from '../../../../utils/user-id-resolver.js';

export async function onRequestGet({ request, env, params }) {
    try {
        const { taskId, chapterIndex } = params;
        
        if (!taskId || !chapterIndex) {
            return new Response('缺少必要参数', { status: 400 });
        }

        const chapterNum = parseInt(chapterIndex.replace('.mp3', ''));
        if (isNaN(chapterNum) || chapterNum < 1) {
            return new Response('无效的章节索引', { status: 400 });
        }

        console.log(`章节音频API: 开始处理请求 - 任务ID: ${taskId}, 章节: ${chapterNum}`);

        // 首先尝试从请求头获取用户邮箱
        let email = request.headers.get('X-User-Email');
        
        // 如果没有请求头，尝试通过任务ID获取用户邮箱
        if (!email) {
            const taskKey = `task:${taskId}`;
            const task = await env.KV.get(taskKey, { type: 'json' });
            
            if (!task) {
                return new Response('任务不存在', { status: 404 });
            }
            
            // 验证任务状态
            if (task.status !== 'SUCCESS') {
                return new Response('任务未完成', { status: 403 });
            }
            
            // 验证是否为增强版任务
            if (!task.isEnhanced && !task.totalChapters) {
                return new Response('非增强版任务', { status: 403 });
            }
            
            email = task.email;
        }
        
        if (!email) {
            return new Response('无法确定用户身份', { status: 401 });
        }

        // 使用统一的用户ID解析器
        const userIdResolver = new UserIdResolver(env);
        const userIdResult = await userIdResolver.resolveUserId(email, taskId);
        
        if (!userIdResult.userId) {
            console.error(`章节音频API: 无法解析用户ID - 邮箱: ${email}`, userIdResult);
            return new Response('无法解析用户ID', { status: 500 });
        }
        
        console.log(`章节音频API: 用户ID解析成功 - 用户ID: ${userIdResult.userId}, 方法: ${userIdResult.method}, 置信度: ${userIdResult.confidence}%`);

        // 初始化存储管理器
        const r2Storage = new EnhancedR2StorageManager(env.R2);

        // 获取章节音频文件
        const audioObject = await r2Storage.getChapterAudio(email, taskId, chapterNum);
        
        if (!audioObject) {
            console.error(`章节音频API: 章节音频不存在 - 用户ID: ${userIdResult.userId}, 任务: ${taskId}, 章节: ${chapterNum}`);
            return new Response('章节音频不存在', { status: 404 });
        }

        console.log(`章节音频API: 成功获取章节音频 - 大小: ${audioObject.size} bytes`);

        // 处理Range请求（支持音频流播放）
        const range = request.headers.get('Range');
        
        if (range) {
            // 解析Range头
            const rangeMatch = range.match(/bytes=(\d+)-(\d*)/);
            if (!rangeMatch) {
                return new Response('无效的Range请求', { status: 416 });
            }

            const start = parseInt(rangeMatch[1]);
            const end = rangeMatch[2] ? parseInt(rangeMatch[2]) : undefined;

            // 先获取文件大小
            const fileSize = audioObject.size;
            const actualEnd = end !== undefined ? Math.min(end, fileSize - 1) : fileSize - 1;
            
            // 验证范围
            if (start >= fileSize || actualEnd >= fileSize || start > actualEnd) {
                return new Response('Range Not Satisfiable', {
                    status: 416,
                    headers: {
                        'Content-Range': `bytes */${fileSize}`,
                        'Access-Control-Allow-Origin': '*',
                        'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
                        'Access-Control-Allow-Headers': 'Range, X-User-Email'
                    }
                });
            }

            // 获取指定范围的音频数据
            const rangeAudioObject = await r2Storage.getChapterAudioRange(
                email, 
                taskId, 
                chapterNum, 
                start, 
                actualEnd
            );

            if (!rangeAudioObject) {
                return new Response('无法获取音频范围数据', { status: 416 });
            }

            return new Response(rangeAudioObject.body, {
                status: 206,
                headers: {
                    'Content-Type': 'audio/mpeg',
                    'Content-Length': (actualEnd - start + 1).toString(),
                    'Content-Range': `bytes ${start}-${actualEnd}/${fileSize}`,
                    'Accept-Ranges': 'bytes',
                    'Cache-Control': 'public, max-age=31536000',
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
                    'Access-Control-Allow-Headers': 'Range, X-User-Email'
                }
            });
        }

        // 普通请求，返回完整音频文件
        return new Response(audioObject.body, {
            headers: {
                'Content-Type': 'audio/mpeg',
                'Content-Length': audioObject.size.toString(),
                'Accept-Ranges': 'bytes',
                'Cache-Control': 'public, max-age=31536000',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
                'Access-Control-Allow-Headers': 'Range, X-User-Email'
            }
        });

    } catch (error) {
        console.error('获取章节音频失败:', error);
        return new Response('服务器内部错误', { status: 500 });
    }
}

// 处理OPTIONS请求（CORS预检）
export async function onRequestOptions() {
    return new Response(null, {
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
            'Access-Control-Allow-Headers': 'Range, X-User-Email',
            'Access-Control-Max-Age': '86400'
        }
    });
} 