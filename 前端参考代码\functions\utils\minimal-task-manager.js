// 极简任务管理器 - 只存储两个核心状态到KV
export class MinimalTaskManager {
    constructor(taskId, env) {
        this.taskId = taskId;
        this.env = env;
        this.taskKey = `task:${taskId}`;
    }

    /**
     * 标记上传成功状态
     */
    async markUploadSuccess(taskData) {
        const task = {
            id: this.taskId,
            email: taskData.email,
            filename: taskData.filename,
            filePath: taskData.filePath,
            status: 'UPLOAD_SUCCESS',
            createdAt: new Date().toISOString(),
            // 只存储必要的元数据
            metadata: {
                originalSize: taskData.fileSize,
                fileType: taskData.filename.split('.').pop().toLowerCase()
            }
        };

        await this.env.KV.put(this.taskKey, JSON.stringify(task));
        console.log(`任务上传成功: ${this.taskId}`);
        return task;
    }

    /**
     * 标记转换成功状态
     */
    async markConversionSuccess(resultData) {
        const task = await this.env.KV.get(this.taskKey, { type: 'json' });
        if (!task) {
            throw new Error('任务不存在');
        }

        task.status = 'CONVERSION_SUCCESS';
        task.completedAt = new Date().toISOString();
        task.audioPath = resultData.audioPath;
        task.metadata.totalDuration = resultData.totalDuration;
        task.metadata.chapterCount = resultData.chapterCount;

        await this.env.KV.put(this.taskKey, JSON.stringify(task));
        console.log(`任务转换成功: ${this.taskId}`);
        return task;
    }

    /**
     * 标记失败状态（上传失败或转换失败）
     */
    async markFailed(error, stage = 'unknown') {
        const task = await this.env.KV.get(this.taskKey, { type: 'json' });
        
        const updatedTask = {
            ...(task || { id: this.taskId }),
            status: stage === 'upload' ? 'UPLOAD_FAILED' : 'CONVERSION_FAILED',
            failedAt: new Date().toISOString(),
            error: error.message,
            errorStage: stage
        };

        await this.env.KV.put(this.taskKey, JSON.stringify(updatedTask));
        console.error(`任务失败 (${stage}): ${this.taskId}`, error);
        return updatedTask;
    }

    /**
     * 获取任务状态
     */
    async getStatus() {
        const task = await this.env.KV.get(this.taskKey, { type: 'json' });
        if (!task) {
            return null;
        }

        return {
            id: task.id,
            status: task.status,
            createdAt: task.createdAt,
            completedAt: task.completedAt,
            failedAt: task.failedAt,
            metadata: task.metadata
        };
    }

    /**
     * 获取详细状态 - 通过Hugging Face API获取实时进度
     */
    async getDetailedStatus() {
        const basicStatus = await this.getStatus();
        if (!basicStatus) {
            return null;
        }

        // 如果任务还在处理中，通过Hugging Face API获取实时状态
        if (basicStatus.status === 'UPLOAD_SUCCESS') {
            try {
                // 这里调用Hugging Face API获取实时进度
                const hfStatus = await this.getHuggingFaceStatus();
                return {
                    ...basicStatus,
                    realTimeProgress: hfStatus.progress,
                    currentStage: hfStatus.stage,
                    estimatedTime: hfStatus.estimatedTime
                };
            } catch (error) {
                console.error('获取HF状态失败:', error);
                return basicStatus;
            }
        }

        return basicStatus;
    }

    /**
     * 调用Hugging Face API获取实时状态
     */
    async getHuggingFaceStatus() {
        // 根据API文档实现具体的API调用
        // 这里是示例实现，需要根据实际API调整
        try {
            const response = await fetch(`https://fromozu-audiobooks-backend.hf.space/api/queue/status/${this.taskId}`);
            if (!response.ok) {
                throw new Error('API调用失败');
            }
            
            const data = await response.json();
            return {
                progress: data.progress || 0,
                stage: data.current_stage || '处理中',
                estimatedTime: data.estimated_duration || '未知'
            };
        } catch (error) {
            console.error('调用HF API失败:', error);
            return {
                progress: 0,
                stage: '状态未知',
                estimatedTime: '未知'
            };
        }
    }

    /**
     * 删除任务（可选的清理功能）
     */
    async delete() {
        await this.env.KV.delete(this.taskKey);
        console.log(`任务已删除: ${this.taskId}`);
    }
} 