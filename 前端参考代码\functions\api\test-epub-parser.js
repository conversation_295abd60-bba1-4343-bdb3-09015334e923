import { EpubParser } from '../utils/epub-parser.js';

export async function onRequestPost({ request, env }) {
    try {
        const formData = await request.formData();
        const file = formData.get('file');
        
        if (!file) {
            return Response.json({ error: '请上传EPUB文件' }, { status: 400 });
        }

        console.log('=== 新EPUB解析器测试开始 ===');
        console.log(`文件名: ${file.name}`);
        console.log(`文件大小: ${file.size} bytes`);
        console.log(`文件类型: ${file.type}`);
        
        const testResults = {
            timestamp: new Date().toISOString(),
            filename: file.name,
            fileSize: file.size,
            fileType: file.type,
            steps: []
        };

        // 步骤1: 读取文件内容
        testResults.steps.push({
            step: 1,
            name: '读取文件内容',
            status: 'running'
        });

        let fileContent;
        try {
            fileContent = await file.arrayBuffer();
            testResults.steps[0].status = 'completed';
            testResults.steps[0].contentSize = fileContent.byteLength;
            testResults.steps[0].success = true;
        } catch (error) {
            testResults.steps[0].status = 'failed';
            testResults.steps[0].error = error.message;
            return Response.json(testResults, { status: 500 });
        }

        // 步骤2: 使用新的EPUB解析器
        testResults.steps.push({
            step: 2,
            name: '使用新EPUB解析器解析',
            status: 'running'
        });

        try {
            const epubParser = new EpubParser();
            const chapters = await epubParser.parseEpub(fileContent);
            
            testResults.steps[1].status = 'completed';
            testResults.steps[1].success = true;
            testResults.steps[1].chaptersCount = chapters.length;
            testResults.steps[1].chapters = chapters.map(chapter => ({
                chapterNum: chapter.chapterNum,
                title: chapter.title,
                filename: chapter.filename,
                textLength: chapter.textContent.length,
                htmlLength: chapter.htmlContent.length,
                href: chapter.href,
                mediaType: chapter.mediaType,
                textPreview: chapter.textContent.substring(0, 200) + '...'
            }));
            
            // 计算总文本长度
            const totalTextLength = chapters.reduce((sum, chapter) => sum + chapter.textContent.length, 0);
            testResults.steps[1].totalTextLength = totalTextLength;
            
            console.log(`解析成功，共提取 ${chapters.length} 个章节，总文本长度: ${totalTextLength} 字符`);
            
        } catch (error) {
            testResults.steps[1].status = 'failed';
            testResults.steps[1].error = error.message;
            testResults.steps[1].success = false;
        }

        // 步骤3: 生成HTML章节结构
        if (testResults.steps[1].success) {
            testResults.steps.push({
                step: 3,
                name: '生成HTML章节结构',
                status: 'running'
            });

            try {
                const epubParser = new EpubParser();
                const chapters = await epubParser.parseEpub(fileContent);
                const htmlChapters = epubParser.createHtmlChapters(chapters, file.name);
                
                testResults.steps[2].status = 'completed';
                testResults.steps[2].success = true;
                testResults.steps[2].bookTitle = htmlChapters.bookTitle;
                testResults.steps[2].totalChapters = htmlChapters.totalChapters;
                testResults.steps[2].chapterFilenames = Object.keys(htmlChapters.chapters);
                
                console.log(`HTML章节结构生成成功，书籍标题: ${htmlChapters.bookTitle}`);
                
            } catch (error) {
                testResults.steps[2].status = 'failed';
                testResults.steps[2].error = error.message;
                testResults.steps[2].success = false;
            }
        }

        // 生成测试报告
        const testReport = generateTestReport(testResults);
        testResults.report = testReport;

        console.log('=== 新EPUB解析器测试完成 ===');
        console.log('测试结果:', testReport);

        return Response.json(testResults, {
            headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            }
        });

    } catch (error) {
        console.error('EPUB解析器测试失败:', error);
        return Response.json({
            error: error.message,
            stack: error.stack,
            timestamp: new Date().toISOString()
        }, { 
            status: 500,
            headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            }
        });
    }
}

// 生成测试报告
function generateTestReport(testResults) {
    const report = {
        overall: 'unknown',
        issues: [],
        successes: [],
        recommendations: []
    };

    // 检查解析结果
    const parseStep = testResults.steps[1];
    if (parseStep && parseStep.success) {
        report.successes.push(`成功解析EPUB文件，提取了 ${parseStep.chaptersCount} 个章节`);
        report.successes.push(`总文本长度: ${parseStep.totalTextLength} 字符`);
        
        if (parseStep.chaptersCount > 1) {
            report.overall = 'excellent';
            report.successes.push('成功识别多章节结构');
        } else {
            report.overall = 'good';
            report.recommendations.push('只识别到1个章节，可能是单章节EPUB或章节识别需要优化');
        }
        
        // 检查章节质量
        const shortChapters = parseStep.chapters.filter(ch => ch.textLength < 100);
        if (shortChapters.length > 0) {
            report.issues.push(`发现 ${shortChapters.length} 个过短的章节（少于100字符）`);
        }
        
    } else if (parseStep) {
        report.overall = 'failed';
        report.issues.push(`EPUB解析失败: ${parseStep.error}`);
        
        if (parseStep.error.includes('ZIP')) {
            report.recommendations.push('文件可能不是有效的EPUB格式或已损坏');
        } else if (parseStep.error.includes('OPF')) {
            report.recommendations.push('EPUB文件结构异常，缺少必要的元数据文件');
        }
    }

    // 检查HTML章节生成
    const htmlStep = testResults.steps[2];
    if (htmlStep && htmlStep.success) {
        report.successes.push('成功生成HTML章节结构');
    } else if (htmlStep) {
        report.issues.push(`HTML章节生成失败: ${htmlStep.error}`);
    }

    return report;
}

// GET请求处理 - 返回测试页面
export async function onRequestGet({ request, env }) {
    const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新EPUB解析器测试</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .excellent { background-color: #d1ecf1; border-color: #bee5eb; }
        .step { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 3px; }
        pre { background: #f1f1f1; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .file-input { margin: 10px 0; }
        .preview-text { max-height: 200px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 3px; }
        .chapter-list { max-height: 300px; overflow-y: auto; }
        .chapter-item { margin: 5px 0; padding: 8px; background: #e9ecef; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>新EPUB解析器测试工具</h1>
    
    <div class="test-section">
        <h2>测试新的EPUB解析算法</h2>
        <p>这个工具使用借鉴epub_extractor.py思路的新解析器来测试EPUB文件处理。</p>
        <p><strong>新算法特点：</strong></p>
        <ul>
            <li>正确解析ZIP文件结构</li>
            <li>查找并解析OPF元数据文件</li>
            <li>按照manifest顺序提取XHTML/HTML文档</li>
            <li>保持原有章节结构</li>
            <li>避免编码问题和乱码</li>
        </ul>
        
        <div class="file-input">
            <input type="file" id="epubFile" accept=".epub" />
        </div>
        
        <button onclick="runEpubTest()">开始测试</button>
    </div>
    
    <div id="results"></div>

    <script>
        async function runEpubTest() {
            const fileInput = document.getElementById('epubFile');
            const file = fileInput.files[0];
            
            if (!file) {
                alert('请选择一个EPUB文件');
                return;
            }
            
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="test-section">正在测试新EPUB解析器...</div>';
            
            try {
                const formData = new FormData();
                formData.append('file', file);
                
                const response = await fetch('/api/test-epub-parser', {
                    method: 'POST',
                    body: formData
                });
                
                const results = await response.json();
                displayTestResults(results);
                
            } catch (error) {
                resultsDiv.innerHTML = \`<div class="test-section error">
                    <h3>测试失败</h3>
                    <p>错误: \${error.message}</p>
                </div>\`;
            }
        }
        
        function displayTestResults(results) {
            const resultsDiv = document.getElementById('results');
            
            let html = \`<div class="test-section">
                <h2>测试结果</h2>
                <p><strong>文件名:</strong> \${results.filename}</p>
                <p><strong>文件大小:</strong> \${(results.fileSize / 1024).toFixed(2)} KB</p>
                <p><strong>测试时间:</strong> \${results.timestamp}</p>
            </div>\`;
            
            // 显示测试报告
            if (results.report) {
                const reportClass = results.report.overall === 'excellent' ? 'excellent' : 
                                  results.report.overall === 'good' ? 'success' : 
                                  results.report.overall === 'failed' ? 'error' : 'warning';
                
                html += \`<div class="test-section \${reportClass}">
                    <h3>测试报告</h3>
                    <p><strong>总体评价:</strong> \${results.report.overall}</p>\`;
                
                if (results.report.successes.length > 0) {
                    html += '<p><strong>成功项目:</strong></p><ul>';
                    results.report.successes.forEach(success => {
                        html += \`<li style="color: green;">\${success}</li>\`;
                    });
                    html += '</ul>';
                }
                
                if (results.report.issues.length > 0) {
                    html += '<p><strong>发现的问题:</strong></p><ul>';
                    results.report.issues.forEach(issue => {
                        html += \`<li style="color: red;">\${issue}</li>\`;
                    });
                    html += '</ul>';
                }
                
                if (results.report.recommendations.length > 0) {
                    html += '<p><strong>建议:</strong></p><ul>';
                    results.report.recommendations.forEach(rec => {
                        html += \`<li style="color: orange;">\${rec}</li>\`;
                    });
                    html += '</ul>';
                }
                
                html += '</div>';
            }
            
            // 显示详细步骤
            html += '<div class="test-section"><h3>详细测试步骤</h3>';
            results.steps.forEach(step => {
                const stepClass = step.status === 'completed' && step.success ? 'success' : 
                                step.status === 'failed' ? 'error' : '';
                
                html += \`<div class="step \${stepClass}">
                    <h4>步骤 \${step.step}: \${step.name}</h4>
                    <p><strong>状态:</strong> \${step.status}</p>\`;
                
                if (step.error) {
                    html += \`<p><strong>错误:</strong> \${step.error}</p>\`;
                }
                
                if (step.chaptersCount) {
                    html += \`<p><strong>提取章节数:</strong> \${step.chaptersCount}</p>\`;
                    html += \`<p><strong>总文本长度:</strong> \${step.totalTextLength} 字符</p>\`;
                    
                    if (step.chapters && step.chapters.length > 0) {
                        html += '<p><strong>章节列表:</strong></p><div class="chapter-list">';
                        step.chapters.forEach(chapter => {
                            html += \`<div class="chapter-item">
                                <strong>\${chapter.title}</strong> (\${chapter.filename})<br>
                                文本长度: \${chapter.textLength} 字符, HTML长度: \${chapter.htmlLength} 字符<br>
                                <small>预览: \${chapter.textPreview}</small>
                            </div>\`;
                        });
                        html += '</div>';
                    }
                }
                
                if (step.bookTitle) {
                    html += \`<p><strong>书籍标题:</strong> \${step.bookTitle}</p>\`;
                    html += \`<p><strong>章节文件:</strong> \${step.chapterFilenames.join(', ')}</p>\`;
                }
                
                html += '</div>';
            });
            html += '</div>';
            
            resultsDiv.innerHTML = html;
        }
    </script>
</body>
</html>`;

    return new Response(html, {
        headers: {
            'Content-Type': 'text/html; charset=utf-8'
        }
    });
} 