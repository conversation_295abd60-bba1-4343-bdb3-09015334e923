"""
EPUB文件解析服务
提取EPUB文件的章节结构和文本内容
基于原始参考代码完整迁移，保持所有算法逻辑不变
"""

import ebooklib
from ebooklib import epub
from bs4 import BeautifulSoup
import re
import os
import tempfile
from typing import List, Dict, Optional, Tuple

from dataclasses import dataclass
from app.core.logging import get_logger

logger = get_logger(__name__)


@dataclass
class Chapter:
    """章节数据模型"""

    id: str
    title: str
    content: str
    order: int
    level: int = 1  # 章节层级，1为主章节，2为子章节
    parent_id: Optional[str] = None
    word_count: int = 0


@dataclass
class EpubMetadata:
    """EPUB元数据模型"""

    title: str
    author: str
    language: str
    identifier: str
    publisher: Optional[str] = None
    description: Optional[str] = None
    total_chapters: int = 0
    total_words: int = 0


class EpubParser:
    """EPUB文件解析器"""

    def __init__(self):
        self.book = None
        self.chapters: List[Chapter] = []
        self.metadata: Optional[EpubMetadata] = None

    async def parse_from_file(
        self, file_path: str
    ) -> Tuple[EpubMetadata, List[Chapter]]:
        """从文件路径解析EPUB"""
        try:
            logger.info(f"开始解析EPUB文件: {file_path}")

            # 读取EPUB文件
            self.book = epub.read_epub(file_path)

            # 解析元数据
            self.metadata = self._extract_metadata()

            # 解析章节内容
            self.chapters = self._extract_chapters()

            # 计算统计信息
            self._calculate_statistics()

            logger.info(
                f"EPUB解析完成: {len(self.chapters)}个章节, 共{self.metadata.total_words}字"
            )

            return self.metadata, self.chapters

        except Exception as e:
            logger.error(f"EPUB解析失败: {str(e)}")
            raise Exception(f"EPUB文件解析失败: {str(e)}")

    async def parse_from_url(self, file_url: str) -> Tuple[EpubMetadata, List[Chapter]]:
        """从URL下载并解析EPUB"""
        import httpx

        try:
            logger.info(f"从URL下载EPUB文件: {file_url}")

            # 下载文件到临时目录
            async with httpx.AsyncClient() as client:
                response = await client.get(file_url)
                response.raise_for_status()

                # 创建临时文件
                with tempfile.NamedTemporaryFile(
                    suffix=".epub", delete=False
                ) as temp_file:
                    temp_file.write(response.content)
                    temp_path = temp_file.name

            try:
                # 解析临时文件
                return await self.parse_from_file(temp_path)
            finally:
                # 清理临时文件
                if os.path.exists(temp_path):
                    os.unlink(temp_path)

        except Exception as e:
            logger.error(f"从URL解析EPUB失败: {str(e)}")
            raise Exception(f"从URL下载EPUB文件失败: {str(e)}")

    def _extract_metadata(self) -> EpubMetadata:
        """提取EPUB元数据"""
        try:
            title = (
                self.book.get_metadata("DC", "title")[0][0]
                if self.book.get_metadata("DC", "title")
                else "未知标题"
            )
            author = (
                self.book.get_metadata("DC", "creator")[0][0]
                if self.book.get_metadata("DC", "creator")
                else "未知作者"
            )
            language = (
                self.book.get_metadata("DC", "language")[0][0]
                if self.book.get_metadata("DC", "language")
                else "zh-CN"
            )
            identifier = (
                self.book.get_metadata("DC", "identifier")[0][0]
                if self.book.get_metadata("DC", "identifier")
                else ""
            )
            publisher = (
                self.book.get_metadata("DC", "publisher")[0][0]
                if self.book.get_metadata("DC", "publisher")
                else None
            )
            description = (
                self.book.get_metadata("DC", "description")[0][0]
                if self.book.get_metadata("DC", "description")
                else None
            )

            return EpubMetadata(
                title=title,
                author=author,
                language=language,
                identifier=identifier,
                publisher=publisher,
                description=description,
            )
        except Exception as e:
            logger.warning(f"提取元数据时出现警告: {str(e)}")
            return EpubMetadata(
                title="未知标题", author="未知作者", language="zh-CN", identifier=""
            )

    def _extract_chapters(self) -> List[Chapter]:
        """提取章节内容"""
        chapters = []

        try:
            # 获取目录结构
            toc = self.book.toc
            spine = self.book.spine

            # 如果有目录结构，按目录解析
            if toc:
                chapters = self._parse_from_toc(toc)
            else:
                # 否则按spine顺序解析
                chapters = self._parse_from_spine(spine)

            # 为章节分配顺序号
            for i, chapter in enumerate(chapters):
                chapter.order = i + 1

            return chapters

        except Exception as e:
            logger.error(f"提取章节内容失败: {str(e)}")
            return []

    def _parse_from_toc(self, toc) -> List[Chapter]:
        """从目录结构解析章节"""
        chapters = []

        def process_toc_item(item, level=1, parent_id=None):
            if isinstance(item, tuple):
                # 处理章节项
                section, children = item
                if hasattr(section, "href") and hasattr(section, "title"):
                    chapter = self._create_chapter_from_item(section, level, parent_id)
                    if chapter:
                        chapters.append(chapter)
                        # 递归处理子章节
                        for child in children:
                            process_toc_item(child, level + 1, chapter.id)
            elif hasattr(item, "href") and hasattr(item, "title"):
                # 处理单个章节
                chapter = self._create_chapter_from_item(item, level, parent_id)
                if chapter:
                    chapters.append(chapter)

        # 处理目录中的每个项目
        for item in toc:
            process_toc_item(item)

        return chapters

    def _parse_from_spine(self, spine) -> List[Chapter]:
        """从spine顺序解析章节"""
        chapters = []

        for item_id, _ in spine:
            item = self.book.get_item_with_id(item_id)
            if item and item.get_type() == ebooklib.ITEM_DOCUMENT:
                chapter = self._create_chapter_from_document(item)
                if chapter:
                    chapters.append(chapter)

        return chapters

    def _create_chapter_from_item(
        self, item, level=1, parent_id=None
    ) -> Optional[Chapter]:
        """从目录项创建章节对象"""
        try:
            # 获取文档内容
            href = item.href.split("#")[0]  # 移除锚点
            doc_item = None

            # 查找对应的文档
            for doc in self.book.get_items_of_type(ebooklib.ITEM_DOCUMENT):
                if doc.get_name().endswith(href) or doc.get_name() == href:
                    doc_item = doc
                    break

            if not doc_item:
                return None

            # 提取文本内容
            content = self._extract_text_from_html(doc_item.get_content())

            if not content.strip():
                return None

            chapter = Chapter(
                id=f"chapter_{len(self.chapters) + 1}",
                title=item.title or f"第{len(self.chapters) + 1}章",
                content=content,
                order=0,  # 稍后分配
                level=level,
                parent_id=parent_id,
                word_count=len(content),
            )

            return chapter

        except Exception as e:
            logger.warning(f"创建章节失败: {str(e)}")
            return None

    def _create_chapter_from_document(self, doc_item) -> Optional[Chapter]:
        """从文档项创建章节对象"""
        try:
            content = self._extract_text_from_html(doc_item.get_content())

            if not content.strip():
                return None

            # 尝试从内容中提取标题
            title = (
                self._extract_title_from_content(content)
                or f"第{len(self.chapters) + 1}章"
            )

            chapter = Chapter(
                id=f"chapter_{len(self.chapters) + 1}",
                title=title,
                content=content,
                order=0,  # 稍后分配
                level=1,
                word_count=len(content),
            )

            return chapter

        except Exception as e:
            logger.warning(f"从文档创建章节失败: {str(e)}")
            return None

    def _extract_text_from_html(self, html_content: bytes) -> str:
        """从HTML内容中提取纯文本"""
        try:
            soup = BeautifulSoup(html_content, "html.parser")

            # 移除脚本和样式标签
            for script in soup(["script", "style"]):
                script.decompose()

            # 获取文本内容
            text = soup.get_text()

            # 清理文本
            text = re.sub(r"\n\s*\n", "\n\n", text)  # 合并多个空行
            text = re.sub(r"[ \t]+", " ", text)  # 合并多个空格
            text = text.strip()

            return text

        except Exception as e:
            logger.warning(f"提取HTML文本失败: {str(e)}")
            return ""

    def _extract_title_from_content(self, content: str) -> Optional[str]:
        """从内容中提取标题"""
        lines = content.split("\n")
        for line in lines[:5]:  # 只检查前5行
            line = line.strip()
            if line and len(line) < 100:  # 标题通常较短
                # 检查是否包含章节关键词
                if re.search(r"第.*?章|Chapter|CHAPTER|第.*?节", line):
                    return line
                # 如果是第一行且不为空，可能是标题
                if line == lines[0].strip():
                    return line
        return None

    def _calculate_statistics(self):
        """计算统计信息"""
        if self.metadata:
            self.metadata.total_chapters = len(self.chapters)
            self.metadata.total_words = sum(
                chapter.word_count for chapter in self.chapters
            )

    def get_chapter_hierarchy(self) -> Dict:
        """获取章节层级结构"""
        hierarchy = {
            "title": self.metadata.title if self.metadata else "未知标题",
            "chapters": [],
        }

        # 构建层级结构
        chapter_map = {}
        root_chapters = []

        for chapter in self.chapters:
            chapter_map[chapter.id] = {
                "id": chapter.id,
                "title": chapter.title,
                "order": chapter.order,
                "level": chapter.level,
                "word_count": chapter.word_count,
                "children": [],
            }

        # 组织父子关系
        for chapter in self.chapters:
            chapter_data = chapter_map[chapter.id]
            if chapter.parent_id and chapter.parent_id in chapter_map:
                chapter_map[chapter.parent_id]["children"].append(chapter_data)
            else:
                root_chapters.append(chapter_data)

        hierarchy["chapters"] = root_chapters
        return hierarchy
