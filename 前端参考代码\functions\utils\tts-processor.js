import { extractText, getDocumentProxy } from 'unpdf';
import { EpubParser } from './epub-parser.js';

// TTS处理模块 - 基于Zwei API
export class TTSProcessor {
    constructor() {
        this.apiUrl = "https://otts.api.zwei.de.eu.org/v1/audio/speech";
        this.apiKey = "sk-Zwei";
        this.defaultConfig = {
            model: "zh-CN-XiaochenMultilingualNeural",
            voice_settings: {
                speed: 0,
                pitch: 0,
                output_format: "audio-24khz-48kbitrate-mono-mp3"
            }
        };
        this.epubParser = new EpubParser();
    }

    /**
     * 将文本转换为语音
     * @param {string} text - 要转换的文本内容
     * @param {Object} options - 可选配置参数
     * @param {Object} logger - 日志记录器（可选）
     * @param {number} retryCount - 重试次数（内部使用）
     * @returns {Promise<ArrayBuffer>} - 音频数据
     */
    async textToSpeech(text, options = {}, logger = null, retryCount = 0) {
        const startTime = Date.now();
        const maxRetries = 10; // 增加重试次数到10次
        const timeoutMs = 300000; // 增加超时时间到5分钟
        
        if (!text || text.trim().length === 0) {
            throw new Error('文本内容不能为空');
        }

        // 清理和准备文本
        const cleanedText = this.cleanText(text);
        if (cleanedText.length > 1000) {
            console.warn(`文本长度 ${cleanedText.length} 超过建议长度，可能影响转换质量`);
            if (logger) {
                await logger.warn('文本长度超过建议值', {
                    textLength: cleanedText.length,
                    recommendedLength: 1000,
                    textPreview: cleanedText.substring(0, 200) + '...'
                });
            }
        }

        // 使用Zwei API的参数格式
        const requestData = {
            model: options.model || this.defaultConfig.model,
            input: cleanedText,
            voice_settings: {
                speed: options.speed || this.defaultConfig.voice_settings.speed,
                pitch: options.pitch || this.defaultConfig.voice_settings.pitch,
                output_format: options.output_format || this.defaultConfig.voice_settings.output_format
            }
        };

        if (logger && retryCount === 0) {
            // 只在首次调用时记录，减少日志数量
            console.log('准备TTS API调用', {
                textLength: cleanedText.length,
                model: requestData.model
            });
        }

        try {
            // 创建超时控制器
            const controller = new AbortController();
            const timeoutId = setTimeout(() => {
                controller.abort();
            }, timeoutMs);

            // 使用正确的Zwei API接口，添加超时控制
            const response = await fetch(this.apiUrl, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData),
                signal: controller.signal
            });

            clearTimeout(timeoutId);
            const responseTime = Date.now() - startTime;

            // 只在有错误或重试时记录详细信息
            if (!response.ok) {
                const errorText = await response.text();
                
                // 尝试解析JSON错误响应
                let parsedError = null;
                try {
                    parsedError = JSON.parse(errorText);
                } catch (e) {
                    // 如果不是JSON，使用原始错误文本
                }
                
                // 记录API调用失败的关键信息
                if (logger) {
                    await logger.error('TTS API调用失败', {
                        status: response.status,
                        error: parsedError?.message || errorText,
                        retryAttempt: retryCount + 1,
                        textLength: cleanedText.length
                    });
                }

                // 控制台输出详细错误信息
                console.error(`TTS API调用失败 (状态${response.status}):`, {
                    status: response.status,
                    statusText: response.statusText,
                    errorBody: errorText,
                    parsedError: parsedError,
                    retryAttempt: retryCount + 1,
                    responseTime: responseTime
                });

                // 检查是否需要重试
                const shouldRetry = (response.status === 429 || response.status >= 500) && retryCount < maxRetries;
                
                if (shouldRetry) {
                    const retryDelay = Math.min(2000 * Math.pow(1.5, retryCount), 30000); // 指数退避，最大30秒
                    
                    if (logger) {
                        await logger.warn(`TTS API重试中`, {
                            retryAttempt: retryCount + 2,
                            retryDelay,
                            status: response.status
                        });
                    }
                    
                    console.warn(`TTS API调用失败 (状态${response.status})，${retryDelay}ms后重试 (${retryCount + 1}/${maxRetries})`);
                    
                    await new Promise(resolve => setTimeout(resolve, retryDelay));
                    return this.textToSpeech(text, options, logger, retryCount + 1);
                }

                // 根据不同错误状态提供简化的用户友好错误信息
                let userErrorMessage;
                if (response.status === 401) {
                    userErrorMessage = '语音转换服务认证失败，请联系管理员';
                } else if (response.status === 429) {
                    userErrorMessage = `服务繁忙且重试${maxRetries}次后仍失败，请稍后重试`;
                } else if (response.status === 400) {
                    userErrorMessage = `文本内容格式错误，请检查文本内容。API错误: ${parsedError?.message || errorText}`;
                } else if (response.status >= 500) {
                    userErrorMessage = `语音转换服务暂时不可用且重试${maxRetries}次后仍失败，请稍后重试。API错误: ${parsedError?.message || errorText}`;
                } else {
                    userErrorMessage = `语音转换失败 (${response.status})，请稍后重试。API错误: ${parsedError?.message || errorText}`;
                }

                throw new Error(userErrorMessage);
            }

            const audioBuffer = await response.arrayBuffer();
            const totalTime = Date.now() - startTime;

            // 使用简化的成功日志记录
            if (logger) {
                await logger.ttsResult(true, {
                    textLength: cleanedText.length,
                    audioSize: audioBuffer.byteLength,
                    retryAttempt: retryCount + 1
                });
            }

            console.log(`TTS转换成功: ${cleanedText.length}字符 -> ${audioBuffer.byteLength}字节 (${totalTime}ms)${retryCount > 0 ? ` [重试${retryCount}次后成功]` : ''}`);
            return audioBuffer;

        } catch (error) {
            const totalTime = Date.now() - startTime;
            
            // 处理超时错误
            if (error.name === 'AbortError') {
                const shouldRetry = retryCount < maxRetries;
                
                if (logger) {
                    await logger.error('TTS API调用超时', {
                        timeout: timeoutMs,
                        retryAttempt: retryCount + 1,
                        textLength: cleanedText.length
                    });
                }
                
                if (shouldRetry) {
                    const retryDelay = Math.min(5000 * Math.pow(1.5, retryCount), 60000); // 超时重试延迟更长，最大1分钟
                    
                    console.warn(`TTS API调用超时 (${timeoutMs/60000}分钟)，${retryDelay}ms后重试 (${retryCount + 1}/${maxRetries})`);
                    
                    await new Promise(resolve => setTimeout(resolve, retryDelay));
                    return this.textToSpeech(text, options, logger, retryCount + 1);
                }
                
                throw new Error(`语音转换超时 (${timeoutMs/60000}分钟) 且重试${maxRetries}次后仍失败，请检查网络连接或文本长度`);
            }
            
            // 处理网络错误
            if (error.name === 'TypeError' && (error.message.includes('fetch') || error.message.includes('network'))) {
                const shouldRetry = retryCount < maxRetries;
                
                if (logger) {
                    await logger.error('TTS网络连接错误', {
                        error: error.message,
                        errorStack: error.stack,
                        textLength: cleanedText.length,
                        totalTime: totalTime,
                        retryAttempt: retryCount + 1,
                        willRetry: shouldRetry,
                        remainingRetries: maxRetries - retryCount,
                        textPreview: cleanedText.substring(0, 100) + '...'
                    });
                }
                
                if (shouldRetry) {
                    const retryDelay = Math.min(3000 * Math.pow(1.5, retryCount), 45000); // 网络错误重试延迟，最大45秒
                    
                    console.warn(`TTS网络连接失败，${retryDelay}ms后重试 (${retryCount + 1}/${maxRetries}): ${error.message}`);
                    
                    if (logger) {
                        await logger.warn('准备网络重试', {
                            retryDelay,
                            retryAttempt: retryCount + 2,
                            maxRetries,
                            networkError: error.message,
                            textLength: cleanedText.length
                        });
                    }
                    
                    await new Promise(resolve => setTimeout(resolve, retryDelay));
                    return this.textToSpeech(text, options, logger, retryCount + 1);
                }
                
                throw new Error(`网络连接失败且重试${maxRetries}次后仍失败: ${error.message}`);
            }
            
            if (logger) {
                await logger.error('TTS转换异常', {
                    error: error.message,
                    errorStack: error.stack,
                    textLength: cleanedText.length,
                    totalTime: totalTime,
                    textPreview: cleanedText.substring(0, 200) + '...',
                    retryAttempt: retryCount + 1,
                    maxRetries: maxRetries,
                    timeoutSettings: timeoutMs / 60000 + '分钟'
                });
            }

            console.error('TTS转换失败:', error);
            throw error;
        }
    }

    /**
     * 将长文本分割为合适的段落
     * @param {string} text - 原始文本
     * @param {number} maxLength - 每段最大长度（减少到1000字符，提高处理速度和成功率）
     * @returns {Array<string>} - 分割后的文本段落
     */
    splitText(text, maxLength = 1000) {
        console.log(`开始分割文本，原始长度: ${text.length}`);
        
        // 先进行基本的文本清理
        const cleanedText = text.trim()
            .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // 移除控制字符
            .replace(/\s+/g, ' '); // 规范化空白字符
        
        if (cleanedText.length === 0) {
            console.warn('文本清理后为空');
            return [];
        }

        const sentences = cleanedText.split(/[。！？\n]+/).filter(s => s.trim().length > 0);
        const chunks = [];
        let currentChunk = '';

        for (const sentence of sentences) {
            const trimmedSentence = sentence.trim();
            if (!trimmedSentence) continue;

            if (currentChunk.length + trimmedSentence.length + 1 <= maxLength) {
                currentChunk += (currentChunk ? '。' : '') + trimmedSentence;
            } else {
                if (currentChunk) {
                    chunks.push(currentChunk + '。');
                }
                
                // 如果单个句子过长，进一步分割
                if (trimmedSentence.length > maxLength) {
                    const words = trimmedSentence.split(/[\s，、]/);
                    let tempChunk = '';
                    for (const word of words) {
                        if (tempChunk.length + word.length + 1 <= maxLength) {
                            tempChunk += (tempChunk ? '，' : '') + word;
                        } else {
                            if (tempChunk) {
                                chunks.push(tempChunk + '。');
                            }
                            tempChunk = word;
                        }
                    }
                    if (tempChunk) {
                        currentChunk = tempChunk;
                    } else {
                        currentChunk = '';
                    }
                } else {
                    currentChunk = trimmedSentence;
                }
            }
        }

        if (currentChunk) {
            chunks.push(currentChunk + '。');
        }

        // 如果没有分割出任何内容，返回原始文本的安全版本
        if (chunks.length === 0) {
            const safeText = cleanedText.substring(0, maxLength);
            chunks.push(safeText);
        }

        console.log(`文本分割完成，共 ${chunks.length} 段（每段最大${maxLength}字符）`);
        chunks.forEach((chunk, index) => {
            console.log(`段落 ${index + 1}: 长度 ${chunk.length}, 内容: ${chunk.substring(0, 30)}...`);
        });

        return chunks;
    }

    /**
     * 批量处理文本转换（优化版本 - 支持并发控制和错误恢复）
     * @param {string} text - 完整文本
     * @param {function} progressCallback - 进度回调函数
     * @param {Object} logger - 日志记录器（可选）
     * @returns {Promise<ArrayBuffer>} - 合并后的音频数据
     */
    async processLongText(text, progressCallback, logger = null) {
        const startTime = Date.now();
        const chunks = this.splitText(text);
        const concurrencyLimit = 2; // 限制并发数，避免API过载
        const batchSize = 5; // 批处理大小，定期保存中间结果
        
        console.log(`开始处理 ${chunks.length} 个文本段落，并发限制: ${concurrencyLimit}`);
        
        const audioBuffers = new Array(chunks.length); // 预分配数组，保持顺序
        const processingResults = {
            completed: 0,
            failed: 0,
            skipped: 0,
            totalChunks: chunks.length
        };
        
        if (logger) {
            await logger.info('开始批量文本转语音处理', {
                totalChunks: chunks.length,
                totalTextLength: text.length,
                averageChunkLength: Math.round(text.length / chunks.length),
                estimatedProcessingTime: Math.round(chunks.length * 10) + '秒', // 更新预估时间，考虑到更长的超时设置
                concurrencyLimit,
                batchSize,
                strategy: '并发控制 + 错误恢复 + 中间保存',
                retrySettings: {
                    textToSpeechRetries: 10,
                    chunkRetries: 5,
                    timeoutPerChunk: '5分钟',
                    totalMaxTimePerChunk: '约25-50分钟（考虑重试）'
                }
            });
        }

        // 分批处理，每批内部使用有限并发
        for (let batchStart = 0; batchStart < chunks.length; batchStart += batchSize) {
            const batchEnd = Math.min(batchStart + batchSize, chunks.length);
            const batchChunks = chunks.slice(batchStart, batchEnd);
            
            if (logger) {
                await logger.info(`开始处理批次 ${Math.floor(batchStart / batchSize) + 1}/${Math.ceil(chunks.length / batchSize)}`, {
                    batchStart: batchStart + 1,
                    batchEnd,
                    batchSize: batchChunks.length,
                    remainingChunks: chunks.length - batchEnd
                });
            }
            
            // 在批次内使用信号量控制并发
            const semaphore = new Array(concurrencyLimit).fill(null);
            const batchPromises = batchChunks.map(async (chunk, localIndex) => {
                const globalIndex = batchStart + localIndex;
                
                // 等待获取并发槽位
                let slotIndex = -1;
                while (slotIndex === -1) {
                    slotIndex = semaphore.findIndex(slot => slot === null);
                    if (slotIndex === -1) {
                        await new Promise(resolve => setTimeout(resolve, 100)); // 等待100ms后重试
                    }
                }
                
                // 占用槽位
                semaphore[slotIndex] = globalIndex;
                
                try {
                    return await this.processChunkWithRetry(chunk, globalIndex, logger);
                } finally {
                    // 释放槽位
                    semaphore[slotIndex] = null;
                }
            });
            
            // 等待当前批次完成
            const batchResults = await Promise.allSettled(batchPromises);
            
            // 处理批次结果
            for (let i = 0; i < batchResults.length; i++) {
                const globalIndex = batchStart + i;
                const result = batchResults[i];
                
                if (result.status === 'fulfilled') {
                    audioBuffers[globalIndex] = result.value;
                    processingResults.completed++;
                    
                    // 简化日志记录：只在控制台输出，不记录到KV
                    console.log(`段落 ${globalIndex + 1} 处理成功`);
                } else {
                    processingResults.failed++;
                    
                    if (logger) {
                        await logger.error(`段落 ${globalIndex + 1} 处理失败`, {
                            error: result.reason.message
                        });
                    }
                    
                    console.error(`段落 ${globalIndex + 1} 处理失败:`, result.reason.message);
                    
                    // 如果失败率过高，停止处理
                    if (processingResults.failed > Math.max(3, chunks.length * 0.2)) {
                        throw new Error(`处理失败率过高 (${processingResults.failed}/${processingResults.totalChunks})，停止处理: ${result.reason.message}`);
                    }
                }
                
                // 更新总体进度
                const overallProgress = Math.round(
                    (processingResults.completed + processingResults.failed) / processingResults.totalChunks * 80
                ) + 10; // 10-90%
                
                if (progressCallback) {
                    await progressCallback(overallProgress);
                }
            }
            
            // 批次间稍作休息，简化日志
            if (batchEnd < chunks.length) {
                const restTime = Math.min(500, batchChunks.length * 100); // 根据批次大小调整休息时间
                console.log(`批次处理完成，休息 ${restTime}ms`);
                await new Promise(resolve => setTimeout(resolve, restTime));
            }
        }
        
        // 检查处理结果
        const successfulBuffers = audioBuffers.filter(buffer => buffer !== undefined);
        if (successfulBuffers.length === 0) {
            throw new Error('没有任何文本段处理成功，无法生成音频文件');
        }
        
        if (processingResults.failed > 0) {
            const successRate = Math.round((processingResults.completed / processingResults.totalChunks) * 100);
            
            if (logger) {
                await logger.warn('部分文本段处理失败，将使用成功的部分生成音频', {
                    successfulChunks: processingResults.completed,
                    failedChunks: processingResults.failed,
                    successRate: successRate + '%',
                    strategy: '继续处理，忽略失败的段落'
                });
            }
            
            console.warn(`警告: ${processingResults.failed}/${processingResults.totalChunks} 段落处理失败，成功率: ${successRate}%`);
        }

        // 合并音频
        const totalProcessingTime = Date.now() - startTime;
        if (logger) {
            await logger.info('开始合并音频文件', {
                totalChunks: chunks.length,
                successfulChunks: processingResults.completed,
                failedChunks: processingResults.failed,
                totalProcessingTime,
                averageChunkTime: Math.round(totalProcessingTime / processingResults.completed),
                totalAudioSize: successfulBuffers.reduce((sum, buf) => sum + buf.byteLength, 0),
                parallelProcessing: true
            });
        }

        const mergedAudio = this.mergeAudioBuffers(successfulBuffers);
        
        if (logger) {
            await logger.info('音频合并完成', {
                finalAudioSize: mergedAudio.byteLength,
                totalProcessingTime: Date.now() - startTime,
                processingSpeed: Math.round(text.length / ((Date.now() - startTime) / 1000)) + '字符/秒',
                efficiency: `${Math.round((processingResults.completed / processingResults.totalChunks) * 100)}% 成功率`,
                concurrencyBenefit: `使用${concurrencyLimit}并发处理，预计节省${Math.round(((chunks.length * 3000) - totalProcessingTime) / 1000)}秒`
            });
        }

        return mergedAudio;
    }

    /**
     * 处理单个文本段（带重试机制）
     * @param {string} chunk - 文本段
     * @param {number} index - 段落索引
     * @param {Object} logger - 日志记录器
     * @returns {Promise<ArrayBuffer>} - 音频数据
     */
    async processChunkWithRetry(chunk, index, logger) {
        const chunkStartTime = Date.now();
        const maxAttempts = 5; // 增加段落级别的重试次数（textToSpeech内部还有自己的10次重试）
        
        for (let attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                // 只在控制台输出，不记录详细的DEBUG日志到KV
                console.log(`处理第 ${index + 1} 段 (尝试 ${attempt}/${maxAttempts}): ${chunk.substring(0, 50)}... (${chunk.length}字符)`);
                
                const audioBuffer = await this.textToSpeech(chunk, {}, logger);
                
                // 简化成功日志，只在控制台输出
                const chunkDuration = Date.now() - chunkStartTime;
                console.log(`文本段 ${index + 1} 处理完成 (${Math.round(chunkDuration/1000)}秒)`);
                
                return audioBuffer;
                
            } catch (error) {
                const chunkDuration = Date.now() - chunkStartTime;
                
                if (attempt < maxAttempts) {
                    const retryDelay = 2000 * attempt; // 线性延迟，给API更多恢复时间
                    
                    if (logger) {
                        await logger.warn(`文本段 ${index + 1} 重试中`, {
                            attempt,
                            error: error.message
                        });
                    }
                    
                    console.warn(`段落 ${index + 1} 第${attempt}次尝试失败 (${chunkDuration}ms)，${retryDelay}ms后重试: ${error.message}`);
                    await new Promise(resolve => setTimeout(resolve, retryDelay));
                    continue;
                } else {
                    // 最后一次尝试也失败了
                    if (logger) {
                        await logger.error(`文本段 ${index + 1} 所有尝试均失败`, {
                            totalAttempts: maxAttempts,
                            error: error.message
                        });
                    }
                    
                    console.error(`段落 ${index + 1} 处理失败 (尝试${maxAttempts}次，总计${Math.round(chunkDuration/1000)}秒): ${error.message}`);
                    throw new Error(`段落 ${index + 1} 处理失败 (尝试${maxAttempts}次): ${error.message}`);
                }
            }
        }
    }

    /**
     * 合并多个音频缓冲区
     * @param {Array<ArrayBuffer>} buffers - 音频缓冲区数组
     * @returns {ArrayBuffer} - 合并后的音频数据
     */
    mergeAudioBuffers(buffers) {
        if (buffers.length === 0) {
            throw new Error('没有音频数据可合并');
        }
        
        if (buffers.length === 1) {
            return buffers[0];
        }

        // 简化的合并方法：直接拼接二进制数据
        // 注意：这种方法对MP3格式可能不完美，但对于演示目的可以使用
        let totalLength = 0;
        for (const buffer of buffers) {
            totalLength += buffer.byteLength;
        }

        const merged = new Uint8Array(totalLength);
        let offset = 0;

        for (const buffer of buffers) {
            merged.set(new Uint8Array(buffer), offset);
            offset += buffer.byteLength;
        }

        return merged.buffer;
    }

    /**
     * 从文件内容提取纯文本 (使用unpdf库 - 专业级实现)
     * @param {string|ArrayBuffer} content - 文件内容
     * @param {string} filename - 文件名
     * @returns {Promise<string>} - 提取的纯文本
     */
    async extractTextFromContent(content, filename) {
        const ext = filename.toLowerCase().split('.').pop();
        
        console.log(`开始处理文档: ${filename}, 格式: ${ext}`);
        
        switch (ext) {
            case 'txt':
                return this.extractTextFromTxt(content);
            case 'epub':
                return this.extractTextFromEpub(content);
            case 'pdf':
                return await this.extractTextFromPdfWithUnpdf(content);
            case 'doc':
                return this.extractTextFromDoc(content);
            case 'docx':
                return this.extractTextFromDocx(content);
            default:
                throw new Error(`不支持的文件格式: ${ext}。支持的格式：TXT、EPUB、PDF、DOC、DOCX`);
        }
    }

    /**
     * 使用unpdf库提取PDF文本 (专业实现)
     * @param {ArrayBuffer} content - PDF文件内容
     * @returns {Promise<string>} - 提取的纯文本
     */
    async extractTextFromPdfWithUnpdf(content) {
        try {
            console.log('使用unpdf库处理PDF文档...');
            
            // 确保content是Uint8Array格式
            let uint8Content;
            if (content instanceof ArrayBuffer) {
                uint8Content = new Uint8Array(content);
            } else if (typeof content === 'string') {
                // 如果是字符串，先转换为ArrayBuffer
                const encoder = new TextEncoder();
                uint8Content = encoder.encode(content);
            } else {
                uint8Content = content;
            }

            // 使用unpdf库加载PDF文档
            const pdf = await getDocumentProxy(uint8Content);
            
            // 提取所有页面的文本
            const { totalPages, text } = await extractText(pdf, { mergePages: true });
            
            if (!text || text.trim().length < 10) {
                throw new Error('PDF文档中没有找到可提取的文本内容');
            }
            
            console.log(`PDF处理成功: 共${totalPages}页，提取文本长度${text.length}字符`);
            return this.cleanText(text);
            
        } catch (error) {
            console.error('unpdf PDF处理失败:', error);
            // 如果unpdf失败，回退到基础实现
            console.log('回退到基础PDF处理...');
            return this.extractTextFromPdfBasic(content);
        }
    }

    /**
     * 基础PDF文本提取（备用方案）
     */
    extractTextFromPdfBasic(content) {
        try {
            let text;
            if (typeof content === 'string') {
                text = content;
            } else {
                const decoder = new TextDecoder('utf-8', { fatal: false });
                text = decoder.decode(content);
            }
            
            // 简化的PDF文本提取 - 查找文本对象
            const textMatches = text.match(/\(([^)]+)\)\s*Tj/g) || [];
            const extractedTexts = textMatches.map(match => {
                return match.replace(/\(([^)]+)\)\s*Tj/, '$1');
            });
            
            // 如果没有找到文本对象，尝试其他模式
            if (extractedTexts.length === 0) {
                // 查找BT...ET块（文本块）
                const textBlocks = text.match(/BT\s+(.*?)\s+ET/gs) || [];
                textBlocks.forEach(block => {
                    const blockTexts = block.match(/\(([^)]+)\)/g) || [];
                    blockTexts.forEach(blockText => {
                        extractedTexts.push(blockText.replace(/[()]/g, ''));
                    });
                });
            }
            
            let extractedText = extractedTexts.join(' ').trim();
            
            // 如果仍然没有提取到内容，返回提示信息
            if (!extractedText || extractedText.length < 10) {
                extractedText = `PDF文档已上传，但使用基础解析器提取的文本内容有限。

建议您：
1. 将PDF另存为TXT格式后重新上传（推荐）
2. 确保PDF为文本格式而非扫描版
3. 尝试使用其他PDF转文本工具预处理

注意：基础解析器对复杂PDF格式支持有限，专业unpdf库处理失败时会显示此消息。`;
            }
            
            console.log(`基础PDF处理完成，提取文本长度: ${extractedText.length} 字符`);
            return this.cleanText(extractedText);
        } catch (error) {
            console.error('基础PDF文件处理失败:', error);
            return this.cleanText(`PDF文档处理遇到技术限制。建议转换为TXT格式后重新上传。错误：${error.message}`);
        }
    }

    /**
     * 提取TXT文件文本
     */
    extractTextFromTxt(content) {
        try {
            let text;
            if (typeof content === 'string') {
                text = content;
            } else {
                // 尝试多种编码
                try {
                    const decoder = new TextDecoder('utf-8');
                    text = decoder.decode(content);
                } catch {
                    // 如果UTF-8失败，尝试GBK等其他编码
                    const decoder = new TextDecoder('gbk');
                    text = decoder.decode(content);
                }
            }
            
            console.log(`TXT文件处理成功，提取文本长度: ${text.length} 字符`);
            return this.cleanText(text);
        } catch (error) {
            console.error('TXT文件处理失败:', error);
            throw new Error(`TXT文件处理失败: ${error.message}`);
        }
    }

    /**
     * 清理文本内容
     * @param {string} text - 原始文本
     * @returns {string} - 清理后的文本
     */
    cleanText(text) {
        return text
            .replace(/\r\n/g, '\n')
            .replace(/\r/g, '\n')
            .replace(/\n{3,}/g, '\n\n')
            .replace(/[ \t]+/g, ' ')
            .trim();
    }

    /**
     * 提取EPUB文件文本 (使用新的正确解析方法)
     */
    async extractTextFromEpub(content) {
        try {
            console.log('开始处理EPUB文件...');
            
            // 检查是否为ArrayBuffer格式
            if (!(content instanceof ArrayBuffer)) {
                throw new Error('EPUB文件内容格式错误，应为ArrayBuffer');
            }
            
            // 使用新的EPUB解析器
            const chapters = await this.epubParser.parseEpub(content);
            
            if (!chapters || chapters.length === 0) {
                throw new Error('未能从EPUB文件中提取到任何章节内容');
            }
            
            // 合并所有章节的文本内容
            let fullText = '';
            chapters.forEach((chapter, index) => {
                if (index > 0) {
                    fullText += '\n\n=== ' + chapter.title + ' ===\n\n';
                }
                fullText += chapter.textContent;
            });
            
            const cleanedText = this.cleanText(fullText);
            console.log(`EPUB文件处理成功，共 ${chapters.length} 个章节，最终文本长度: ${cleanedText.length} 字符`);
            
            if (cleanedText.length < 50) {
                throw new Error(`提取的文本内容过少（${cleanedText.length}字符），可能文件主要包含图片或特殊格式内容。`);
            }
            
            return cleanedText;
            
        } catch (error) {
            console.error('EPUB文件处理失败:', error);
            
            // 提供更详细的错误信息和建议
            let errorMessage = `EPUB文件处理失败: ${error.message}`;
            
            if (error.message.includes('ArrayBuffer')) {
                errorMessage += '\n\n建议：请确保上传的是有效的EPUB文件。';
            } else if (error.message.includes('ZIP格式') || error.message.includes('无效的ZIP文件')) {
                errorMessage += '\n\n建议：EPUB文件可能已损坏，请重新下载或使用其他EPUB文件。';
            } else if (error.message.includes('文本内容过少')) {
                errorMessage += '\n\n建议：\n1. 该EPUB可能主要包含图片内容\n2. 尝试使用文本版本的电子书\n3. 将内容复制到TXT文件后上传';
            } else if (error.message.includes('OPF文件')) {
                errorMessage += '\n\n建议：\n1. 该EPUB文件结构异常，可能不是标准格式\n2. 尝试将EPUB转换为TXT格式后上传\n3. 使用其他格式的电子书文件';
            } else {
                errorMessage += '\n\n建议：\n1. 尝试将EPUB转换为TXT格式后上传\n2. 使用其他格式的电子书文件\n3. 检查文件是否完整下载';
            }
            
            throw new Error(errorMessage);
        }
    }

    /**
     * 提取EPUB文件的章节结构（用于分章节处理）
     */
    async extractEpubChapters(content) {
        try {
            console.log('开始提取EPUB章节结构...');
            
            if (!(content instanceof ArrayBuffer)) {
                throw new Error('EPUB文件内容格式错误，应为ArrayBuffer');
            }
            
            const chapters = await this.epubParser.parseEpub(content);
            
            if (!chapters || chapters.length === 0) {
                throw new Error('未能从EPUB文件中提取到任何章节内容');
            }
            
            // 转换为TTS处理器需要的格式
            const processedChapters = chapters.map(chapter => ({
                title: chapter.title,
                content: chapter.textContent,
                estimatedDuration: Math.ceil(chapter.textContent.length / this.estimatedWordsPerSecond),
                chapterNum: chapter.chapterNum,
                filename: chapter.filename
            }));
            
            console.log(`EPUB章节提取成功，共 ${processedChapters.length} 个章节`);
            processedChapters.forEach((chapter, index) => {
                console.log(`章节 ${index + 1}: ${chapter.title} (${chapter.content.length}字符, 预计${Math.ceil(chapter.estimatedDuration/60)}分钟)`);
            });
            
            return processedChapters;
            
        } catch (error) {
            console.error('EPUB章节提取失败:', error);
            throw error;
        }
    }

    /**
     * 提取传统DOC文件文本
     */
    extractTextFromDoc(content) {
        try {
            // DOC格式是二进制格式，基础文本提取
            let text;
            if (typeof content === 'string') {
                text = content;
            } else {
                const decoder = new TextDecoder('utf-8', { fatal: false });
                text = decoder.decode(content);
            }
            
            // 尝试提取可读文本
            let extractedText = text.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x9F]/g, ' ');
            extractedText = extractedText.replace(/[^\x20-\x7E\u4e00-\u9fff\u3400-\u4dbf]/g, ' ');
            extractedText = extractedText.replace(/\s+/g, ' ').trim();
            
            if (!extractedText || extractedText.length < 20) {
                extractedText = `传统Word文档(.doc)上传成功，但文本提取有限。

建议您：
1. 将DOC文档另存为DOCX格式后重新上传（推荐）
2. 将文档另存为TXT格式后上传
3. 复制文档内容直接粘贴到TXT文件中上传

DOC格式是较老的二进制格式，在云端环境中处理有限制。
DOCX格式（Word 2007+）有更好的兼容性。`;
            }
            
            console.log(`DOC文件处理完成，提取文本长度: ${extractedText.length} 字符`);
            return this.cleanText(extractedText);
        } catch (error) {
            console.error('DOC文件处理失败:', error);
            return this.cleanText(`DOC文档处理遇到技术限制。建议转换为DOCX或TXT格式后重新上传。错误：${error.message}`);
        }
    }

    /**
     * 提取DOCX文件文本 (基于ZIP结构)
     */
    extractTextFromDocx(content) {
        try {
            // DOCX是基于ZIP的XML格式
            let extractedText = '';
            
            if (typeof content === 'string') {
                // 如果是字符串，尝试查找XML文本
                const xmlMatch = content.match(/<w:t[^>]*>([^<]+)<\/w:t>/g);
                if (xmlMatch) {
                    extractedText = xmlMatch.map(match => 
                        match.replace(/<w:t[^>]*>([^<]+)<\/w:t>/, '$1')
                    ).join(' ');
                }
            } else {
                // 如果是ArrayBuffer，尝试基础文本提取
                const decoder = new TextDecoder('utf-8', { fatal: false });
                const text = decoder.decode(content);
                
                // 查找DOCX中的文本内容
                const xmlMatches = text.match(/<w:t[^>]*>([^<]*)<\/w:t>/g) || [];
                extractedText = xmlMatches.map(match => {
                    return match.replace(/<w:t[^>]*>([^<]*)<\/w:t>/, '$1');
                }).join(' ');
                
                // 如果没找到w:t标签，尝试其他文本模式
                if (!extractedText) {
                    const generalTextMatches = text.match(/>([^<]{10,})</g) || [];
                    extractedText = generalTextMatches.map(match => 
                        match.replace(/^>|<$/g, '')
                    ).filter(text => 
                        text.trim().length > 5 && 
                        /[\u4e00-\u9fff\u3400-\u4dbfa-zA-Z]/.test(text)
                    ).join(' ');
                }
            }
            
            if (!extractedText || extractedText.trim().length < 10) {
                extractedText = `DOCX文档上传成功，检测到Word 2007+格式。

建议您：
1. 在Word中选择"另存为" > "纯文本(.txt)"格式后重新上传（推荐）
2. 复制文档内容粘贴到记事本保存为TXT文件上传
3. 确保文档不是纯图片或表格

DOCX基础文本提取功能有限，TXT格式能确保最佳的文本识别效果。`;
            }
            
            console.log(`DOCX文件处理完成，提取文本长度: ${extractedText.length} 字符`);
            return this.cleanText(extractedText);
        } catch (error) {
            console.error('DOCX文件处理失败:', error);
            return this.cleanText(`DOCX文档处理遇到问题。建议转换为TXT格式后重新上传以获得最佳效果。错误：${error.message}`);
        }
    }
} 