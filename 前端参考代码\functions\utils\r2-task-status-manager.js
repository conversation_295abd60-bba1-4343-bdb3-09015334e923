/**
 * R2任务状态管理器
 * 负责在R2存储中管理任务状态文件，替代KV存储
 */

import { R2StorageManager } from './r2-storage.js';
import { UserIdResolver } from './user-id-resolver.js';

export class R2TaskStatusManager {
    constructor(r2Bucket, env) {
        this.r2Storage = new R2StorageManager(r2Bucket);
        this.userIdResolver = new UserIdResolver(env);
        this.env = env;
    }

    /**
     * 创建新任务状态文件
     */
    async createTaskStatus(taskData) {
        const {
            id,
            email,
            filename,
            type = 'file',
            fileSize,
            filePath
        } = taskData;

        // 解析用户ID
        const userIdResult = await this.userIdResolver.resolveUserId(email, id);
        if (!userIdResult.userId) {
            throw new Error(`无法解析用户ID: ${email}`);
        }

        const statusPath = `users/${userIdResult.userId}/tasks/${id}/status.json`;
        
        const statusData = {
            version: "1.0",
            id,
            email,
            filename,
            type,
            status: "pending",
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            startedAt: null,
            completedAt: null,
            fileSize,
            filePath,
            audioPath: null,
            audioSize: null,
            error: null,
            metadata: {
                wordCount: null,
                duration: null,
                isEnhanced: false,
                totalChapters: 1,
                processingDuration: null
            },
            urls: {
                audioUrl: null,
                playlistUrl: null,
                metadataUrl: null,
                downloadUrl: null
            }
        };

        try {
            await this.r2Storage.r2.put(statusPath, JSON.stringify(statusData, null, 2), {
                httpMetadata: {
                    contentType: 'application/json',
                    cacheControl: 'no-cache'
                },
                customMetadata: {
                    type: 'task-status',
                    taskId: id,
                    email: email,
                    version: '1.0'
                }
            });

            console.log(`✅ R2任务状态已创建: ${statusPath}`);
            return statusData;
        } catch (error) {
            console.error('创建R2任务状态失败:', error);
            throw new Error(`创建任务状态失败: ${error.message}`);
        }
    }

    /**
     * 读取任务状态
     */
    async getTaskStatus(taskId, email) {
        try {
            // 解析用户ID
            const userIdResult = await this.userIdResolver.resolveUserId(email, taskId);
            if (!userIdResult.userId) {
                throw new Error(`无法解析用户ID: ${email}`);
            }

            const statusPath = `users/${userIdResult.userId}/tasks/${taskId}/status.json`;
            const statusObject = await this.r2Storage.r2.get(statusPath);

            if (!statusObject) {
                return null; // 状态文件不存在
            }

            const statusData = await statusObject.json();
            return statusData;
        } catch (error) {
            console.error(`读取任务状态失败 ${taskId}:`, error);
            return null;
        }
    }

    /**
     * 更新任务状态
     */
    async updateTaskStatus(taskId, email, updates, options = {}) {
        const { retryCount = 3, checkConflict = true } = options;

        for (let attempt = 0; attempt < retryCount; attempt++) {
            try {
                // 读取当前状态
                const currentStatus = await this.getTaskStatus(taskId, email);
                if (!currentStatus) {
                    throw new Error('任务状态文件不存在');
                }

                // 检查并发冲突（简单的乐观锁）
                if (checkConflict && updates.expectedUpdatedAt) {
                    if (currentStatus.updatedAt !== updates.expectedUpdatedAt) {
                        throw new Error('状态文件已被其他进程修改，请重试');
                    }
                    delete updates.expectedUpdatedAt;
                }

                // 合并更新
                const newStatus = {
                    ...currentStatus,
                    ...updates,
                    updatedAt: new Date().toISOString()
                };

                // 写入更新后的状态
                const userIdResult = await this.userIdResolver.resolveUserId(email, taskId);
                const statusPath = `users/${userIdResult.userId}/tasks/${taskId}/status.json`;

                await this.r2Storage.r2.put(statusPath, JSON.stringify(newStatus, null, 2), {
                    httpMetadata: {
                        contentType: 'application/json',
                        cacheControl: 'no-cache'
                    },
                    customMetadata: {
                        type: 'task-status',
                        taskId: taskId,
                        email: email,
                        version: '1.0',
                        lastUpdate: newStatus.updatedAt
                    }
                });

                console.log(`✅ R2任务状态已更新: ${taskId} -> ${newStatus.status}`);
                return newStatus;

            } catch (error) {
                console.error(`更新任务状态失败 (尝试 ${attempt + 1}/${retryCount}):`, error);
                
                if (attempt === retryCount - 1) {
                    throw error;
                }

                // 指数退避重试
                const delay = Math.min(1000 * Math.pow(2, attempt), 5000);
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
    }

    /**
     * 标记任务开始处理
     */
    async markAsProcessing(taskId, email) {
        return await this.updateTaskStatus(taskId, email, {
            status: 'pending',
            startedAt: new Date().toISOString()
        });
    }

    /**
     * 标记任务完成
     */
    async markAsCompleted(taskId, email, completionData = {}) {
        const updates = {
            status: 'completed',
            completedAt: new Date().toISOString(),
            ...completionData
        };

        return await this.updateTaskStatus(taskId, email, updates);
    }

    /**
     * 标记任务失败
     */
    async markAsFailed(taskId, email, error, additionalData = {}) {
        const updates = {
            status: 'failed',
            completedAt: new Date().toISOString(),
            error: error.message || error,
            ...additionalData
        };

        return await this.updateTaskStatus(taskId, email, updates);
    }

    /**
     * 获取用户所有任务状态
     */
    async getUserTasks(email, options = {}) {
        const { limit = 100, status = null } = options;

        try {
            // 解析用户ID
            const userIdResult = await this.userIdResolver.resolveUserId(email);
            if (!userIdResult.userId) {
                throw new Error(`无法解析用户ID: ${email}`);
            }

            const tasksPrefix = `users/${userIdResult.userId}/tasks/`;
            const { objects } = await this.r2Storage.r2.list({
                prefix: tasksPrefix,
                delimiter: '/',
                limit: limit * 2 // 预留空间，因为每个任务可能有多个文件
            });

            const tasks = [];
            const taskDirs = new Set();

            // 收集所有任务目录
            for (const obj of objects) {
                const relativePath = obj.key.substring(tasksPrefix.length);
                const taskId = relativePath.split('/')[0];
                if (taskId && !taskDirs.has(taskId)) {
                    taskDirs.add(taskId);
                }
            }

            // 并行读取所有任务状态
            const statusPromises = Array.from(taskDirs).slice(0, limit).map(async (taskId) => {
                try {
                    const status = await this.getTaskStatus(taskId, email);
                    if (status && (!status || status === status.status)) {
                        return status;
                    }
                } catch (error) {
                    console.warn(`读取任务状态失败 ${taskId}:`, error);
                }
                return null;
            });

            const results = await Promise.all(statusPromises);
            const validTasks = results.filter(task => task !== null);

            // 按创建时间排序
            validTasks.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

            console.log(`📋 获取用户任务: ${email}, 找到 ${validTasks.length} 个任务`);
            return validTasks;

        } catch (error) {
            console.error('获取用户任务失败:', error);
            throw new Error(`获取任务列表失败: ${error.message}`);
        }
    }

    /**
     * 批量获取任务状态
     */
    async getBatchTaskStatus(taskIds, email) {
        const statusPromises = taskIds.map(taskId => 
            this.getTaskStatus(taskId, email).catch(error => {
                console.warn(`批量获取任务状态失败 ${taskId}:`, error);
                return null;
            })
        );

        const results = await Promise.all(statusPromises);
        return results.filter(status => status !== null);
    }

    /**
     * 删除任务状态文件
     */
    async deleteTaskStatus(taskId, email) {
        try {
            const userIdResult = await this.userIdResolver.resolveUserId(email, taskId);
            if (!userIdResult.userId) {
                throw new Error(`无法解析用户ID: ${email}`);
            }

            const statusPath = `users/${userIdResult.userId}/tasks/${taskId}/status.json`;
            await this.r2Storage.r2.delete(statusPath);

            console.log(`🗑️ R2任务状态已删除: ${statusPath}`);
            return true;
        } catch (error) {
            console.error('删除任务状态失败:', error);
            return false;
        }
    }

    /**
     * 检查任务是否存在
     */
    async taskExists(taskId, email) {
        const status = await this.getTaskStatus(taskId, email);
        return status !== null;
    }

    /**
     * 获取任务统计信息
     */
    async getTaskStatistics(email) {
        try {
            const tasks = await this.getUserTasks(email, { limit: 1000 });

            const stats = {
                total: tasks.length,
                pending: 0,
                completed: 0,
                failed: 0,
                totalFileSize: 0,
                totalAudioSize: 0
            };

            tasks.forEach(task => {
                stats[task.status.toLowerCase()]++;
                if (task.fileSize) stats.totalFileSize += task.fileSize;
                if (task.audioSize) stats.totalAudioSize += task.audioSize;
            });

            return stats;
        } catch (error) {
            console.error('获取任务统计失败:', error);
            return null;
        }
    }

    /**
     * 从KV数据转换为R2状态格式（兼容性方法）
     */
    static mapKvToR2Status(kvTask) {
        return {
            version: "1.0",
            id: kvTask.id,
            email: kvTask.email,
            filename: kvTask.filename,
            type: kvTask.type || 'file',
            status: kvTask.status,
            createdAt: kvTask.createdAt,
            updatedAt: kvTask.lastProgressUpdate || kvTask.updatedAt || kvTask.createdAt,
            startedAt: kvTask.startedAt || null,
            completedAt: kvTask.completedAt || null,
            fileSize: kvTask.fileSize || null,
            filePath: kvTask.filePath || null,
            audioPath: kvTask.audioPath || null,
            audioSize: kvTask.audioSize || null,
            error: kvTask.error || null,
            metadata: {
                wordCount: kvTask.wordCount || null,
                duration: kvTask.totalDuration || kvTask.duration || null,
                isEnhanced: kvTask.isEnhanced || kvTask.audioType === 'MULTI_CHAPTER' || false,
                totalChapters: kvTask.totalChapters || 1,
                processingDuration: kvTask.processingDuration || null
            },
            urls: {
                audioUrl: kvTask.audioUrl || null,
                playlistUrl: kvTask.playlistUrl || null,
                metadataUrl: kvTask.metadataUrl || null,
                downloadUrl: kvTask.downloadUrl || kvTask.audioUrl || null
            }
        };
    }

    /**
     * 验证状态数据格式
     */
    static validateStatusData(statusData) {
        const required = ['version', 'id', 'email', 'filename', 'status', 'createdAt'];
        const missing = required.filter(field => !statusData[field]);

        if (missing.length > 0) {
            throw new Error(`状态数据缺少必需字段: ${missing.join(', ')}`);
        }

        const validStatuses = ['pending', 'completed', 'failed'];
        if (!validStatuses.includes(statusData.status)) {
            throw new Error(`无效的任务状态: ${statusData.status}`);
        }

        return true;
    }

    /**
     * 格式化任务数据用于前端显示
     */
    static formatTaskForDisplay(task) {
        return {
            ...task,
            createdAtFormatted: new Date(task.createdAt).toLocaleString('zh-CN'),
            completedAtFormatted: task.completedAt ? new Date(task.completedAt).toLocaleString('zh-CN') : null,
            fileSizeFormatted: task.fileSize ? formatFileSize(task.fileSize) : null,
            audioSizeFormatted: task.audioSize ? formatFileSize(task.audioSize) : null,
            durationFormatted: task.metadata.duration ? formatDuration(task.metadata.duration) : null,
            statusText: getStatusText(task.status),
            typeDisplay: task.type === 'file' ? '文件上传' : 'URL抓取'
        };
    }
}

// 辅助函数
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function formatDuration(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else {
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }
}

function getStatusText(status) {
    const statusMap = {
        'pending': '处理中',
        'completed': '已完成',
        'failed': '失败'
    };
    return statusMap[status] || '未知';
}
