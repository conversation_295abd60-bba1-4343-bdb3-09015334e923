"""
本地文件存储服务 - 单体架构版本
替代Cloudflare R2，使用本地文件系统进行文件存储和管理
"""

import os
import re
import json
import shutil
import mimetypes
from typing import Optional, Dict, List, Tuple
from datetime import datetime
from pathlib import Path
from urllib.parse import urlparse

from app.core.config import settings
from app.core.logging import get_logger
from app.core.exceptions import StorageError, FileNotFoundError

logger = get_logger(__name__)


class LocalStorageService:
    """本地文件存储服务 - 兼容原始R2存储服务接口"""

    def __init__(self):
        """初始化本地存储服务"""
        self.base_dir = Path(settings.data_dir)
        self.upload_dir = Path(settings.upload_dir)
        self.audio_dir = Path(settings.audio_dir)
        self.temp_dir = Path(settings.temp_dir)

        # 兼容R2接口的属性
        self.s3_client = None  # 标识为本地存储模式
        self.bucket_name = "local-storage"
        self.public_domain = None

        # 确保目录存在
        self._ensure_directories()

        logger.info("本地存储服务初始化成功")

    def _ensure_directories(self):
        """确保必要的目录存在"""
        directories = [
            self.base_dir,
            self.upload_dir,
            self.audio_dir,
            self.temp_dir,
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            # 设置目录权限
            try:
                os.chmod(directory, 0o755)
            except (OSError, PermissionError):
                pass

    def parse_file_url_info(self, file_url: str) -> Tuple[Optional[str], Optional[str]]:
        """
        从文件URL中解析用户ID和任务ID
        
        支持的URL格式：
        - users/{user_id}/tasks/{task_id}/{filename}
        - users/{user_id}/uploads/{task_id}/{filename} (向后兼容)
        - uploads/{uuid}/{filename} (旧格式，向后兼容)
        
        Args:
            file_url: 文件URL
            
        Returns:
            Tuple[user_id, task_id]: 用户ID和任务ID，如果解析失败返回None
        """
        try:
            # 从URL中提取路径部分
            if file_url.startswith('http'):
                parsed_url = urlparse(file_url)
                path = parsed_url.path.lstrip('/')
            else:
                path = file_url.lstrip('/')

            logger.debug(f"解析文件URL路径: {path}")

            # 匹配新格式：users/{user_id}/tasks/{task_id}/{filename}
            new_format_pattern = r'^users/([^/]+)/tasks/([^/]+)/[^/]+$'
            match = re.match(new_format_pattern, path)

            if match:
                user_id = match.group(1)
                task_id = match.group(2)
                logger.info(f"解析成功 (新格式) - 用户ID: {user_id}, 任务ID: {task_id}")
                return user_id, task_id

            # 匹配旧的新格式：users/{user_id}/uploads/{task_id}/{filename} (向后兼容)
            old_new_format_pattern = r'^users/([^/]+)/uploads/([^/]+)/[^/]+$'
            match = re.match(old_new_format_pattern, path)

            if match:
                user_id = match.group(1)
                task_id = match.group(2)
                logger.info(f"解析成功 (旧新格式) - 用户ID: {user_id}, 任务ID: {task_id}")
                return user_id, task_id

            # 匹配旧格式：uploads/{uuid}/{filename} (向后兼容)
            old_format_pattern = r'^uploads/([^/]+)/[^/]+$'
            match = re.match(old_format_pattern, path)

            if match:
                uuid_or_task_id = match.group(1)
                logger.warning(f"使用旧格式URL，无法获取用户ID，任务ID: {uuid_or_task_id}")
                return None, uuid_or_task_id

            logger.error(f"无法解析文件URL格式: {path}")
            return None, None

        except Exception as e:
            logger.error(f"解析文件URL失败: {str(e)}")
            return None, None

    def generate_audio_storage_path(self, user_id: str, task_id: str, filename: str) -> str:
        """
        生成音频文件存储路径
        
        Args:
            user_id: 用户ID
            task_id: 任务ID
            filename: 文件名
            
        Returns:
            str: 存储路径
        """
        return f"users/{user_id}/tasks/{task_id}/{filename}"

    def generate_playlist_storage_path(self, user_id: str, task_id: str, playlist_filename: str) -> str:
        """
        生成播放列表文件存储路径
        
        Args:
            user_id: 用户ID
            task_id: 任务ID
            playlist_filename: 播放列表文件名
            
        Returns:
            str: 存储路径
        """
        return f"users/{user_id}/tasks/{task_id}/{playlist_filename}"

    def generate_status_storage_path(self, user_id: str, task_id: str) -> str:
        """
        生成状态文件存储路径
        
        Args:
            user_id: 用户ID
            task_id: 任务ID
            
        Returns:
            str: 状态文件路径
        """
        return f"users/{user_id}/tasks/{task_id}/status.json"

    def generate_original_file_storage_path(self, user_id: str, task_id: str, filename: str) -> str:
        """
        生成原始文件存储路径
        
        Args:
            user_id: 用户ID
            task_id: 任务ID
            filename: 原始文件名
            
        Returns:
            str: 原始文件路径
        """
        # 保持原始文件扩展名
        if filename.lower().endswith('.epub'):
            return f"users/{user_id}/tasks/{task_id}/original.epub"
        elif filename.lower().endswith('.txt'):
            return f"users/{user_id}/tasks/{task_id}/original.txt"
        else:
            # 保持原始扩展名
            return f"users/{user_id}/tasks/{task_id}/original{os.path.splitext(filename)[1]}"

    def get_local_file_path(self, storage_path: str) -> Path:
        """
        将存储路径转换为本地文件系统路径
        
        Args:
            storage_path: 存储路径
            
        Returns:
            Path: 本地文件系统路径
        """
        return self.base_dir / storage_path

    async def upload_file(
        self, local_path: str, storage_path: str, content_type: Optional[str] = None
    ) -> Optional[str]:
        """
        上传文件到本地存储
        
        Args:
            local_path: 本地文件路径
            storage_path: 存储路径
            content_type: 内容类型（可选）
            
        Returns:
            Optional[str]: 文件访问URL，失败返回None
        """
        try:
            if not os.path.exists(local_path):
                raise FileNotFoundError(f"源文件不存在: {local_path}")

            # 获取目标路径
            target_path = self.get_local_file_path(storage_path)
            
            # 确保目标目录存在
            target_path.parent.mkdir(parents=True, exist_ok=True)

            # 复制文件
            shutil.copy2(local_path, target_path)

            # 设置文件权限
            try:
                os.chmod(target_path, 0o644)
            except (OSError, PermissionError):
                pass

            # 保存文件元数据
            await self._save_file_metadata(storage_path, local_path, content_type)

            # 返回访问URL
            public_url = f"/files/{storage_path}"
            logger.info(f"文件上传成功: {storage_path} -> {public_url}")
            return public_url

        except Exception as e:
            logger.error(f"文件上传失败: {str(e)}")
            raise StorageError(f"文件上传失败: {str(e)}")

    async def _save_file_metadata(self, storage_path: str, original_path: str, content_type: Optional[str] = None):
        """保存文件元数据"""
        try:
            # 自动检测内容类型
            if not content_type:
                content_type, _ = mimetypes.guess_type(original_path)
                content_type = content_type or "application/octet-stream"

            metadata = {
                "storage_path": storage_path,
                "original_filename": os.path.basename(original_path),
                "content_type": content_type,
                "file_size": os.path.getsize(original_path),
                "uploaded_at": datetime.now().isoformat(),
            }

            # 保存元数据文件
            metadata_path = self.get_local_file_path(storage_path + ".metadata")
            metadata_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)

        except Exception as e:
            logger.warning(f"保存文件元数据失败: {str(e)}")

    async def upload_audio_files(
        self, audio_files: List[Dict], task_id: str, user_id: str
    ) -> Dict[str, str]:
        """
        批量上传音频文件
        
        Args:
            audio_files: 音频文件列表
            task_id: 任务ID
            user_id: 用户ID
            
        Returns:
            Dict[str, str]: 章节ID到URL的映射
        """
        uploaded_files = {}

        for audio_file in audio_files:
            local_path = audio_file.get("local_path")
            filename = audio_file.get("filename")
            chapter_id = audio_file.get("chapter_id", "")

            if not local_path or not os.path.exists(local_path):
                continue

            # 生成存储路径
            storage_path = self.generate_audio_storage_path(user_id, task_id, filename)

            # 上传文件
            public_url = await self.upload_file(local_path, storage_path, "audio/mpeg")

            if public_url:
                uploaded_files[chapter_id] = public_url
                logger.info(f"音频文件上传成功: {chapter_id} -> {storage_path}")

        return uploaded_files

    async def download_file(self, file_url: str, local_path: str) -> bool:
        """
        从存储下载文件到本地

        Args:
            file_url: 文件URL或存储路径
            local_path: 本地保存路径

        Returns:
            bool: 下载是否成功
        """
        try:
            # 解析存储路径
            if file_url.startswith("/files/"):
                storage_path = file_url.replace("/files/", "")
            elif file_url.startswith("http"):
                # 从HTTP URL中提取路径
                parsed_url = urlparse(file_url)
                storage_path = parsed_url.path.replace("/files/", "").lstrip("/")
            else:
                storage_path = file_url

            # 获取源文件路径
            source_path = self.get_local_file_path(storage_path)

            if not source_path.exists():
                raise FileNotFoundError(f"文件不存在: {storage_path}")

            # 确保目标目录存在
            os.makedirs(os.path.dirname(local_path), exist_ok=True)

            # 复制文件
            shutil.copy2(source_path, local_path)

            logger.info(f"文件下载成功: {storage_path} -> {local_path}")
            return True

        except Exception as e:
            logger.error(f"文件下载失败: {str(e)}")
            return False

    async def delete_file(self, storage_path: str) -> bool:
        """
        删除存储中的文件

        Args:
            storage_path: 存储路径

        Returns:
            bool: 删除是否成功
        """
        try:
            file_path = self.get_local_file_path(storage_path)
            metadata_path = self.get_local_file_path(storage_path + ".metadata")

            # 删除文件
            if file_path.exists():
                file_path.unlink()

            # 删除元数据文件
            if metadata_path.exists():
                metadata_path.unlink()

            logger.info(f"文件删除成功: {storage_path}")
            return True

        except Exception as e:
            logger.error(f"文件删除失败: {str(e)}")
            return False

    async def delete_task_files(self, task_id: str, user_id: str) -> bool:
        """
        删除任务相关的所有文件

        Args:
            task_id: 任务ID
            user_id: 用户ID

        Returns:
            bool: 删除是否成功
        """
        try:
            # 构建任务目录路径
            task_dir = self.get_local_file_path(f"users/{user_id}/tasks/{task_id}")

            if task_dir.exists():
                shutil.rmtree(task_dir)
                logger.info(f"任务文件删除成功: {task_dir}")
                return True
            else:
                logger.warning(f"任务目录不存在: {task_dir}")
                return True  # 目录不存在也算删除成功

        except Exception as e:
            logger.error(f"删除任务文件失败: {str(e)}")
            return False

    async def list_files(self, storage_prefix: str) -> List[Dict]:
        """
        列出指定前缀下的所有文件

        Args:
            storage_prefix: 存储路径前缀

        Returns:
            List[Dict]: 文件信息列表
        """
        try:
            prefix_path = self.get_local_file_path(storage_prefix)
            files = []

            if not prefix_path.exists():
                return files

            # 遍历目录
            for file_path in prefix_path.rglob("*"):
                if file_path.is_file() and not file_path.name.endswith(".metadata"):
                    # 计算相对路径
                    relative_path = file_path.relative_to(self.base_dir)
                    storage_path = str(relative_path).replace("\\", "/")

                    # 获取文件信息
                    file_info = {
                        "storage_path": storage_path,
                        "filename": file_path.name,
                        "size": file_path.stat().st_size,
                        "modified_at": datetime.fromtimestamp(file_path.stat().st_mtime).isoformat(),
                        "url": f"/files/{storage_path}"
                    }

                    # 尝试加载元数据
                    metadata_path = file_path.with_suffix(file_path.suffix + ".metadata")
                    if metadata_path.exists():
                        try:
                            with open(metadata_path, 'r', encoding='utf-8') as f:
                                metadata = json.load(f)
                            file_info.update(metadata)
                        except Exception:
                            pass

                    files.append(file_info)

            return files

        except Exception as e:
            logger.error(f"列出文件失败: {str(e)}")
            return []

    async def get_file_info(self, storage_path: str) -> Optional[Dict]:
        """
        获取文件信息

        Args:
            storage_path: 存储路径

        Returns:
            Optional[Dict]: 文件信息，不存在返回None
        """
        try:
            file_path = self.get_local_file_path(storage_path)

            if not file_path.exists():
                return None

            # 基本文件信息
            file_info = {
                "storage_path": storage_path,
                "filename": file_path.name,
                "size": file_path.stat().st_size,
                "modified_at": datetime.fromtimestamp(file_path.stat().st_mtime).isoformat(),
                "url": f"/files/{storage_path}"
            }

            # 尝试加载元数据
            metadata_path = file_path.with_suffix(file_path.suffix + ".metadata")
            if metadata_path.exists():
                try:
                    with open(metadata_path, 'r', encoding='utf-8') as f:
                        metadata = json.load(f)
                    file_info.update(metadata)
                except Exception:
                    pass

            return file_info

        except Exception as e:
            logger.error(f"获取文件信息失败: {str(e)}")
            return None

    async def save_json_data(self, storage_path: str, data: Dict) -> bool:
        """
        保存JSON数据到存储

        Args:
            storage_path: 存储路径
            data: 要保存的数据

        Returns:
            bool: 保存是否成功
        """
        try:
            file_path = self.get_local_file_path(storage_path)
            file_path.parent.mkdir(parents=True, exist_ok=True)

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            logger.info(f"JSON数据保存成功: {storage_path}")
            return True

        except Exception as e:
            logger.error(f"保存JSON数据失败: {str(e)}")
            return False

    async def load_json_data(self, storage_path: str) -> Optional[Dict]:
        """
        从存储加载JSON数据

        Args:
            storage_path: 存储路径

        Returns:
            Optional[Dict]: 加载的数据，失败返回None
        """
        try:
            file_path = self.get_local_file_path(storage_path)

            if not file_path.exists():
                return None

            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            logger.info(f"JSON数据加载成功: {storage_path}")
            return data

        except Exception as e:
            logger.error(f"加载JSON数据失败: {str(e)}")
            return None

    def get_local_file_path_from_url(self, file_url: str) -> Optional[Path]:
        """
        从文件URL获取本地文件路径

        Args:
            file_url: 文件URL，格式如 /files/users/1/tasks/123/file.txt

        Returns:
            Path: 本地文件路径，如果文件不存在则返回None
        """
        try:
            # 移除URL前缀 /files/
            if file_url.startswith('/files/'):
                storage_path = file_url[7:]  # 移除 '/files/' 前缀
            else:
                storage_path = file_url

            # 构建本地路径
            local_path = self.base_dir / storage_path

            if local_path.exists():
                return local_path
            else:
                logger.warning(f"文件不存在: {local_path}")
                return None

        except Exception as e:
            logger.error(f"获取本地文件路径失败: {str(e)}")
            return None

    def get_storage_stats(self) -> Dict:
        """
        获取存储统计信息

        Returns:
            Dict: 存储统计信息
        """
        try:
            total_size = 0
            file_count = 0

            for file_path in self.base_dir.rglob("*"):
                if file_path.is_file() and not file_path.name.endswith(".metadata"):
                    total_size += file_path.stat().st_size
                    file_count += 1

            return {
                "total_files": file_count,
                "total_size": total_size,
                "total_size_mb": round(total_size / (1024 * 1024), 2),
                "base_dir": str(self.base_dir),
                "upload_dir": str(self.upload_dir),
                "audio_dir": str(self.audio_dir),
            }

        except Exception as e:
            logger.error(f"获取存储统计失败: {str(e)}")
            return {}


# 创建全局存储服务实例
storage_service = LocalStorageService()
