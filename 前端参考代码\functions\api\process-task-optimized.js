// 优化版任务处理 API - 减少KV存储使用
import { TTSProcessor } from '../utils/tts-processor.js';
import { R2StorageManager } from '../utils/r2-storage.js';
import { EnhancedTTSProcessor } from '../utils/enhanced-tts-processor.js';
import { EnhancedR2StorageManager } from '../utils/enhanced-r2-storage.js';
import { OptimizedTaskLogger } from '../utils/optimized-task-logger.js';
import { OptimizedTaskManager } from '../utils/optimized-task-manager.js';
import { AudiobookApiClient } from '../utils/audiobook-api-client.js';

export async function onRequestPost({ request, env }) {
    try {
        const { taskId } = await request.json();
        
        if (!taskId) {
            return Response.json({ error: '任务ID不能为空' }, { status: 400 });
        }

        // 处理任务
        await processTaskOptimized(taskId, env);

        return Response.json({
            success: true,
            message: '任务处理完成'
        });

    } catch (error) {
        console.error('处理任务失败:', error);
        return Response.json({ error: error.message }, { status: 500 });
    }
}

// 优化版任务处理函数
async function processTaskOptimized(taskId, env) {
    const logger = new OptimizedTaskLogger(taskId, env);
    const taskManager = new OptimizedTaskManager(taskId, env);
    const startTime = Date.now();
    
    try {
        // 获取任务信息
        const task = await taskManager.getTask();
        if (!task) {
            throw new Error('任务不存在');
        }

        // 记录任务开始 - 这会写入KV
        await logger.taskStart({
            filename: task.filename,
            email: task.email
        });

        // 检查文件类型，EPUB文件自动使用增强模式
        const ext = task.filename.toLowerCase().split('.').pop();
        const isEpubFile = ext === 'epub';
        
        if (isEpubFile) {
            // 只在控制台输出，不写入KV
            logger.info('检测到EPUB文件，自动使用增强模式处理', { 
                filename: task.filename,
                processingMode: 'ENHANCED_CHAPTER_SPLIT'
            });
            
            // 更新任务类型 - 这会写入KV（状态变化）
            await taskManager.updateTask({
                processingType: 'MULTI_CHAPTER',
                autoEnhanced: true
            });
            
            // 调用增强版处理逻辑
            return await processEnhancedTaskLogicOptimized(taskId, true, env, logger, taskManager, startTime);
        }

        // 非EPUB文件继续使用标准处理逻辑
        logger.info('使用标准模式处理', { 
            filename: task.filename,
            fileType: ext.toUpperCase(),
            processingMode: 'STANDARD'
        });

        // 标记任务开始处理 - 这会写入KV（状态变化）
        await taskManager.markAsProcessing();

        // 初始化存储管理器
        logger.info('初始化存储管理器');
        const r2Storage = new R2StorageManager(env.R2);

        // 从R2获取文件内容
        logger.info('开始读取文件内容');
        let fileContent;
        if (task.filePath) {
            console.log('从R2读取文件:', task.filePath);
            const fileObject = await r2Storage.getFile(task.filePath);
            if (!fileObject) {
                throw new Error('文件内容不存在');
            }
            
            if (ext === 'txt') {
                fileContent = await fileObject.text();
                console.log('文本文件读取完成，长度:', fileContent.length);
            } else {
                fileContent = await fileObject.arrayBuffer();
                console.log('二进制文件读取完成，大小:', fileContent.byteLength);
            }
        } else {
            console.log('从KV存储读取文件（兼容模式）');
            const fileKey = `file:${taskId}`;
            fileContent = await env.KV.get(fileKey);
            if (!fileContent) {
                throw new Error('文件内容不存在');
            }
        }

        // 初始化TTS处理器
        logger.info('初始化TTS处理器');
        const ttsProcessor = new TTSProcessor();
        
        // 提取文本内容
        logger.info('开始提取文本内容');
        const extractStartTime = Date.now();
        const text = await ttsProcessor.extractTextFromContent(fileContent, task.filename);
        
        if (!text || text.trim().length === 0) {
            throw new Error('无法从文件中提取有效文本');
        }

        const extractDuration = Date.now() - extractStartTime;
        logger.info('文本提取成功', { 
            textLength: text.length,
            extractionTime: extractDuration,
            estimatedDuration: Math.round(text.length / 5) + '秒'
        });

        // 保存提取的文本到R2
        logger.info('保存提取的文本到R2');
        await r2Storage.saveExtractedText(task.email, taskId, text);

        // 更新进度到10% - 只在达到关键进度点时写入KV
        await taskManager.updateProgress(10, '文本提取完成，开始音频转换');

        // 创建优化的进度回调
        const progressCallback = taskManager.createProgressCallback();

        // 使用外部API进行文本转语音处理
        logger.info('开始调用外部音频转换API');
        const apiStartTime = Date.now();
        
        try {
            const apiClient = new AudiobookApiClient();
            
            let r2FilePath;
            if (task.filePath) {
                r2FilePath = task.filePath;
                logger.info('使用现有R2文件路径', { r2FilePath });
            } else {
                // 使用与R2StorageManager一致的哈希算法
                const emailHash = r2Storage.hashEmail(task.email);
                const textFileName = `users/${emailHash}/tasks/${taskId}/original.txt`;
                const textBuffer = new TextEncoder().encode(text);

                await r2Storage.saveFile(textFileName, textBuffer, 'text/plain');
                r2FilePath = textFileName;
                logger.info('文本已保存为R2文件', { r2FilePath, emailHash });
            }

            // 提交转换任务
            logger.info('提交音频转换任务');
            const convertResult = await apiClient.submitConvertTaskWithR2Path(r2FilePath, {
                language: 'zh-CN',
                voiceName: 'zh-CN-XiaochenMultilingualNeural',
                speed: 1.0,
                pitch: 0,
                volume: 1.0
            });

            if (!convertResult.task_id) {
                throw new Error('提交转换任务失败：未返回任务ID');
            }

            logger.info('转换任务已提交', { externalTaskId: convertResult.task_id });

            // 等待转换完成
            logger.info('等待音频转换完成');
            const finalResult = await apiClient.waitForTaskCompletion(
                convertResult.task_id,
                progressCallback,
                1800000, // 30分钟超时
                5000     // 5秒轮询间隔
            );

            if (!finalResult.output_files || finalResult.output_files.length === 0) {
                throw new Error('转换完成但未生成音频文件');
            }

            const apiDuration = Date.now() - apiStartTime;
            logger.info('外部API转换完成', {
                duration: apiDuration,
                outputFiles: finalResult.output_files.length
            });

            // 处理转换结果
            logger.info('开始处理转换结果');
            const audioUrl = finalResult.output_files[0];
            
            // 下载并保存音频文件
            const audioResponse = await fetch(audioUrl);
            if (!audioResponse.ok) {
                throw new Error(`下载音频文件失败: ${audioResponse.status}`);
            }

            const audioBuffer = await audioResponse.arrayBuffer();
            const audioPath = await r2Storage.saveAudio(task.email, taskId, audioBuffer);
            
            // 保存音频元数据
            const audioMetadata = {
                originalUrl: audioUrl,
                r2Path: audioPath,
                fileSize: audioBuffer.byteLength,
                duration: finalResult.duration || '未知',
                createdAt: new Date().toISOString(),
                taskId: taskId
            };

            await r2Storage.saveAudioMetadata(task.email, taskId, audioMetadata);

            // 任务完成 - 这会写入KV
            await taskManager.markAsCompleted({
                audioPath: audioPath,
                audioMetadata: audioMetadata,
                totalDuration: Date.now() - startTime,
                apiDuration: apiDuration
            });

            // 记录任务完成 - 这会写入KV
            await logger.taskComplete({
                duration: Date.now() - startTime,
                fileSize: audioBuffer.byteLength,
                audioLength: finalResult.duration
            });

            // 扣除用户积分
            await chargeUserPoints(task.email, 5, taskId, env);

            logger.info('任务处理完成', { 
                taskId,
                totalDuration: Date.now() - startTime,
                audioPath
            });

        } catch (error) {
            const apiDuration = Date.now() - apiStartTime;
            
            // 记录错误 - 这会写入KV
            await logger.error('外部API调用失败', {
                error: error.message,
                duration: apiDuration
            });

            // 标记任务失败 - 这会写入KV
            await taskManager.markAsFailed(error, {
                failurePoint: 'EXTERNAL_API',
                apiDuration: apiDuration
            });

            throw error;
        }

    } catch (error) {
        console.error(`任务 ${taskId} 处理失败:`, error);
        
        // 记录任务失败 - 这会写入KV
        await logger.taskFailed(error, {
            duration: Date.now() - startTime
        });

        // 确保任务状态被标记为失败
        try {
            await taskManager.markAsFailed(error);
        } catch (updateError) {
            console.error('更新任务失败状态时出错:', updateError);
        }

        throw error;
    } finally {
        // 确保所有缓存的更新都写入KV
        await taskManager.flush();
        await logger.flushBuffer();
    }
}

// 扣除用户积分的函数保持不变
async function chargeUserPoints(email, points, taskId, env) {
    try {
        const userKey = `user:${email}`;
        
        // 使用事务性操作避免并发问题
        const chargePromise = async () => {
            const userData = await env.KV.get(userKey, { type: 'json' });
            if (!userData) {
                throw new Error('用户不存在');
            }

            // 检查积分余额
            if (userData.points < points) {
                throw new Error(`积分不足，当前余额: ${userData.points}，需要: ${points}`);
            }

            // 扣除积分
            userData.points -= points;
            userData.lastPointsUpdate = new Date().toISOString();

            // 更新用户数据
            await env.KV.put(userKey, JSON.stringify(userData));

            // 记录积分消费记录
            const recordKey = `points_record:${email}:${Date.now()}_${taskId}`;
            const pointsRecord = {
                email: email,
                taskId: taskId,
                type: 'CONSUME',
                amount: -points,
                description: '音频转换服务',
                timestamp: new Date().toISOString(),
                balanceAfter: userData.points
            };

            await env.KV.put(recordKey, JSON.stringify(pointsRecord));

            console.log(`用户 ${email} 积分扣除成功: -${points}，余额: ${userData.points}`);
            return userData.points;
        };

        // 执行扣费操作
        await chargePromise();

    } catch (error) {
        console.error('扣除用户积分失败:', error);
        throw new Error(`积分扣除失败: ${error.message}`);
    }
}

// 增强版任务处理逻辑（简化版本）
async function processEnhancedTaskLogicOptimized(taskId, enableChapterSplit, env, logger, taskManager, startTime) {
    try {
        logger.info('开始增强版任务处理');
        
        const task = await taskManager.getTask();
        
        // 标记为处理中
        await taskManager.markAsProcessing();

        // 初始化增强版处理器
        const enhancedStorage = new EnhancedR2StorageManager(env.R2);
        const enhancedProcessor = new EnhancedTTSProcessor();

        // 读取文件内容
        let fileContent;
        if (task.filePath) {
            const fileObject = await enhancedStorage.getFile(task.filePath);
            if (!fileObject) {
                throw new Error('文件内容不存在');
            }
            fileContent = await fileObject.arrayBuffer();
        } else {
            const fileKey = `file:${taskId}`;
            fileContent = await env.KV.get(fileKey);
            if (!fileContent) {
                throw new Error('文件内容不存在');
            }
        }

        // 创建进度回调
        const progressCallback = taskManager.createProgressCallback();

        // 处理增强版任务
        const result = await enhancedProcessor.processEnhancedTask(
            fileContent,
            task.filename,
            task.email,
            taskId,
            enhancedStorage,
            progressCallback,
            enableChapterSplit
        );

        // 任务完成
        await taskManager.markAsCompleted({
            audioPath: result.audioPath,
            chapters: result.chapters,
            totalDuration: Date.now() - startTime
        });

        await logger.taskComplete({
            duration: Date.now() - startTime,
            chapters: result.chapters?.length || 0
        });

        // 扣除用户积分
        await chargeUserPoints(task.email, 10, taskId, env); // 增强版更贵

        logger.info('增强版任务处理完成', { 
            taskId,
            totalDuration: Date.now() - startTime,
            chapters: result.chapters?.length || 0
        });

    } catch (error) {
        await logger.taskFailed(error);
        await taskManager.markAsFailed(error);
        throw error;
    }
} 