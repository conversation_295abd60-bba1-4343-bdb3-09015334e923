import { TTSProcessor } from '../utils/tts-processor.js';

export async function onRequestPost(context) {
    try {
        const { request } = context;
        const body = await request.json();
        
        const { text } = body;
        
        if (!text || typeof text !== 'string') {
            return new Response(JSON.stringify({
                success: false,
                error: '请提供有效的文本内容'
            }), {
                status: 400,
                headers: {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                }
            });
        }
        
        // 使用TTS处理器来分割文本
        const ttsProcessor = new TTSProcessor();
        const segments = ttsProcessor.splitText(text);
        
        const result = {
            success: true,
            length: text.length,
            segments: segments.map(segment => ({
                length: segment.length,
                preview: segment.substring(0, 50) + (segment.length > 50 ? '...' : '')
            })),
            segmentCount: segments.length,
            estimatedProcessingTime: Math.ceil(segments.length * 3), // 假设每段3秒处理时间
            recommendation: segments.length > 50 
                ? `文本较长，分割为${segments.length}个段落，预计处理时间较长，请耐心等待` 
                : '文本长度适中，可以正常处理',
            processingCapability: '已移除段数限制，支持处理任意长度的文本'
        };
        
        return new Response(JSON.stringify(result), {
            status: 200,
            headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            }
        });
        
    } catch (error) {
        console.error('检查文本长度失败:', error);
        
        return new Response(JSON.stringify({
            success: false,
            error: '检查文本长度时发生错误',
            details: error.message
        }), {
            status: 500,
            headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            }
        });
    }
}

export async function onRequestOptions(context) {
    return new Response(null, {
        status: 204,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type'
        }
    });
} 