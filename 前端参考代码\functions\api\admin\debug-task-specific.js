import { UserIdResolver } from '../../utils/user-id-resolver.js';

/**
 * 特定任务状态诊断工具 - 修复版
 * 专门用于诊断任务状态同步问题，使用多路径检查和智能用户ID解析
 */
export async function onRequestPost({ request, env }) {
    try {
        const { email, taskId } = await request.json();
        
        if (!email || !taskId) {
            return Response.json({ error: '缺少邮箱或任务ID参数' }, { status: 400 });
        }

        console.log(`🔍 开始特定任务诊断: 邮箱=${email}, 任务ID=${taskId}`);

        const result = {
            email: email,
            taskId: taskId,
            timestamp: new Date().toISOString(),
            userIdResolution: null,
            pathsChecked: [],
            filesFound: [],
            audioFiles: [],
            metadataFiles: [],
            playlistFiles: [],
            diagnosis: {
                hasAudioFiles: false,
                hasMetadata: false,
                hasPlaylist: false,
                isComplete: false,
                issues: [],
                recommendations: []
            }
        };

        // 1. 解析用户ID
        const resolver = new UserIdResolver(env);
        const userIdResult = await resolver.resolveUserId(email, taskId);
        
        result.userIdResolution = {
            success: !!userIdResult.userId,
            userId: userIdResult.userId,
            method: userIdResult.method,
            confidence: userIdResult.confidence,
            details: userIdResult.details
        };

        if (!userIdResult.userId) {
            result.diagnosis.issues.push('无法解析用户ID');
            result.diagnosis.recommendations.push('检查用户数据和邮箱格式');
            return Response.json(result);
        }

        const userId = userIdResult.userId;
        console.log(`✅ 解析到用户ID: ${userId} (方法: ${userIdResult.method}, 置信度: ${userIdResult.confidence}%)`);

        // 2. 检查多个可能的路径
        const basePathPatterns = [
            // 新的统一路径
            `users/${userId}/tasks/${taskId}/`,
            // 优先检查audios路径（旧格式）
            `users/${userId}/audios/${taskId}/`,
            // 新的audiobooks路径
            `users/${userId}/audiobooks/${taskId}/`,
            // 旧的uploads路径（向后兼容）
            `users/${userId}/uploads/${taskId}/`,
            // 其他可能的路径
            `users/${userId}/audio/${taskId}/`,
            `audiobooks/${userId}/${taskId}/`,
            `audios/${userId}/${taskId}/`
        ];

        // 添加带bucket前缀的路径（处理用户看到的完整路径格式）
        const pathPatterns = [
            ...basePathPatterns,
            // 带bucket前缀的路径
            ...basePathPatterns.map(path => `audiobook-storage/${path}`)
        ];

        // 尝试多个R2绑定
        const allBindings = [
            { name: 'R2', bucket: env.R2 },
            { name: 'STORAGE', bucket: env.STORAGE },
            { name: 'AUDIO_BUCKET', bucket: env.AUDIO_BUCKET }
        ];

        const r2Bindings = allBindings.filter(binding => binding.bucket); // 只保留存在的绑定

        console.log(`🔧 所有绑定状态: ${allBindings.map(b => `${b.name}=${!!b.bucket}`).join(', ')}`);
        console.log(`🔧 可用的R2绑定: ${r2Bindings.map(b => b.name).join(', ')}`);

        if (r2Bindings.length === 0) {
            return Response.json({
                error: '没有可用的R2绑定',
                details: '所有R2绑定都未定义，请检查Cloudflare Pages的R2配置',
                bindingStatus: allBindings.map(b => ({ name: b.name, available: !!b.bucket }))
            }, { status: 500 });
        }

        for (const binding of r2Bindings) {
            console.log(`\n🔍 使用绑定: ${binding.name}`);

            for (const basePath of pathPatterns) {
                try {
                    console.log(`🔍 检查路径: ${basePath}`);

                    const listResult = await binding.bucket.list({
                        prefix: basePath,
                        limit: 50
                    });

                    const pathInfo = {
                        binding: binding.name,
                        path: basePath,
                        exists: false,
                        fileCount: 0,
                        files: [],
                        audioFiles: [],
                        metadataFiles: [],
                        playlistFiles: []
                    };

                    if (listResult.objects && listResult.objects.length > 0) {
                        pathInfo.exists = true;
                        pathInfo.fileCount = listResult.objects.length;

                        for (const obj of listResult.objects) {
                            const fileInfo = {
                                key: obj.key,
                                size: obj.size,
                                lastModified: obj.uploaded,
                                etag: obj.etag
                            };

                            pathInfo.files.push(fileInfo);
                            result.filesFound.push(fileInfo);

                            // 分类文件
                            if (obj.key.match(/\.(mp3|wav|m4a|aac)$/i)) {
                                pathInfo.audioFiles.push(fileInfo);
                                result.audioFiles.push(fileInfo);
                            } else if (obj.key.endsWith('metadata.json')) {
                                pathInfo.metadataFiles.push(fileInfo);
                                result.metadataFiles.push(fileInfo);
                            } else if (obj.key.match(/playlist\.(json|m3u8)$/i)) {
                                pathInfo.playlistFiles.push(fileInfo);
                                result.playlistFiles.push(fileInfo);
                            }
                        }

                        console.log(`✅ 在 ${basePath} 找到 ${pathInfo.fileCount} 个文件 [绑定: ${binding.name}]`);
                        console.log(`   - 音频文件: ${pathInfo.audioFiles.length}`);
                        console.log(`   - 元数据文件: ${pathInfo.metadataFiles.length}`);
                        console.log(`   - 播放列表文件: ${pathInfo.playlistFiles.length}`);

                        // 如果找到了音频文件，记录成功的绑定
                        if (pathInfo.audioFiles.length > 0) {
                            console.log(`🎯 成功找到音频文件！使用绑定: ${binding.name}, 路径: ${basePath}`);
                        }
                    } else {
                        console.log(`❌ 路径为空: ${basePath} [绑定: ${binding.name}]`);
                    }

                    result.pathsChecked.push(pathInfo);

                } catch (error) {
                    console.warn(`检查路径 ${basePath} [绑定: ${binding.name}] 失败:`, error);
                    result.pathsChecked.push({
                        binding: binding.name,
                        path: basePath,
                        exists: false,
                        error: error.message
                    });
                }
            }
        }

        // 3. 生成诊断结果
        result.diagnosis.hasAudioFiles = result.audioFiles.length > 0;
        result.diagnosis.hasMetadata = result.metadataFiles.length > 0;
        result.diagnosis.hasPlaylist = result.playlistFiles.length > 0;
        result.diagnosis.isComplete = result.diagnosis.hasAudioFiles && (result.diagnosis.hasMetadata || result.diagnosis.hasPlaylist);

        // 4. 分析问题和建议
        if (!result.diagnosis.hasAudioFiles) {
            result.diagnosis.issues.push('未找到音频文件');
            result.diagnosis.recommendations.push('检查任务处理是否完成');
            result.diagnosis.recommendations.push('验证R2存储权限配置');
        }

        if (!result.diagnosis.hasMetadata && !result.diagnosis.hasPlaylist) {
            result.diagnosis.issues.push('未找到元数据或播放列表文件');
            result.diagnosis.recommendations.push('检查任务处理流程是否完整');
        }

        if (result.userIdResolution.confidence < 90) {
            result.diagnosis.issues.push(`用户ID解析置信度较低 (${result.userIdResolution.confidence}%)`);
            result.diagnosis.recommendations.push('验证用户数据完整性');
        }

        // 5. 特殊情况检查
        if (result.filesFound.length === 0) {
            result.diagnosis.issues.push('所有检查路径都未找到文件');
            result.diagnosis.recommendations.push('检查任务是否真正开始处理');
            result.diagnosis.recommendations.push('验证用户ID和任务ID的正确性');
        } else if (result.diagnosis.hasAudioFiles) {
            result.diagnosis.recommendations.push('音频文件存在，任务应该可以正常播放');
        }

        console.log(`📊 诊断完成: 音频文件=${result.audioFiles.length}, 元数据=${result.metadataFiles.length}, 播放列表=${result.playlistFiles.length}`);

        return Response.json(result);

    } catch (error) {
        console.error('特定任务诊断失败:', error);
        return Response.json({ 
            error: '诊断失败',
            details: error.message,
            timestamp: new Date().toISOString()
        }, { status: 500 });
    }
}

// 处理OPTIONS请求（CORS预检）
export async function onRequestOptions() {
    return new Response(null, {
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type',
            'Access-Control-Max-Age': '86400'
        }
    });
}
