/**
 * 新的文件上传API - 使用R2状态管理
 * 完全基于R2存储，不依赖KV
 */

import { R2StorageManager } from '../../utils/r2-storage.js';
import { R2TaskStatusManager } from '../../utils/r2-task-status-manager.js';

export async function onRequestPost({ request, env }) {
    try {
        // 解析表单数据
        const formData = await request.formData();
        const file = formData.get('file');
        const email = formData.get('email');

        // 验证输入
        if (!file || !email) {
            return Response.json({ error: '文件和邮箱不能为空' }, { status: 400 });
        }

        // 验证文件类型
        const allowedTypes = ['text/plain', 'application/epub+zip'];
        const allowedExtensions = ['.txt', '.epub'];
        
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
        if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {
            return Response.json({ 
                error: '不支持的文件格式，请上传 TXT 或 EPUB 文件' 
            }, { status: 400 });
        }

        // 验证文件大小 (50MB限制)
        const maxSize = 50 * 1024 * 1024;
        if (file.size > maxSize) {
            return Response.json({ 
                error: '文件大小超过限制，最大支持50MB' 
            }, { status: 400 });
        }

        // 验证用户积分（简化版，实际应该从用户管理系统获取）
        const userKey = `user:${email}`;
        const userData = await env.KV.get(userKey, { type: 'json' });
        
        if (!userData) {
            return Response.json({ error: '用户不存在' }, { status: 404 });
        }

        const requiredPoints = 5; // 基础积分要求
        if (userData.points < requiredPoints) {
            return Response.json({ 
                error: `积分不足，需要${requiredPoints}积分，当前${userData.points}积分` 
            }, { status: 402 });
        }

        // 生成任务ID
        const taskId = generateTaskId();
        console.log(`🚀 创建新任务 (R2架构): ${taskId} - ${file.name}`);

        // 初始化存储管理器
        const r2Storage = new R2StorageManager(env.R2);
        const statusManager = new R2TaskStatusManager(env.R2, env);

        // 保存原始文件到R2
        const fileContent = await file.arrayBuffer();
        const filePath = await r2Storage.saveUploadedFile(
            email, 
            taskId, 
            file.name, 
            fileContent, 
            file.type
        );

        // 在R2中创建任务状态文件
        const taskStatus = await statusManager.createTaskStatus({
            id: taskId,
            email: email,
            filename: file.name,
            type: 'file',
            fileSize: file.size,
            filePath: filePath
        });

        console.log(`✅ R2任务状态已创建: ${taskId}`);

        // 立即触发任务处理
        try {
            console.log(`🔄 触发R2任务处理: ${taskId}`);
            const processResponse = await fetch(new URL('/api/process-task-r2', request.url), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    taskId: taskId,
                    email: email,
                    useR2Status: true
                })
            });

            console.log(`📤 任务 ${taskId} 处理请求已发送 (R2), 状态: ${processResponse.status}`);

            if (!processResponse.ok) {
                const errorText = await processResponse.text();
                console.error(`⚠️ 任务处理请求失败 ${taskId}:`, {
                    status: processResponse.status,
                    statusText: processResponse.statusText,
                    errorText: errorText
                });
            }
        } catch (processError) {
            console.error(`❌ 触发R2任务处理失败 ${taskId}:`, {
                error: processError.message,
                stack: processError.stack,
                taskId: taskId,
                email: email
            });
            // 不影响上传结果，任务会在后续处理中被处理
        }

        return Response.json({
            success: true,
            taskId: taskId,
            points: userData.points,
            message: '文件上传成功，开始转换 (R2架构)',
            architecture: 'R2',
            statusPath: `users/${email}/tasks/${taskId}/status.json`
        });

    } catch (error) {
        console.error('R2文件上传错误:', error);
        return Response.json({ 
            error: '服务器内部错误',
            details: error.message 
        }, { status: 500 });
    }
}

// 生成任务ID
function generateTaskId() {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 9);
    return `r2_${timestamp}_${random}`; // 添加r2前缀标识新架构
}

// URL上传版本
export async function onRequestPut({ request, env }) {
    try {
        const { url, email, filename } = await request.json();

        // 验证输入
        if (!url || !email || !filename) {
            return Response.json({ error: 'URL、邮箱和文件名不能为空' }, { status: 400 });
        }

        // 验证URL格式
        try {
            new URL(url);
        } catch {
            return Response.json({ error: '无效的URL格式' }, { status: 400 });
        }

        // 验证用户积分
        const userKey = `user:${email}`;
        const userData = await env.KV.get(userKey, { type: 'json' });
        
        if (!userData) {
            return Response.json({ error: '用户不存在' }, { status: 404 });
        }

        const requiredPoints = 5;
        if (userData.points < requiredPoints) {
            return Response.json({ 
                error: `积分不足，需要${requiredPoints}积分，当前${userData.points}积分` 
            }, { status: 402 });
        }

        // 生成任务ID
        const taskId = generateTaskId();
        console.log(`🚀 创建URL任务 (R2架构): ${taskId} - ${url}`);

        // 初始化状态管理器
        const statusManager = new R2TaskStatusManager(env.R2, env);

        // 创建URL任务状态
        const taskStatus = await statusManager.createTaskStatus({
            id: taskId,
            email: email,
            filename: filename,
            type: 'url',
            fileSize: null, // URL任务暂时不知道文件大小
            filePath: url   // 对于URL任务，filePath存储原始URL
        });

        console.log(`✅ R2 URL任务状态已创建: ${taskId}`);

        // 触发URL任务处理
        try {
            const processResponse = await fetch(new URL('/api/process-url-task-r2', request.url), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ 
                    taskId: taskId,
                    email: email,
                    url: url,
                    filename: filename,
                    useR2Status: true 
                })
            });
            
            console.log(`URL任务 ${taskId} 处理请求已发送 (R2), 状态: ${processResponse.status}`);
        } catch (processError) {
            console.error('触发R2 URL任务处理失败:', processError);
        }

        return Response.json({
            success: true,
            taskId: taskId,
            points: userData.points,
            message: 'URL任务创建成功，开始处理 (R2架构)',
            architecture: 'R2',
            url: url
        });

    } catch (error) {
        console.error('R2 URL任务创建错误:', error);
        return Response.json({ 
            error: '服务器内部错误',
            details: error.message 
        }, { status: 500 });
    }
}

// 获取上传进度（预留接口）
export async function onRequestGet({ request, env }) {
    const url = new URL(request.url);
    const taskId = url.searchParams.get('taskId');
    const email = url.searchParams.get('email');

    if (!taskId || !email) {
        return Response.json({ error: '缺少必需参数' }, { status: 400 });
    }

    try {
        const statusManager = new R2TaskStatusManager(env.R2, env);
        const status = await statusManager.getTaskStatus(taskId, email);

        if (!status) {
            return Response.json({ error: '任务不存在' }, { status: 404 });
        }

        return Response.json({
            success: true,
            status: status,
            architecture: 'R2'
        });

    } catch (error) {
        console.error('获取R2任务状态失败:', error);
        return Response.json({ 
            error: '获取状态失败',
            details: error.message 
        }, { status: 500 });
    }
}
