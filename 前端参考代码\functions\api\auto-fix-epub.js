// 自动修复EPUB音频书任务 - 一键修复
export async function onRequestGet({ request, env }) {
    const url = new URL(request.url);
    const email = url.searchParams.get('email');
    
    if (!email) {
        return new Response(`
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>EPUB音频书自动修复</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
        .form { background: #f9f9f9; padding: 20px; border-radius: 8px; }
        input { width: 100%; padding: 10px; margin: 10px 0; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>🔧 EPUB音频书自动修复</h1>
    <p>如果您的EPUB文件转换成功但在音频库中看不到，请输入邮箱进行自动修复：</p>
    <div class="form">
        <input type="email" id="email" placeholder="请输入您的邮箱地址" required>
        <button onclick="window.location.href='/api/auto-fix-epub?email=' + encodeURIComponent(document.getElementById('email').value)">
            自动修复EPUB音频书
        </button>
    </div>
</body>
</html>
        `, {
            headers: { 'Content-Type': 'text/html; charset=utf-8' }
        });
    }

    try {
        let result = {
            email: email,
            timestamp: new Date().toISOString(),
            totalTasks: 0,
            enhancedTasks: 0,
            fixedTasks: 0,
            details: []
        };

        // 获取用户的所有任务
        const taskList = await env.KV.list({ prefix: 'task:' });
        const userTasks = [];

        for (const key of taskList.keys) {
            const task = await env.KV.get(key.name, { type: 'json' });
            if (task && task.email === email) {
                userTasks.push({ key: key.name, task });
            }
        }

        result.totalTasks = userTasks.length;

        // 查找需要修复的增强版任务
        const enhancedTasks = userTasks.filter(({ task }) => {
            return task.status === 'SUCCESS' && 
                   (task.isEnhanced || task.audioType === 'MULTI_CHAPTER' || 
                    task.processingType === 'MULTI_CHAPTER' || task.totalChapters > 0);
        });

        result.enhancedTasks = enhancedTasks.length;

        // 自动修复缺少audioUrl的任务
        for (const { key, task } of enhancedTasks) {
            const taskInfo = {
                id: task.id,
                filename: task.filename,
                status: task.status,
                hadAudioUrl: !!task.audioUrl,
                fixed: false
            };

            if (!task.audioUrl) {
                // 执行修复
                task.audioUrl = `/api/audio/playlist/${task.id}`;
                task.playlistUrl = `/api/audio/playlist/${task.id}`;
                task.metadataUrl = `/api/audio/metadata/${task.id}`;
                task.isEnhanced = true;
                
                // 如果没有章节信息，尝试从其他字段获取
                if (!task.chapters && task.totalChapters > 0) {
                    task.chapters = [];
                    for (let i = 1; i <= task.totalChapters; i++) {
                        task.chapters.push({
                            index: i,
                            title: `第${i}章`,
                            duration: Math.ceil((task.totalDuration || 0) / task.totalChapters),
                            wordCount: Math.ceil((task.totalWordCount || 0) / task.totalChapters)
                        });
                    }
                }
                
                // 保存修复后的任务
                await env.KV.put(key, JSON.stringify(task));
                result.fixedTasks++;
                taskInfo.fixed = true;
                taskInfo.newAudioUrl = task.audioUrl;
            }

            result.details.push(taskInfo);
        }

        // 返回修复结果页面
        const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>修复完成 - EPUB音频书</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            max-width: 800px; 
            margin: 0 auto; 
            padding: 20px; 
            background: #f5f5f5; 
        }
        .container { 
            background: white; 
            padding: 30px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .success { color: #28a745; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        .task-item { 
            background: #f8f9fa; 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 5px; 
            border-left: 4px solid #007bff; 
        }
        .fixed { border-left-color: #28a745; }
        .button { 
            display: inline-block; 
            background: #007bff; 
            color: white; 
            padding: 12px 24px; 
            text-decoration: none; 
            border-radius: 5px; 
            margin: 10px 5px; 
        }
        .button:hover { background: #0056b3; }
        .stats { 
            background: #e9ecef; 
            padding: 20px; 
            border-radius: 5px; 
            margin: 20px 0; 
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 EPUB音频书修复完成！</h1>
        
        <div class="stats">
            <h3>📊 修复统计</h3>
            <p><strong>邮箱：</strong>${email}</p>
            <p><strong>总任务数：</strong>${result.totalTasks}</p>
            <p><strong>增强版任务数：</strong>${result.enhancedTasks}</p>
            <p><strong class="success">已修复任务数：</strong>${result.fixedTasks}</p>
            <p><strong>修复时间：</strong>${new Date(result.timestamp).toLocaleString('zh-CN')}</p>
        </div>

        ${result.fixedTasks > 0 ? `
        <div class="success">
            <h3>✅ 修复成功！</h3>
            <p>已为您修复了 <strong>${result.fixedTasks}</strong> 个EPUB音频书任务。</p>
        </div>

        <h3>🔧 已修复的任务：</h3>
        ${result.details.filter(t => t.fixed).map(task => `
        <div class="task-item fixed">
            <strong>${task.filename}</strong><br>
            <small>任务ID: ${task.id}</small><br>
            <small class="success">✅ 已添加audioUrl字段</small>
        </div>
        `).join('')}

        <div style="background: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0;">
            <h4>🎉 修复完成！现在请：</h4>
            <ol>
                <li>回到您的音频书网站主页</li>
                <li>进入"我的音频库"</li>
                <li>刷新页面（按F5或Ctrl+R）</li>
                <li>您的EPUB音频书应该出现了！</li>
            </ol>
        </div>
        ` : `
        <div class="warning">
            <h3>⚠️ 没有需要修复的任务</h3>
            <p>您的所有增强版任务都已经有正确的audioUrl字段。</p>
            ${result.enhancedTasks === 0 ? '<p>没有找到增强版（EPUB）任务。</p>' : ''}
        </div>
        `}

        ${result.details.length > 0 ? `
        <h3>📚 您的增强版任务详情：</h3>
        ${result.details.map(task => `
        <div class="task-item ${task.fixed ? 'fixed' : ''}">
            <strong>${task.filename}</strong><br>
            <small>任务ID: ${task.id}</small><br>
            <small>状态: ${task.status}</small><br>
            <small>${task.hadAudioUrl ? '✅ 已有audioUrl' : (task.fixed ? '🔧 已修复audioUrl' : '❌ 缺少audioUrl')}</small>
        </div>
        `).join('')}
        ` : ''}

        <div style="text-align: center; margin-top: 30px;">
            <a href="/" class="button">返回主页</a>
            <a href="/api/auto-fix-epub?email=${encodeURIComponent(email)}" class="button">重新检查</a>
        </div>
    </div>
</body>
</html>
        `;

        return new Response(html, {
            headers: { 'Content-Type': 'text/html; charset=utf-8' }
        });

    } catch (error) {
        console.error('自动修复EPUB任务失败:', error);
        
        const errorHtml = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>修复失败</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
        .error { background: #f8d7da; color: #721c24; padding: 20px; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="error">
        <h2>❌ 修复失败</h2>
        <p><strong>错误信息：</strong>${error.message}</p>
        <p>请联系技术支持或稍后重试。</p>
    </div>
    <a href="/api/auto-fix-epub">返回重试</a>
</body>
</html>
        `;
        
        return new Response(errorHtml, {
            status: 500,
            headers: { 'Content-Type': 'text/html; charset=utf-8' }
        });
    }
} 