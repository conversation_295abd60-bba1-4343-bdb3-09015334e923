// R2存储调试API
import { EnhancedR2StorageManager } from '../../utils/enhanced-r2-storage.js';
import { UserIdResolver } from '../../utils/user-id-resolver.js';

export async function onRequestPost({ request, env }) {
    try {
        const { action, email, taskId } = await request.json();
        
        if (!action) {
            return Response.json({ error: '缺少操作类型' }, { status: 400 });
        }

        const r2Storage = new EnhancedR2StorageManager(env.R2);
        
        switch (action) {
            case 'list-files':
                return await listFiles(r2Storage, email, taskId);
            
            case 'check-file':
                return await checkFile(r2Storage, email, taskId, request.filePath);
            
            case 'get-metadata':
                return await getMetadata(r2Storage, email, taskId);
            
            default:
                return Response.json({ error: '未知操作类型' }, { status: 400 });
        }

    } catch (error) {
        console.error('R2存储调试API错误:', error);
        return Response.json({ 
            error: '服务器内部错误',
            details: error.message 
        }, { status: 500 });
    }
}

// 列出文件
async function listFiles(r2Storage, email, taskId) {
    try {
        if (!email || !taskId) {
            return Response.json({ error: '缺少邮箱或任务ID' }, { status: 400 });
        }

        // 使用UserIdResolver获取正确的用户ID
        const resolver = new UserIdResolver({ AUDIO_BUCKET: r2Storage.r2, KV: null });
        const userIdResult = await resolver.resolveUserId(email, taskId);

        if (!userIdResult.userId) {
            console.error(`❌ 无法解析用户ID: ${email}`);
            return Response.json({
                error: '无法解析用户ID',
                details: userIdResult
            }, { status: 400 });
        }

        const userId = userIdResult.userId;
        console.log(`✅ 解析到用户ID: ${userId} (方法: ${userIdResult.method}, 置信度: ${userIdResult.confidence}%)`);

        // 检查多个可能的路径，优先检查audios路径
        const pathPatterns = [
            `users/${userId}/audios/${taskId}/`,
            `users/${userId}/audiobooks/${taskId}/`,
            `users/${userId}/audio/${taskId}/`
        ];

        let allFiles = [];
        let foundPaths = [];
        let totalSize = 0;

        for (const basePath of pathPatterns) {
            try {
                console.log(`调试R2存储: 检查路径 - ${basePath}`);

                // 列出该路径下的所有文件
                const listResult = await r2Storage.r2.list({ prefix: basePath });

                if (listResult.objects && listResult.objects.length > 0) {
                    const pathFiles = listResult.objects.map(obj => ({
                        key: obj.key,
                        size: obj.size,
                        lastModified: obj.uploaded,
                        etag: obj.etag,
                        path: basePath
                    }));

                    allFiles.push(...pathFiles);
                    foundPaths.push({
                        path: basePath,
                        fileCount: pathFiles.length,
                        size: pathFiles.reduce((sum, file) => sum + file.size, 0)
                    });

                    totalSize += pathFiles.reduce((sum, file) => sum + file.size, 0);
                    console.log(`✅ 在 ${basePath} 找到 ${pathFiles.length} 个文件`);
                }
            } catch (error) {
                console.warn(`检查路径 ${basePath} 失败:`, error);
            }
        }

        console.log(`调试R2存储: 总共找到 ${allFiles.length} 个文件`);

        return Response.json({
            userId: userId,
            userIdResolution: {
                method: userIdResult.method,
                confidence: userIdResult.confidence,
                details: userIdResult.details
            },
            pathsChecked: pathPatterns,
            foundPaths: foundPaths,
            files: allFiles,
            totalFiles: allFiles.length,
            totalSize: totalSize
        });

    } catch (error) {
        console.error('列出文件失败:', error);
        return Response.json({
            error: '列出文件失败',
            details: error.message
        }, { status: 500 });
    }
}

// 检查特定文件
async function checkFile(r2Storage, email, taskId, filePath) {
    try {
        if (!email || !taskId || !filePath) {
            return Response.json({ error: '缺少必要参数' }, { status: 400 });
        }

        // 使用UserIdResolver获取正确的用户ID
        const resolver = new UserIdResolver({ AUDIO_BUCKET: r2Storage.r2, KV: null });
        const userIdResult = await resolver.resolveUserId(email, taskId);

        if (!userIdResult.userId) {
            console.error(`❌ 无法解析用户ID: ${email}`);
            return Response.json({
                error: '无法解析用户ID',
                details: userIdResult
            }, { status: 400 });
        }

        const userId = userIdResult.userId;
        console.log(`✅ 解析到用户ID: ${userId} (方法: ${userIdResult.method})`);

        // 检查多个可能的路径
        const possiblePaths = [
            `users/${userId}/audios/${taskId}/${filePath}`,
            `users/${userId}/audiobooks/${taskId}/${filePath}`,
            `users/${userId}/audio/${taskId}/${filePath}`
        ];

        for (const fullPath of possiblePaths) {
            try {
                console.log(`调试R2存储: 检查文件 - ${fullPath}`);

                // 使用head方法检查文件是否存在（更高效）
                const fileObject = await r2Storage.r2.head(fullPath);

                if (fileObject) {
                    console.log(`✅ 找到文件: ${fullPath}`);
                    return Response.json({
                        exists: true,
                        path: fullPath,
                        size: fileObject.size,
                        contentType: fileObject.httpMetadata?.contentType,
                        lastModified: fileObject.uploaded,
                        etag: fileObject.etag,
                        customMetadata: fileObject.customMetadata,
                        userIdResolution: {
                            method: userIdResult.method,
                            confidence: userIdResult.confidence
                        }
                    });
                }
            } catch (error) {
                console.log(`❌ 路径不存在: ${fullPath}`);
                continue;
            }
        }

        // 所有路径都没找到文件
        return Response.json({
            exists: false,
            checkedPaths: possiblePaths,
            userIdResolution: {
                method: userIdResult.method,
                confidence: userIdResult.confidence,
                details: userIdResult.details
            }
        });

    } catch (error) {
        console.error('检查文件失败:', error);
        return Response.json({
            error: '检查文件失败',
            details: error.message
        }, { status: 500 });
    }
}

// 获取元数据
async function getMetadata(r2Storage, email, taskId) {
    try {
        if (!email || !taskId) {
            return Response.json({ error: '缺少邮箱或任务ID' }, { status: 400 });
        }

        console.log(`调试R2存储: 获取元数据 - 邮箱: ${email}, 任务: ${taskId}`);

        // 使用UserIdResolver获取正确的用户ID
        const resolver = new UserIdResolver({ AUDIO_BUCKET: r2Storage.r2, KV: null });
        const userIdResult = await resolver.resolveUserId(email, taskId);

        if (!userIdResult.userId) {
            console.error(`❌ 无法解析用户ID: ${email}`);
            return Response.json({
                error: '无法解析用户ID',
                details: userIdResult
            }, { status: 400 });
        }

        const userId = userIdResult.userId;
        console.log(`✅ 解析到用户ID: ${userId} (方法: ${userIdResult.method})`);

        // 检查多个可能的元数据路径
        const metadataPaths = [
            `users/${userId}/audios/${taskId}/metadata.json`,
            `users/${userId}/audiobooks/${taskId}/metadata.json`,
            `users/${userId}/audio/${taskId}/metadata.json`
        ];

        let metadata = null;
        let foundMetadataPath = null;

        for (const path of metadataPaths) {
            try {
                console.log(`检查元数据路径: ${path}`);
                const metadataObject = await r2Storage.r2.get(path);
                if (metadataObject) {
                    metadata = await metadataObject.json();
                    foundMetadataPath = path;
                    console.log(`✅ 找到元数据: ${path}`);
                    break;
                }
            } catch (error) {
                console.log(`❌ 元数据路径不存在: ${path}`);
                continue;
            }
        }

        if (!metadata) {
            return Response.json({
                exists: false,
                message: '元数据文件不存在',
                checkedPaths: metadataPaths,
                userIdResolution: {
                    method: userIdResult.method,
                    confidence: userIdResult.confidence
                }
            });
        }

        // 检查播放列表路径
        const playlistPaths = [
            `users/${userId}/audios/${taskId}/playlist.json`,
            `users/${userId}/audiobooks/${taskId}/playlist.json`,
            `users/${userId}/audio/${taskId}/playlist.json`
        ];

        let playlist = null;
        let foundPlaylistPath = null;

        for (const path of playlistPaths) {
            try {
                console.log(`检查播放列表路径: ${path}`);
                const playlistObject = await r2Storage.r2.get(path);
                if (playlistObject) {
                    playlist = await playlistObject.json();
                    foundPlaylistPath = path;
                    console.log(`✅ 找到播放列表: ${path}`);
                    break;
                }
            } catch (error) {
                console.log(`❌ 播放列表路径不存在: ${path}`);
                continue;
            }
        }

        return Response.json({
            exists: true,
            metadata: metadata,
            metadataPath: foundMetadataPath,
            playlist: playlist ? {
                title: playlist.title,
                totalChapters: playlist.totalChapters,
                totalDuration: playlist.totalDuration
            } : null,
            playlistPath: foundPlaylistPath,
            summary: {
                title: metadata.title,
                totalChapters: metadata.totalChapters,
                totalDuration: metadata.totalDuration,
                totalSize: metadata.totalSize,
                createdAt: metadata.createdAt
            },
            userIdResolution: {
                method: userIdResult.method,
                confidence: userIdResult.confidence
            }
        });

    } catch (error) {
        console.error('获取元数据失败:', error);
        return Response.json({
            error: '获取元数据失败',
            details: error.message
        }, { status: 500 });
    }
}

// 处理OPTIONS请求（CORS预检）
export async function onRequestOptions() {
    return new Response(null, {
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type',
            'Access-Control-Max-Age': '86400'
        }
    });
} 