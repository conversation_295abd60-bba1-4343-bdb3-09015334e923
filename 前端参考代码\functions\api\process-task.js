// 任务处理 API
import { TTSProcessor } from '../utils/tts-processor.js';
import { R2StorageManager } from '../utils/r2-storage.js';
import { R2TaskStatusManager } from '../utils/r2-task-status-manager.js';
import { EnhancedTTSProcessor } from '../utils/enhanced-tts-processor.js';
import { EnhancedR2StorageManager } from '../utils/enhanced-r2-storage.js';
import { TaskLogger } from '../utils/task-logger.js';
import { AudiobookApiClient } from '../utils/audiobook-api-client.js';

export async function onRequestPost({ request, env }) {
    try {
        const { taskId, email, useR2Status = false } = await request.json();

        if (!taskId) {
            return Response.json({ error: '任务ID不能为空' }, { status: 400 });
        }

        // 检测任务架构并选择相应的处理方式
        if (useR2Status || taskId.startsWith('r2_')) {
            console.log(`🔄 使用R2架构处理任务: ${taskId}`);
            // 异步处理R2任务
            processR2Task(taskId, email, env).catch(error => {
                console.error(`R2任务处理失败 ${taskId}:`, error);
            });

            return Response.json({
                success: true,
                message: 'R2任务处理已开始',
                architecture: 'R2'
            });
        } else {
            console.log(`🔄 使用传统架构处理任务: ${taskId}`);
            // 处理传统KV任务
            await processTask(taskId, env);

            return Response.json({
                success: true,
                message: '任务处理完成',
                architecture: 'KV'
            });
        }

    } catch (error) {
        console.error('处理任务失败:', error);
        return Response.json({ error: error.message }, { status: 500 });
    }
}

// 主要的任务处理函数
async function processTask(taskId, env) {
    const taskKey = `task:${taskId}`;
    const logger = new TaskLogger(taskId, env);
    const startTime = Date.now();
    
    try {
        await logger.info('任务处理开始', { taskId });
        
        // 获取任务信息
        await logger.stepStart('获取任务信息');
        const task = await env.KV.get(taskKey, { type: 'json' });
        if (!task) {
            throw new Error('任务不存在');
        }
        await logger.stepComplete('获取任务信息', null, { 
            filename: task.filename, 
            email: task.email,
            createdAt: task.createdAt
        });

        // 检查文件类型，EPUB文件自动使用增强模式
        const ext = task.filename.toLowerCase().split('.').pop();
        const isEpubFile = ext === 'epub';
        
        if (isEpubFile) {
            await logger.info('检测到EPUB文件，自动使用增强模式处理', { 
                filename: task.filename,
                processingMode: 'ENHANCED_CHAPTER_SPLIT'
            });
            
            // 更新任务类型
            task.processingType = 'MULTI_CHAPTER';
            task.autoEnhanced = true; // 标记为自动启用增强模式
            await env.KV.put(taskKey, JSON.stringify(task));
            
            // 调用增强版处理逻辑
            return await processEnhancedTaskLogic(taskId, true, env, logger, startTime);
        }

        // 非EPUB文件继续使用标准处理逻辑
        await logger.info('使用标准模式处理', { 
            filename: task.filename,
            fileType: ext.toUpperCase(),
            processingMode: 'STANDARD'
        });

        // 更新状态为处理中
        await logger.stepStart('更新任务状态');
        task.status = 'PROCESSING';
        task.progress = 5;
        task.startedAt = new Date().toISOString();
        await env.KV.put(taskKey, JSON.stringify(task));
        await logger.progress(5, '任务状态已更新为处理中');
        await logger.stepComplete('更新任务状态');

        // 初始化R2存储管理器
        await logger.stepStart('初始化存储管理器');
        const r2Storage = new R2StorageManager(env.R2);
        await logger.stepComplete('初始化存储管理器');

        // 从R2获取文件内容
        await logger.stepStart('读取文件内容');
        let fileContent;
        if (task.filePath) {
            // 简化日志：只在控制台输出文件路径
            console.log('从R2读取文件:', task.filePath);
            // 从R2读取原始文件
            const fileObject = await r2Storage.getFile(task.filePath);
            if (!fileObject) {
                throw new Error('文件内容不存在');
            }
            
            // 根据文件类型处理内容
            const ext = task.filename.toLowerCase().split('.').pop();
            console.log('文件类型:', ext);
            
            if (ext === 'txt') {
                // 对于文本文件，转换为字符串
                fileContent = await fileObject.text();
                console.log('文本文件读取完成，长度:', fileContent.length);
            } else {
                // 对于其他文件类型（如EPUB），保持ArrayBuffer
                fileContent = await fileObject.arrayBuffer();
                console.log('二进制文件读取完成，大小:', fileContent.byteLength);
            }
        } else {
            console.log('从KV存储读取文件（兼容模式）');
            // 兼容旧的KV存储方式
            const fileKey = `file:${taskId}`;
            fileContent = await env.KV.get(fileKey);
            if (!fileContent) {
                throw new Error('文件内容不存在');
            }
        }
        await logger.stepComplete('读取文件内容', Date.now() - startTime);

        // 初始化TTS处理器
        await logger.stepStart('初始化TTS处理器');
        const ttsProcessor = new TTSProcessor();
        await logger.stepComplete('初始化TTS处理器');
        
        // 提取文本内容
        await logger.stepStart('提取文本内容');
        const extractStartTime = Date.now();
        const text = await ttsProcessor.extractTextFromContent(fileContent, task.filename);
        
        if (!text || text.trim().length === 0) {
            throw new Error('无法从文件中提取有效文本');
        }

        const extractDuration = Date.now() - extractStartTime;
        await logger.stepComplete('提取文本内容', extractDuration, {
            textLength: text.length,
            extractionTime: extractDuration
        });

        console.log(`开始处理任务 ${taskId}, 文本长度: ${text.length} 字符`);
        await logger.info('文本提取成功', { 
            textLength: text.length,
            estimatedDuration: Math.round(text.length / 5) + '秒'
        });

        // 保存提取的文本到R2
        await logger.stepStart('保存提取的文本');
        await r2Storage.saveExtractedText(task.email, taskId, text);
        await logger.stepComplete('保存提取的文本');

        // 更新进度
        task.progress = 10;
        await env.KV.put(taskKey, JSON.stringify(task));
        await logger.progress(10, '文本提取完成，开始音频转换');

        // 创建进度更新回调
        const progressCallback = async (progress, stage) => {
            const currentTask = await env.KV.get(taskKey, { type: 'json' });
            if (currentTask) {
                currentTask.progress = Math.max(progress, 10); // 确保进度不会倒退
                currentTask.currentStage = stage || '音频转换中';
                currentTask.lastProgressUpdate = new Date().toISOString();
                await env.KV.put(taskKey, JSON.stringify(currentTask));
                await logger.progress(currentTask.progress, `音频转换进度更新: ${currentTask.progress}% - ${currentTask.currentStage}`);
            }
        };

        // 使用外部API进行文本转语音处理
        await logger.stepStart('调用外部音频转换API');
        const apiStartTime = Date.now();
        
        try {
            // 初始化API客户端
            const apiClient = new AudiobookApiClient();
            
            // 使用R2存储路径而不是公开URL
            await logger.stepStart('准备R2文件路径');
            let r2FilePath;
            
            if (task.filePath) {
                // 直接使用已有的R2路径
                r2FilePath = task.filePath;
                
                await logger.stepComplete('准备R2文件路径', null, {
                    r2FilePath: r2FilePath,
                    sourceType: 'existing_file_path'
                });
            } else {
                // 如果没有filePath，需要先将文本内容保存为文件
                const emailHash = task.email.replace('@', '_').replace('.', '_');
                const textFileName = `users/${emailHash}/tasks/${taskId}/original.txt`;
                const textBuffer = new TextEncoder().encode(text);
                await r2Storage.saveFile(textFileName, textBuffer, 'text/plain');
                r2FilePath = textFileName;
                
                await logger.stepComplete('准备R2文件路径', null, {
                    r2FilePath: r2FilePath,
                    textLength: text.length,
                    sourceType: 'generated_from_text'
                });
            }

            // 提交转换任务（使用R2路径）
            await logger.stepStart('提交音频转换任务（R2路径模式）');
            const convertOptions = {
                language: 'zh-CN',
                voiceName: 'zh-CN-XiaochenMultilingualNeural',
                speed: 1.0,
                pitch: 0,
                volume: 1.0
            };
            
            const taskInfo = await apiClient.submitConvertTaskWithR2Path(r2FilePath, convertOptions);
            await logger.stepComplete('提交音频转换任务（R2路径模式）', null, {
                externalTaskId: taskInfo.task_id,
                status: taskInfo.status,
                estimatedDuration: taskInfo.estimated_duration,
                r2FilePath: r2FilePath
            });

            // 保存外部任务ID
            task.externalTaskId = taskInfo.task_id;
            task.progress = 25;
            task.currentStage = '音频转换队列中';
            await env.KV.put(taskKey, JSON.stringify(task));

            // 等待转换完成
            await logger.stepStart('等待音频转换完成');
            const result = await apiClient.waitForTaskCompletion(
                taskInfo.task_id,
                (progress, stage) => {
                    // 将外部进度映射到25-95%范围
                    const mappedProgress = 25 + (progress * 0.7);
                    progressCallback(mappedProgress, stage || '音频转换中');
                },
                1800000, // 30分钟超时
                5000     // 5秒轮询间隔
            );
            
            const apiDuration = Date.now() - apiStartTime;
            await logger.stepComplete('等待音频转换完成', apiDuration, {
                externalTaskId: taskInfo.task_id,
                totalDuration: apiDuration,
                result: {
                    status: result.status,
                    audioFiles: Object.keys(result.audio_files || {}),
                    metadata: result.metadata
                }
            });

            // 从外部API结果中获取音频文件的R2存储路径
            if (!result.audio_files || !result.audio_files.mp3 || !result.audio_files.mp3.chapters || result.audio_files.mp3.chapters.length === 0) {
                throw new Error('外部API未返回有效的音频文件');
            }

            // 获取音频文件的R2存储路径（后端直接返回路径）
            await logger.stepStart('获取音频文件R2存储路径');
            const audioChapter = result.audio_files.mp3.chapters[0]; // 标准模式只有一个音频文件
            
            // 后端现在直接返回R2路径而不是完整URL
            let audioPath;
            if (audioChapter.r2_path) {
                // 后端直接提供R2路径
                audioPath = audioChapter.r2_path;
            } else if (audioChapter.url && audioChapter.url.includes('/users/')) {
                // 兼容模式：从URL中提取R2路径
                audioPath = audioChapter.url.split('/users/')[1];
                audioPath = 'users/' + audioPath;
            } else {
                // 使用默认路径格式（基于用户邮箱和任务ID）
                const emailHash = task.email.replace('@', '_').replace('.', '_');
                audioPath = `users/${emailHash}/audios/${taskId}/audio.mp3`;
            }
            
            await logger.stepComplete('获取音频文件R2存储路径', null, {
                audioPath,
                originalResponse: audioChapter,
                externalTaskId: taskInfo.task_id
            });

            // 验证音频文件是否存在于R2存储中
            await logger.stepStart('验证音频文件存在性');
            const audioExists = await r2Storage.fileExists(audioPath);
            if (!audioExists) {
                console.warn(`音频文件在R2存储中不存在: ${audioPath}，但外部API已完成转换`);
                // 可能需要等待一段时间让外部API完成文件上传
                await new Promise(resolve => setTimeout(resolve, 5000));
                const retryExists = await r2Storage.fileExists(audioPath);
                if (!retryExists) {
                    throw new Error(`音频文件在R2存储中不存在: ${audioPath}`);
                }
            }
            await logger.stepComplete('验证音频文件存在性', null, { audioPath, exists: true });

            // 任务成功完成后扣除积分
            if (!task.pointsCharged) {
                await logger.stepStart('扣除用户积分');
                
                try {
                    console.log(`开始扣除用户积分: ${task.email}, 50积分`);
                    await chargeUserPoints(task.email, 50, taskId, env);
                    
                    task.pointsCharged = true;
                    console.log('积分扣除成功，更新任务状态');
                    
                    await logger.stepComplete('扣除用户积分', null, { 
                        points: 50,
                        success: true,
                        userEmail: task.email
                    });
                } catch (pointsError) {
                    console.error('积分扣除失败，但音频已生成:', pointsError.message);
                    
                    // 积分扣除失败时的处理
                    await logger.stepFailed('扣除用户积分', pointsError, {
                        points: 50,
                        userEmail: task.email,
                        audioAlreadyGenerated: true,
                        errorType: pointsError.message.includes('超时') ? 'TIMEOUT' : 
                                   pointsError.message.includes('积分不足') ? 'INSUFFICIENT_POINTS' : 'UNKNOWN'
                    });
                    
                    // 标记积分扣除失败，但不影响音频文件
                    task.pointsCharged = false;
                    task.pointsError = pointsError.message;
                    task.pointsFailedAt = new Date().toISOString();
                    
                    // 记录警告但继续完成任务
                    console.warn('音频文件已生成，但积分扣除失败。任务将标记为成功，但积分问题需要人工处理。');
                    
                    // 注意：这里我们不抛出错误，让任务继续完成
                    // 因为音频已经生成，用户应该能获得结果
                }
            }

            // 更新任务状态为完成
            const totalDuration = Date.now() - startTime;
            task.status = 'SUCCESS';
            task.progress = 100;
            task.completedAt = new Date().toISOString();
            task.audioUrl = `/api/audio/${taskId}.mp3`;
            task.audioPath = audioPath; // 记录R2路径
            task.audioSize = fileContent.byteLength;
            task.wordCount = text.length;
            task.processingDuration = totalDuration;
            task.externalMetadata = result.metadata; // 保存外部API返回的元数据

            await env.KV.put(taskKey, JSON.stringify(task));

            // 创建任务完成标记文件（混合方案的关键部分）
            try {
                await r2Storage.createCompletionMarker(task.email, taskId, {
                    audioPath: audioPath,
                    audioSize: fileContent.byteLength,
                    wordCount: text.length,
                    processingDuration: totalDuration,
                    audioType: 'single-file'
                });
                console.log(`✅ 任务完成标记已创建: ${taskId}`);
            } catch (markerError) {
                console.error('创建完成标记失败:', markerError);
                // 不影响主流程，只记录错误
            }

            await logger.info('任务处理完成', {
                totalDuration,
                audioSize: fileContent.byteLength,
                wordCount: text.length,
                averageSpeed: Math.round(text.length / (totalDuration / 1000)) + '字符/秒',
                processingMethod: 'EXTERNAL_API',
                externalTaskId: taskInfo.task_id
            });

            console.log(`任务 ${taskId} 处理完成, 音频大小: ${fileContent.byteLength} bytes`);
            
        } catch (apiError) {
            await logger.stepFailed('调用外部音频转换API', apiError, {
                textLength: text.length,
                processingTime: Date.now() - apiStartTime,
                externalTaskId: task.externalTaskId
            });
            throw apiError;
        }

    } catch (error) {
        const totalDuration = Date.now() - startTime;
        await logger.error('任务处理失败', {
            error: error.message,
            stack: error.stack,
            totalDuration
        });
        
        console.error(`任务 ${taskId} 处理失败:`, error);
        
        // 更新任务状态为失败
        try {
            const task = await env.KV.get(taskKey, { type: 'json' });
            if (task) {
                task.status = 'FAILED';
                task.error = error.message;
                task.failedAt = new Date().toISOString();
                task.processingDuration = totalDuration;
                await env.KV.put(taskKey, JSON.stringify(task));
            }
        } catch (updateError) {
            console.error('更新失败状态时出错:', updateError);
            await logger.error('更新失败状态时出错', { error: updateError.message });
        }
        
        throw error;
    }
}

// 扣除用户积分并创建记录（增强版 - 带超时和重试）
async function chargeUserPoints(email, points, taskId, env) {
    const maxRetries = 3;
    const timeoutMs = 15000; // 15秒超时
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            console.log(`尝试扣除积分 (${attempt}/${maxRetries}): 用户${email}, 金额${points}`);
            
            // 设置超时控制
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('积分扣除操作超时')), timeoutMs);
            });
            
            const chargePromise = async () => {
                const userKey = `user:${email}`;
                
                // Step 1: 获取用户数据
                console.log('正在获取用户数据...');
                const userData = await env.KV.get(userKey, { type: 'json' });
                
                if (!userData) {
                    throw new Error('用户不存在');
                }

                if (userData.points < points) {
                    throw new Error(`积分不足，当前积分：${userData.points}，需要：${points}`);
                }

                // Step 2: 扣除积分
                console.log(`正在扣除积分: ${userData.points} - ${points} = ${userData.points - points}`);
                userData.points -= points;
                
                // 使用原子性操作更新用户数据
                await env.KV.put(userKey, JSON.stringify(userData));
                console.log('用户积分更新成功');

                // Step 3: 创建积分使用记录
                const pointsRecordId = `points_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                const pointsRecord = {
                    id: pointsRecordId,
                    email: email,
                    type: 'CONSUME',
                    amount: -points,
                    balance: userData.points,
                    description: '音频转换服务',
                    taskId: taskId,
                    createdAt: new Date().toISOString(),
                    attempt: attempt  // 记录是第几次尝试成功的
                };

                const recordKey = `points_record:${email}:${pointsRecordId}`;
                console.log('正在创建积分记录...');
                await env.KV.put(recordKey, JSON.stringify(pointsRecord));
                console.log('积分记录创建成功');

                return userData.points; // 返回剩余积分
            };
            
            // 执行带超时的积分扣除操作
            const remainingPoints = await Promise.race([chargePromise(), timeoutPromise]);
            
            console.log(`用户 ${email} 扣除 ${points} 积分成功，剩余 ${remainingPoints} 积分 (尝试${attempt}次)`);
            return; // 成功则直接返回
            
        } catch (error) {
            console.error(`积分扣除第${attempt}次尝试失败:`, error.message);
            
            // 如果是最后一次尝试，或者是业务逻辑错误（非超时/网络错误），直接抛出
            if (attempt === maxRetries || 
                error.message.includes('用户不存在') || 
                error.message.includes('积分不足')) {
                
                console.error(`积分扣除最终失败 (${attempt}/${maxRetries} 尝试):`, error.message);
                throw error;
            }
            
            // 计算重试延迟（指数退避）
            const retryDelay = Math.min(1000 * Math.pow(2, attempt - 1), 5000); // 1s, 2s, 4s, 最大5s
            console.warn(`积分扣除将在${retryDelay}ms后重试 (${attempt}/${maxRetries})`);
            
            await new Promise(resolve => setTimeout(resolve, retryDelay));
        }
    }
}

// 处理批量任务的定时任务
export async function onRequestGet({ env }) {
    try {
        console.log('开始批量处理待处理任务...');
        
        // 获取所有待处理的任务
        const taskList = await env.KV.list({ prefix: 'task:' });
        const pendingTasks = [];
        
        for (const key of taskList.keys) {
            const task = await env.KV.get(key.name, { type: 'json' });
            if (task && task.status === 'PENDING') {
                pendingTasks.push(task);
            }
        }
        
        console.log(`找到 ${pendingTasks.length} 个待处理任务`);
        
        // 处理每个任务
        const results = [];
        for (const task of pendingTasks.slice(0, 5)) { // 限制并发数量
            try {
                await processTask(task.id, env);
                results.push({ taskId: task.id, status: 'success' });
            } catch (error) {
                console.error(`任务 ${task.id} 处理失败:`, error);
                results.push({ taskId: task.id, status: 'failed', error: error.message });
            }
        }
        
        return Response.json({
            success: true,
            processed: results.length,
            results: results
        });
        
    } catch (error) {
        console.error('批量处理任务失败:', error);
        return Response.json({ error: '批量处理失败' }, { status: 500 });
    }
}

// 增强版任务处理逻辑（从process-enhanced-task.js复制）
async function processEnhancedTaskLogic(taskId, enableChapterSplit, env, logger, startTime) {
    const taskKey = `task:${taskId}`;
    
    try {
        // 获取任务信息
        const task = await env.KV.get(taskKey, { type: 'json' });
        if (!task) {
            throw new Error('任务不存在');
        }

        // 更新状态为处理中
        await logger.stepStart('更新任务状态');
        task.status = 'PROCESSING';
        task.progress = 5;
        task.processingType = enableChapterSplit ? 'MULTI_CHAPTER' : 'SINGLE_AUDIO';
        task.startedAt = new Date().toISOString();
        await env.KV.put(taskKey, JSON.stringify(task));
        await logger.progress(5, '增强版任务状态已更新为处理中');
        await logger.stepComplete('更新任务状态');

        // 初始化增强版存储管理器
        await logger.stepStart('初始化增强版存储管理器');
        const r2Storage = new EnhancedR2StorageManager(env.R2);
        await logger.stepComplete('初始化增强版存储管理器');

        // 从R2获取文件内容
        await logger.stepStart('读取文件内容');
        let fileContent;
        if (task.filePath) {
            const fileObject = await r2Storage.getFile(task.filePath);
            if (!fileObject) {
                throw new Error('文件内容不存在');
            }
            
            const ext = task.filename.toLowerCase().split('.').pop();
            if (ext === 'txt') {
                fileContent = await fileObject.text();
            } else {
                fileContent = await fileObject.arrayBuffer();
            }
        } else {
            const fileKey = `file:${taskId}`;
            fileContent = await env.KV.get(fileKey);
            if (!fileContent) {
                throw new Error('文件内容不存在');
            }
        }
        await logger.stepComplete('读取文件内容', Date.now() - startTime);

        // 初始化增强版TTS处理器
        await logger.stepStart('初始化增强版TTS处理器');
        const ttsProcessor = new EnhancedTTSProcessor();
        await logger.stepComplete('初始化增强版TTS处理器');

        // 创建进度更新回调
        const progressCallback = async (progress) => {
            const currentTask = await env.KV.get(taskKey, { type: 'json' });
            if (currentTask) {
                currentTask.progress = progress;
                currentTask.lastProgressUpdate = new Date().toISOString();
                await env.KV.put(taskKey, JSON.stringify(currentTask));
                await logger.progress(progress, `增强版音频转换进度更新: ${progress}%`);
            }
        };

        let audioChapters;
        const ext = task.filename.toLowerCase().split('.').pop();
        
        if (enableChapterSplit && ext === 'epub') {
            // EPUB文件 - 按章节处理
            await logger.stepStart('EPUB章节分析处理');
            await logger.info(`开始处理EPUB文件: ${task.filename}`, { 
                processingMode: 'CHAPTER_SPLIT',
                fileType: 'EPUB'
            });
            
            try {
                audioChapters = await ttsProcessor.processEpubWithChapters(
                    fileContent, 
                    progressCallback,
                    logger
                );
                
                await logger.stepComplete('EPUB章节分析处理', Date.now() - startTime, {
                    totalChapters: audioChapters.length,
                    totalWordCount: audioChapters.reduce((sum, ch) => sum + ch.wordCount, 0),
                    estimatedTotalDuration: Math.ceil(audioChapters.reduce((sum, ch) => sum + ch.duration, 0) / 60) + '分钟'
                });
                
            } catch (epubError) {
                await logger.stepFailed('EPUB章节分析处理', epubError);
                throw epubError;
            }
            
        } else {
            // 其他文件类型 - 按时长分割
            await logger.stepStart('文本内容提取与分析');
            await logger.info(`开始处理文本文件: ${task.filename}`, { 
                processingMode: enableChapterSplit ? 'TIME_SPLIT' : 'SINGLE_AUDIO',
                fileType: ext.toUpperCase()
            });
            
            try {
                const extractStartTime = Date.now();
                const text = await ttsProcessor.extractTextFromContent(fileContent, task.filename);
                
                if (!text || text.trim().length === 0) {
                    throw new Error('无法从文件中提取有效文本');
                }
                
                const extractDuration = Date.now() - extractStartTime;
                await logger.stepComplete('文本内容提取与分析', extractDuration, {
                    textLength: text.length,
                    extractionTime: extractDuration,
                    estimatedDuration: Math.round(text.length / 5) + '秒'
                });
                
                const bookTitle = task.filename.replace(/\.[^/.]+$/, '');
                
                if (enableChapterSplit) {
                    await logger.stepStart('文本时长分割处理');
                    audioChapters = await ttsProcessor.processTextWithTimeSplit(
                        text, 
                        bookTitle, 
                        progressCallback,
                        logger
                    );
                    await logger.stepComplete('文本时长分割处理', Date.now() - startTime, {
                        totalParts: audioChapters.length,
                        averagePartLength: Math.round(text.length / audioChapters.length)
                    });
                } else {
                    await logger.stepStart('单文件音频处理');
                    const audioBuffer = await ttsProcessor.processLongText(text, progressCallback, logger);
                    
                    audioChapters = [{
                        title: bookTitle,
                        audioBuffer: audioBuffer,
                        duration: Math.ceil(text.length / 5),
                        wordCount: text.length
                    }];
                    await logger.stepComplete('单文件音频处理', Date.now() - startTime, {
                        audioSize: audioBuffer.byteLength,
                        wordCount: text.length
                    });
                }
                
            } catch (textError) {
                await logger.stepFailed('文本处理', textError);
                throw textError;
            }
        }

        // 保存多章节音频到R2
        await logger.stepStart('保存多章节音频文件');
        const bookMetadata = {
            title: task.filename.replace(/\.[^/.]+$/, ''),
            originalFilename: task.filename,
            fileType: ext,
            processingType: task.processingType,
            createdAt: new Date().toISOString()
        };

        try {
            const saveResult = await r2Storage.saveMultiChapterAudio(
                task.email,
                taskId,
                audioChapters,
                bookMetadata
            );
            
            await logger.stepComplete('保存多章节音频文件', null, {
                totalChapters: audioChapters.length,
                totalSize: saveResult.totalSize,
                totalDuration: saveResult.totalDuration,
                averageChapterSize: Math.round(saveResult.totalSize / audioChapters.length)
            });
            
            // 任务成功完成后扣除积分
            if (!task.pointsCharged) {
                await logger.stepStart('扣除用户积分');
                const totalWordCount = audioChapters.reduce((sum, ch) => sum + ch.wordCount, 0);
                const pointsToCharge = Math.max(50, Math.ceil(totalWordCount / 1000) * 10);
                
                await chargeUserPoints(task.email, pointsToCharge, taskId, env);
                task.pointsCharged = true;
                task.pointsCharged_amount = pointsToCharge;
                
                await logger.stepComplete('扣除用户积分', null, { 
                    points: pointsToCharge,
                    wordCount: totalWordCount,
                    formula: `基础50 + ${Math.ceil(totalWordCount / 1000)} * 10`
                });
            }

            // 更新任务状态为完成
            const totalDuration = Date.now() - startTime;
            task.status = 'SUCCESS';
            task.progress = 100;
            task.completedAt = new Date().toISOString();
            task.audioType = 'MULTI_CHAPTER';
            task.totalChapters = audioChapters.length;
            task.totalDuration = saveResult.totalDuration;
            task.totalSize = saveResult.totalSize;
            task.totalWordCount = audioChapters.reduce((sum, ch) => sum + ch.wordCount, 0);
            task.playlistUrl = `/api/audio/playlist/${taskId}`;
            task.metadataUrl = `/api/audio/metadata/${taskId}`;
            task.processingDuration = totalDuration;

            await env.KV.put(taskKey, JSON.stringify(task));

            // 创建增强版任务完成标记文件
            try {
                await r2Storage.createCompletionMarker(task.email, taskId, {
                    audioType: 'multi-chapter',
                    totalChapters: audioChapters.length,
                    totalDuration: saveResult.totalDuration,
                    totalSize: saveResult.totalSize,
                    totalWordCount: task.totalWordCount,
                    processingDuration: totalDuration
                });
                console.log(`✅ 增强版任务完成标记已创建: ${taskId}`);
            } catch (markerError) {
                console.error('创建增强版完成标记失败:', markerError);
                // 不影响主流程，只记录错误
            }

            await logger.info('增强版任务处理完成', {
                totalDuration,
                totalChapters: audioChapters.length,
                totalSize: saveResult.totalSize,
                totalWordCount: task.totalWordCount,
                averageSpeed: Math.round(task.totalWordCount / (totalDuration / 1000)) + '字符/秒',
                processingType: task.processingType
            });

            console.log(`增强版任务 ${taskId} 处理完成:`);
            console.log(`- 总章节数: ${audioChapters.length}`);
            console.log(`- 总时长: ${Math.ceil(saveResult.totalDuration / 60)} 分钟`);
            console.log(`- 总大小: ${Math.ceil(saveResult.totalSize / 1024 / 1024)} MB`);
            
        } catch (saveError) {
            await logger.stepFailed('保存多章节音频文件', saveError);
            throw saveError;
        }

    } catch (error) {
        const totalDuration = Date.now() - startTime;
        await logger.error('增强版任务处理失败', {
            error: error.message,
            stack: error.stack,
            totalDuration,
            processingType: enableChapterSplit ? 'MULTI_CHAPTER' : 'SINGLE_AUDIO'
        });
        
        console.error(`增强版任务 ${taskId} 处理失败:`, error);
        
        // 更新任务状态为失败
        try {
            const task = await env.KV.get(taskKey, { type: 'json' });
            if (task) {
                task.status = 'FAILED';
                task.error = error.message;
                task.failedAt = new Date().toISOString();
                task.processingDuration = totalDuration;
                await env.KV.put(taskKey, JSON.stringify(task));
            }
        } catch (updateError) {
            console.error('更新失败状态时出错:', updateError);
            await logger.error('更新失败状态时出错', { error: updateError.message });
        }
        
        throw error;
    }
}

/**
 * R2架构的任务处理函数
 */
async function processR2Task(taskId, email, env) {
    const startTime = Date.now();
    const statusManager = new R2TaskStatusManager(env.R2, env);
    const r2Storage = new R2StorageManager(env.R2);

    try {
        // 获取任务状态
        const task = await statusManager.getTaskStatus(taskId, email);
        if (!task) {
            throw new Error('R2任务不存在');
        }

        if (task.status !== 'PENDING') {
            console.log(`R2任务 ${taskId} 状态为 ${task.status}，跳过处理`);
            return;
        }

        console.log(`📋 开始处理R2任务: ${taskId} - ${task.filename}`);

        // 标记任务开始处理
        await statusManager.markAsProcessing(taskId, email);

        // 从R2读取文件内容
        const fileObject = await r2Storage.getFile(task.filePath);
        if (!fileObject) {
            throw new Error('原始文件不存在');
        }

        let fileContent;
        const ext = task.filename.toLowerCase().split('.').pop();

        if (ext === 'txt') {
            fileContent = await fileObject.text();
        } else {
            fileContent = await fileObject.arrayBuffer();
        }

        console.log(`📄 文件读取完成: ${task.filename}`);

        // 提取文本内容
        const ttsProcessor = new TTSProcessor();
        const text = await ttsProcessor.extractTextFromContent(fileContent, task.filename);

        if (!text || text.trim().length === 0) {
            throw new Error('无法从文件中提取有效文本');
        }

        console.log(`📝 文本提取完成，字数: ${text.length}`);

        // 调用外部API进行音频转换
        const apiClient = new AudiobookApiClient();

        const convertOptions = {
            language: 'zh-CN',
            voiceName: 'zh-CN-XiaochenMultilingualNeural',
            speed: 1.0,
            pitch: 0,
            volume: 1.0
        };

        console.log(`🎵 提交音频转换任务...`);
        const taskInfo = await apiClient.submitConvertTaskWithR2Path(task.filePath, convertOptions);

        // 更新任务状态，保存外部任务ID
        await statusManager.updateTaskStatus(taskId, email, {
            externalTaskId: taskInfo.task_id,
            currentStage: '音频转换队列中'
        });

        console.log(`⏳ 等待音频转换完成，外部任务ID: ${taskInfo.task_id}`);

        // 等待转换完成
        const result = await apiClient.waitForTaskCompletion(
            taskInfo.task_id,
            null, // 不需要进度回调
            1800000, // 30分钟超时
            10000    // 10秒轮询间隔
        );

        if (result.status !== 'completed') {
            throw new Error(`音频转换失败: ${result.status}`);
        }

        console.log(`✅ 音频转换完成`);

        // 获取音频文件路径
        const audioChapter = result.audio_files.mp3.chapters[0];
        let audioPath;

        if (audioChapter.r2_path) {
            audioPath = audioChapter.r2_path;
        } else {
            // 使用默认路径格式
            const userIdResult = await statusManager.userIdResolver.resolveUserId(email, taskId);
            audioPath = `users/${userIdResult.userId}/audio/${taskId}.mp3`;
        }

        // 验证音频文件存在
        const audioExists = await r2Storage.fileExists(audioPath);
        if (!audioExists) {
            throw new Error(`音频文件不存在: ${audioPath}`);
        }

        // 扣除用户积分
        await chargeUserPoints(email, 5, taskId, env);

        // 标记任务完成
        const completionData = {
            audioPath: audioPath,
            metadata: {
                wordCount: text.length,
                duration: result.duration,
                isEnhanced: false,
                totalChapters: 1,
                processingDuration: Date.now() - startTime
            },
            urls: {
                audioUrl: `/api/audio/${taskId}.mp3`,
                downloadUrl: `/api/audio/${taskId}.mp3`
            }
        };

        await statusManager.markAsCompleted(taskId, email, completionData);

        const totalDuration = Date.now() - startTime;
        console.log(`🎉 R2任务处理完成: ${taskId}, 耗时: ${totalDuration}ms`);

    } catch (error) {
        console.error(`❌ R2任务处理失败 ${taskId}:`, error);

        // 标记任务失败
        try {
            await statusManager.markAsFailed(taskId, email, error, {
                processingDuration: Date.now() - startTime
            });
        } catch (updateError) {
            console.error('更新R2失败状态时出错:', updateError);
        }

        throw error;
    }
}