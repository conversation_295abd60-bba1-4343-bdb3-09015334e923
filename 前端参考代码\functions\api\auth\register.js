// 用户注册 API
export async function onRequestPost({ request, env }) {
    try {
        const { email, password } = await request.json();
        
        if (!email || !password) {
            return Response.json({ error: '邮箱和密码不能为空' }, { status: 400 });
        }

        // 验证邮箱格式
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return Response.json({ error: '邮箱格式不正确' }, { status: 400 });
        }

        // 验证密码长度
        if (password.length < 6) {
            return Response.json({ error: '密码至少需要6位' }, { status: 400 });
        }

        // 检查用户是否已存在
        const userKey = `user:${email}`;
        const existingUser = await env.KV.get(userKey);
        
        if (existingUser) {
            return Response.json({ error: '该邮箱已被注册' }, { status: 409 });
        }

        // 创建新用户
        const newUser = {
            email,
            password, // 实际项目中应该加密存储
            points: 100, // 注册赠送100积分
            createdAt: new Date().toISOString(),
            lastLogin: new Date().toISOString(),
            lastCheckin: null
        };

        // 保存用户数据
        await env.KV.put(userKey, JSON.stringify(newUser));

        // 创建注册积分奖励记录
        const pointsRecordId = `points_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const pointsRecord = {
            id: pointsRecordId,
            email: email,
            type: 'EARN', // 获得
            amount: 100,
            balance: 100,
            description: '新用户注册奖励',
            createdAt: new Date().toISOString()
        };

        const recordKey = `points_record:${email}:${pointsRecordId}`;
        await env.KV.put(recordKey, JSON.stringify(pointsRecord));

        // 返回用户信息（不包含密码）
        const { password: _, ...userInfo } = newUser;
        
        return Response.json({
            success: true,
            user: userInfo,
            message: '注册成功，获得100积分'
        });

    } catch (error) {
        console.error('注册错误:', error);
        return Response.json({ error: '服务器内部错误' }, { status: 500 });
    }
} 