// 用户资料 API - 获取用户最新信息
export async function onRequestPost({ request, env }) {
    try {
        const { email } = await request.json();
        
        if (!email) {
            return Response.json({ error: '邮箱不能为空' }, { status: 400 });
        }

        // 从 KV 获取用户数据
        const userKey = `user:${email}`;
        const userData = await env.KV.get(userKey, { type: 'json' });
        
        if (!userData) {
            return Response.json({ error: '用户不存在' }, { status: 404 });
        }

        // 返回用户信息（不包含密码）
        const { password, ...userInfo } = userData;
        
        return Response.json({
            success: true,
            user: userInfo
        });

    } catch (error) {
        console.error('获取用户资料错误:', error);
        return Response.json({ error: '服务器内部错误' }, { status: 500 });
    }
} 