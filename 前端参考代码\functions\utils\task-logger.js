// 任务日志记录工具
import { LoggingConfig, shouldLogToKV, isConsoleOnly, cleanLogData } from '../config/logging-config.js';

export class TaskLogger {
    constructor(taskId, env) {
        this.taskId = taskId;
        this.env = env;
        this.logKey = `task_log:${taskId}`;
        this.config = LoggingConfig;
    }

    /**
     * 记录日志 - 优化版本，使用配置文件
     * @param {string} level - 日志级别: INFO, WARN, ERROR, DEBUG
     * @param {string} message - 日志消息
     * @param {Object} data - 附加数据
     * @param {string} context - 上下文标识（tts, chapter, progress等）
     */
    async log(level, message, data = {}, context = '') {
        // 检查是否只在控制台输出
        if (isConsoleOnly(level)) {
            console.log(`[${level}] ${this.taskId}: ${message}`, data);
            return;
        }

        // 检查是否应该记录到KV
        if (!shouldLogToKV(level)) {
            return;
        }

        const timestamp = new Date().toISOString();
        const logEntry = {
            timestamp,
            level,
            message,
            // 使用配置清理数据
            data: cleanLogData(data, level, context),
            taskId: this.taskId
        };

        try {
            // 获取现有日志
            const existingLogs = await this.env.KV.get(this.logKey, { type: 'json' }) || [];
            
            // 添加新日志
            existingLogs.push(logEntry);
            
            // 使用配置的最大日志数
            if (existingLogs.length > this.config.storage.maxLogs) {
                existingLogs.splice(0, existingLogs.length - this.config.storage.maxLogs);
            }
            
            // 保存日志
            await this.env.KV.put(this.logKey, JSON.stringify(existingLogs));
            
            // 同时输出到控制台
            console.log(`[${level}] ${this.taskId}: ${message}`, cleanLogData(data, level, context));
            
        } catch (error) {
            console.error('记录日志失败:', error);
        }
    }

    // 简化现有方法，移除复杂的数据过滤逻辑
    simplifyLogData(data, level) {
        // 现在使用配置文件中的cleanLogData函数
        return cleanLogData(data, level);
    }

    /**
     * 记录信息日志
     */
    async info(message, data = {}) {
        await this.log('INFO', message, data);
    }

    /**
     * 记录警告日志
     */
    async warn(message, data = {}) {
        await this.log('WARN', message, data);
    }

    /**
     * 记录错误日志
     */
    async error(message, data = {}) {
        await this.log('ERROR', message, data);
    }

    /**
     * 记录调试日志
     */
    async debug(message, data = {}) {
        await this.log('DEBUG', message, data);
    }

    /**
     * 记录进度更新
     */
    async progress(progress, message, data = {}) {
        await this.log('PROGRESS', message, { 
            progress, 
            ...data 
        });
    }

    /**
     * 记录步骤开始
     */
    async stepStart(stepName, description = '') {
        await this.log('STEP_START', `开始步骤: ${stepName}`, { 
            stepName, 
            description 
        });
    }

    /**
     * 记录步骤完成
     */
    async stepComplete(stepName, duration = null, data = {}) {
        await this.log('STEP_COMPLETE', `完成步骤: ${stepName}`, { 
            stepName, 
            duration,
            ...data 
        });
    }

    /**
     * 记录步骤失败
     */
    async stepFailed(stepName, error, data = {}) {
        await this.log('STEP_FAILED', `步骤失败: ${stepName}`, { 
            stepName, 
            error: error.message,
            stack: error.stack,
            ...data 
        });
    }

    /**
     * 获取所有日志
     */
    async getLogs() {
        try {
            return await this.env.KV.get(this.logKey, { type: 'json' }) || [];
        } catch (error) {
            console.error('获取日志失败:', error);
            return [];
        }
    }

    /**
     * 清除日志
     */
    async clearLogs() {
        try {
            await this.env.KV.delete(this.logKey);
        } catch (error) {
            console.error('清除日志失败:', error);
        }
    }

    /**
     * 获取最近的日志
     */
    async getRecentLogs(count = 50) {
        const logs = await this.getLogs();
        return logs.slice(-count);
    }

    /**
     * 按级别过滤日志
     */
    async getLogsByLevel(level) {
        const logs = await this.getLogs();
        return logs.filter(log => log.level === level);
    }

    /**
     * 记录关键信息日志 - 新增方法，专门记录最重要的信息
     */
    async critical(message, data = {}) {
        await this.log('CRITICAL', message, data);
    }

    /**
     * 记录TTS API调用结果 - 简化版本
     */
    async ttsResult(success, data = {}) {
        if (success) {
            // 只在重试情况下记录成功信息
            if (data.retryAttempt && data.retryAttempt > 1) {
                await this.log('INFO', 'TTS转换成功（重试后）', data, 'tts');
            }
        } else {
            await this.log('ERROR', 'TTS转换失败', data, 'tts');
        }
    }

    /**
     * 记录章节处理结果 - 简化版本
     */
    async chapterResult(chapterIndex, success, data = {}) {
        if (success) {
            await this.log('INFO', `章节 ${chapterIndex} 处理完成`, data, 'chapter');
        } else {
            await this.log('ERROR', `章节 ${chapterIndex} 处理失败`, data, 'chapter');
        }
    }

    /**
     * 记录关键业务指标
     */
    async metric(metricName, data = {}) {
        if (this.config.criticalMetrics.includes(metricName)) {
            await this.log('INFO', `指标: ${metricName}`, data, 'metric');
        }
    }
} 