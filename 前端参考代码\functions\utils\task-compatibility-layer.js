/**
 * 任务兼容层
 * 处理R2和KV架构之间的兼容性问题
 */

import { R2TaskStatusManager } from './r2-task-status-manager.js';

export class TaskCompatibilityLayer {
    constructor(env) {
        this.env = env;
        this.r2StatusManager = new R2TaskStatusManager(env.R2, env);
    }

    /**
     * 智能获取任务状态 - 优先R2，回退KV
     */
    async getTaskStatus(taskId, email) {
        try {
            // 首先尝试从R2获取
            const r2Status = await this.r2StatusManager.getTaskStatus(taskId, email);
            if (r2Status) {
                return {
                    status: r2Status,
                    source: 'R2',
                    version: r2Status.version || '1.0'
                };
            }

            // 如果R2中没有，尝试从KV获取
            const kvStatus = await this.getKvTaskStatus(taskId, email);
            if (kvStatus) {
                // 将KV数据转换为R2格式
                const convertedStatus = R2TaskStatusManager.mapKvToR2Status(kvStatus);
                return {
                    status: convertedStatus,
                    source: 'KV',
                    version: 'legacy'
                };
            }

            return null;
        } catch (error) {
            console.error(`获取任务状态失败 ${taskId}:`, error);
            return null;
        }
    }

    /**
     * 从KV获取任务状态
     */
    async getKvTaskStatus(taskId, email) {
        try {
            const taskKey = `task:${taskId}`;
            const task = await this.env.KV.get(taskKey, { type: 'json' });
            
            if (task && task.email === email) {
                return task;
            }
            
            return null;
        } catch (error) {
            console.error(`从KV获取任务状态失败 ${taskId}:`, error);
            return null;
        }
    }

    /**
     * 智能更新任务状态 - 根据任务来源选择存储方式
     */
    async updateTaskStatus(taskId, email, updates) {
        try {
            // 检查任务是否存在于R2
            const r2Exists = await this.r2StatusManager.taskExists(taskId, email);
            
            if (r2Exists) {
                // 更新R2状态
                return await this.r2StatusManager.updateTaskStatus(taskId, email, updates);
            }

            // 检查任务是否存在于KV
            const kvTask = await this.getKvTaskStatus(taskId, email);
            if (kvTask) {
                // 更新KV状态
                const updatedTask = { ...kvTask, ...updates };
                const taskKey = `task:${taskId}`;
                await this.env.KV.put(taskKey, JSON.stringify(updatedTask));
                
                return R2TaskStatusManager.mapKvToR2Status(updatedTask);
            }

            throw new Error('任务不存在');
        } catch (error) {
            console.error(`更新任务状态失败 ${taskId}:`, error);
            throw error;
        }
    }

    /**
     * 获取用户所有任务 - 合并R2和KV数据
     */
    async getUserTasks(email, options = {}) {
        const { limit = 100, status = null } = options;
        
        try {
            // 并行获取R2和KV任务
            const [r2Tasks, kvTasks] = await Promise.all([
                this.r2StatusManager.getUserTasks(email, { limit, status }).catch(error => {
                    console.warn('获取R2任务失败:', error);
                    return [];
                }),
                this.getKvUserTasks(email, { limit, status }).catch(error => {
                    console.warn('获取KV任务失败:', error);
                    return [];
                })
            ]);

            // 合并和去重
            const allTasks = this.mergeAndDeduplicateTasks(r2Tasks, kvTasks);
            
            // 按创建时间排序
            allTasks.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
            
            // 限制数量
            const limitedTasks = allTasks.slice(0, limit);

            return {
                tasks: limitedTasks,
                statistics: {
                    total: limitedTasks.length,
                    r2Count: r2Tasks.length,
                    kvCount: kvTasks.length,
                    statusBreakdown: this.getStatusBreakdown(limitedTasks)
                }
            };
        } catch (error) {
            console.error('获取用户任务失败:', error);
            throw error;
        }
    }

    /**
     * 从KV获取用户任务
     */
    async getKvUserTasks(email, options = {}) {
        const { limit = 100, status = null } = options;
        
        try {
            const { list } = await this.env.KV.list({ prefix: 'task:' });
            const tasks = [];

            for (const key of list.keys) {
                if (tasks.length >= limit * 2) break; // 预留空间

                try {
                    const task = await this.env.KV.get(key.name, { type: 'json' });
                    if (task && task.email === email) {
                        // 跳过已经迁移到R2的任务（避免重复）
                        if (task.useR2Status) {
                            continue;
                        }
                        
                        const r2Status = R2TaskStatusManager.mapKvToR2Status(task);
                        if (!status || r2Status.status === status) {
                            r2Status.dataSource = 'KV';
                            tasks.push(r2Status);
                        }
                    }
                } catch (error) {
                    console.warn(`跳过无效任务: ${key.name}`, error);
                }
            }

            return tasks.slice(0, limit);
        } catch (error) {
            console.error('从KV获取用户任务失败:', error);
            return [];
        }
    }

    /**
     * 合并和去重任务
     */
    mergeAndDeduplicateTasks(r2Tasks, kvTasks) {
        const taskMap = new Map();
        
        // 添加R2任务（优先级更高）
        r2Tasks.forEach(task => {
            taskMap.set(task.id, { ...task, dataSource: 'R2' });
        });
        
        // 添加KV任务（如果不存在相同ID的R2任务）
        kvTasks.forEach(task => {
            if (!taskMap.has(task.id)) {
                taskMap.set(task.id, { ...task, dataSource: 'KV' });
            }
        });
        
        return Array.from(taskMap.values());
    }

    /**
     * 获取状态统计
     */
    getStatusBreakdown(tasks) {
        const breakdown = {
            pending: 0,
            completed: 0,
            failed: 0
        };
        
        tasks.forEach(task => {
            if (breakdown.hasOwnProperty(task.status)) {
                breakdown[task.status]++;
            }
        });
        
        return breakdown;
    }

    /**
     * 检测任务架构类型
     */
    async detectTaskArchitecture(taskId, email) {
        try {
            // 检查R2
            const r2Exists = await this.r2StatusManager.taskExists(taskId, email);
            if (r2Exists) {
                return 'R2';
            }

            // 检查KV
            const kvTask = await this.getKvTaskStatus(taskId, email);
            if (kvTask) {
                return kvTask.useR2Status ? 'R2_MIGRATED' : 'KV';
            }

            return 'NOT_FOUND';
        } catch (error) {
            console.error(`检测任务架构失败 ${taskId}:`, error);
            return 'ERROR';
        }
    }

    /**
     * 迁移KV任务到R2（可选功能）
     */
    async migrateKvTaskToR2(taskId, email) {
        try {
            // 获取KV任务
            const kvTask = await this.getKvTaskStatus(taskId, email);
            if (!kvTask) {
                throw new Error('KV任务不存在');
            }

            // 检查是否已经迁移
            if (kvTask.useR2Status) {
                console.log(`任务 ${taskId} 已标记为使用R2状态`);
                return false;
            }

            // 转换为R2格式
            const r2Status = R2TaskStatusManager.mapKvToR2Status(kvTask);

            // 创建R2状态文件
            await this.r2StatusManager.createTaskStatus({
                id: r2Status.id,
                email: r2Status.email,
                filename: r2Status.filename,
                type: r2Status.type,
                fileSize: r2Status.fileSize,
                filePath: r2Status.filePath
            });

            // 如果任务已完成，更新完整状态
            if (r2Status.status !== 'pending') {
                await this.r2StatusManager.updateTaskStatus(taskId, email, {
                    status: r2Status.status,
                    startedAt: r2Status.startedAt,
                    completedAt: r2Status.completedAt,
                    audioPath: r2Status.audioPath,
                    audioSize: r2Status.audioSize,
                    error: r2Status.error,
                    metadata: r2Status.metadata,
                    urls: r2Status.urls
                });
            }

            // 标记KV任务为已迁移
            kvTask.useR2Status = true;
            kvTask.migratedAt = new Date().toISOString();
            const taskKey = `task:${taskId}`;
            await this.env.KV.put(taskKey, JSON.stringify(kvTask));

            console.log(`✅ 任务 ${taskId} 已成功迁移到R2`);
            return true;
        } catch (error) {
            console.error(`迁移任务到R2失败 ${taskId}:`, error);
            throw error;
        }
    }

    /**
     * 批量迁移用户的KV任务到R2
     */
    async batchMigrateUserTasks(email, options = {}) {
        const { limit = 50, dryRun = false } = options;
        
        try {
            // 获取用户的KV任务
            const kvTasks = await this.getKvUserTasks(email, { limit });
            
            if (kvTasks.length === 0) {
                return {
                    success: true,
                    message: '没有需要迁移的KV任务',
                    migrated: 0,
                    total: 0
                };
            }

            let migratedCount = 0;
            const errors = [];

            for (const task of kvTasks) {
                try {
                    if (dryRun) {
                        console.log(`[DRY RUN] 将迁移任务: ${task.id} - ${task.filename}`);
                    } else {
                        const migrated = await this.migrateKvTaskToR2(task.id, email);
                        if (migrated) {
                            migratedCount++;
                        }
                    }
                } catch (error) {
                    errors.push({
                        taskId: task.id,
                        error: error.message
                    });
                }
            }

            return {
                success: true,
                message: dryRun 
                    ? `预计迁移 ${kvTasks.length} 个任务`
                    : `成功迁移 ${migratedCount} 个任务`,
                migrated: migratedCount,
                total: kvTasks.length,
                errors: errors,
                dryRun: dryRun
            };
        } catch (error) {
            console.error('批量迁移任务失败:', error);
            throw error;
        }
    }
}
