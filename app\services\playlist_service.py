"""
播放列表生成服务
根据EPUB章节结构生成音频播放列表，支持M3U8和JSON格式
基于原始参考代码完整迁移，保持所有算法逻辑不变
"""

import json
import os
import tempfile
from typing import List, Dict
from datetime import datetime


from app.services.epub_parser import Chapter, EpubMetadata
from app.core.logging import get_logger

logger = get_logger(__name__)


class PlaylistService:
    """播放列表生成服务"""

    def __init__(self):
        pass

    def generate_json_playlist(
        self,
        metadata: EpubMetadata,
        chapters: List[Chapter],
        audio_urls: Dict[str, str],
        task_id: str,
    ) -> Dict:
        """生成JSON格式播放列表"""
        try:
            playlist = {
                "version": "1.0",
                "type": "audiobook",
                "metadata": {
                    "title": metadata.title,
                    "author": metadata.author,
                    "language": metadata.language,
                    "publisher": metadata.publisher,
                    "description": metadata.description,
                    "identifier": metadata.identifier,
                    "total_chapters": len(chapters),
                    "total_words": metadata.total_words,
                    "created_at": datetime.now().isoformat(),
                    "task_id": task_id,
                },
                "chapters": [],
                "statistics": {
                    "total_duration": "0分0秒",  # TODO: 计算总时长
                    "total_size": 0,
                    "average_chapter_length": 0,
                },
            }

            total_words = 0

            for chapter in chapters:
                if chapter.id in audio_urls:
                    chapter_info = {
                        "id": chapter.id,
                        "title": chapter.title,
                        "url": audio_urls[chapter.id],
                        "order": chapter.order,
                        "level": chapter.level,
                        "parent_id": chapter.parent_id,
                        "word_count": chapter.word_count,
                        "duration": "未知",  # TODO: 从音频文件获取时长
                        "file_size": 0,  # TODO: 从存储服务获取文件大小
                        "content_preview": self._get_content_preview(chapter.content),
                    }

                    playlist["chapters"].append(chapter_info)
                    total_words += chapter.word_count

            # 更新统计信息
            if playlist["chapters"]:
                playlist["statistics"]["average_chapter_length"] = total_words // len(
                    playlist["chapters"]
                )

            logger.info(f"JSON播放列表生成完成: {len(playlist['chapters'])}个章节")
            return playlist

        except Exception as e:
            logger.error(f"生成JSON播放列表失败: {str(e)}")
            return {}

    def generate_m3u8_playlist(
        self,
        metadata: EpubMetadata,
        chapters: List[Chapter],
        audio_urls: Dict[str, str],
    ) -> str:
        """生成M3U8格式播放列表"""
        try:
            m3u8_content = ["#EXTM3U"]
            m3u8_content.append("#EXT-X-VERSION:3")
            m3u8_content.append("#EXT-X-PLAYLIST-TYPE:VOD")

            # 添加元数据
            m3u8_content.append(f"#EXT-X-TITLE:{metadata.title}")
            if metadata.author:
                m3u8_content.append(f"#EXT-X-AUTHOR:{metadata.author}")

            # 添加章节信息
            for chapter in chapters:
                if chapter.id in audio_urls:
                    # 估算时长 (基于字数，假设每分钟200字)
                    estimated_duration = max(1, chapter.word_count // 200 * 60)

                    m3u8_content.append(f"#EXTINF:{estimated_duration},{chapter.title}")
                    m3u8_content.append(audio_urls[chapter.id])

            m3u8_content.append("#EXT-X-ENDLIST")

            playlist_content = "\n".join(m3u8_content)
            logger.info(
                f"M3U8播放列表生成完成: {len([c for c in chapters if c.id in audio_urls])}个章节"
            )

            return playlist_content

        except Exception as e:
            logger.error(f"生成M3U8播放列表失败: {str(e)}")
            return ""

    def generate_hierarchical_playlist(
        self,
        metadata: EpubMetadata,
        chapters: List[Chapter],
        audio_urls: Dict[str, str],
    ) -> Dict:
        """生成层级结构播放列表"""
        try:
            # 构建章节层级关系
            chapter_map = {}
            root_chapters = []

            # 创建章节映射
            for chapter in chapters:
                if chapter.id in audio_urls:
                    chapter_data = {
                        "id": chapter.id,
                        "title": chapter.title,
                        "url": audio_urls[chapter.id],
                        "order": chapter.order,
                        "level": chapter.level,
                        "word_count": chapter.word_count,
                        "children": [],
                    }
                    chapter_map[chapter.id] = chapter_data

            # 构建层级关系
            for chapter in chapters:
                if chapter.id in chapter_map:
                    chapter_data = chapter_map[chapter.id]

                    if chapter.parent_id and chapter.parent_id in chapter_map:
                        # 添加到父章节
                        chapter_map[chapter.parent_id]["children"].append(chapter_data)
                    else:
                        # 根级章节
                        root_chapters.append(chapter_data)

            hierarchical_playlist = {
                "title": metadata.title,
                "author": metadata.author,
                "structure": "hierarchical",
                "chapters": root_chapters,
                "metadata": {
                    "total_chapters": len([c for c in chapters if c.id in audio_urls]),
                    "max_depth": self._calculate_max_depth(root_chapters),
                    "created_at": datetime.now().isoformat(),
                },
            }

            logger.info(f"层级播放列表生成完成: {len(root_chapters)}个根章节")
            return hierarchical_playlist

        except Exception as e:
            logger.error(f"生成层级播放列表失败: {str(e)}")
            return {}

    async def save_playlist_to_file(
        self, playlist_data: Dict, file_path: str, format_type: str = "json"
    ) -> bool:
        """保存播放列表到文件"""
        try:
            if format_type.lower() == "json":
                with open(file_path, "w", encoding="utf-8") as f:
                    json.dump(playlist_data, f, ensure_ascii=False, indent=2)
            elif format_type.lower() == "m3u8":
                # playlist_data应该是字符串内容
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(playlist_data)
            else:
                raise ValueError(f"不支持的格式: {format_type}")

            logger.info(f"播放列表保存成功: {file_path}")
            return True

        except Exception as e:
            logger.error(f"保存播放列表失败: {str(e)}")
            return False

    def create_playlist_bundle(
        self,
        metadata: EpubMetadata,
        chapters: List[Chapter],
        audio_urls: Dict[str, str],
        task_id: str,
    ) -> Dict[str, str]:
        """创建完整的播放列表包（包含多种格式）"""
        try:
            bundle = {}

            # 生成JSON播放列表
            json_playlist = self.generate_json_playlist(
                metadata, chapters, audio_urls, task_id
            )
            if json_playlist:
                json_file = tempfile.NamedTemporaryFile(
                    mode="w", suffix=".json", delete=False, encoding="utf-8"
                )
                json.dump(json_playlist, json_file, ensure_ascii=False, indent=2)
                json_file.close()
                bundle["json"] = json_file.name

            # 生成M3U8播放列表
            m3u8_content = self.generate_m3u8_playlist(metadata, chapters, audio_urls)
            if m3u8_content:
                m3u8_file = tempfile.NamedTemporaryFile(
                    mode="w", suffix=".m3u8", delete=False, encoding="utf-8"
                )
                m3u8_file.write(m3u8_content)
                m3u8_file.close()
                bundle["m3u8"] = m3u8_file.name

            # 跳过层级播放列表生成（节省存储空间）
            # hierarchical_playlist = self.generate_hierarchical_playlist(
            #     metadata, chapters, audio_urls
            # )
            # if hierarchical_playlist:
            #     hierarchical_file = tempfile.NamedTemporaryFile(
            #         mode="w", suffix=".json", delete=False, encoding="utf-8"
            #     )
            #     json.dump(
            #         hierarchical_playlist,
            #         hierarchical_file,
            #         ensure_ascii=False,
            #         indent=2,
            #     )
            #     hierarchical_file.close()
            #     bundle["hierarchical"] = hierarchical_file.name

            logger.info(f"播放列表包创建完成: {len(bundle)}种格式")
            return bundle

        except Exception as e:
            logger.error(f"创建播放列表包失败: {str(e)}")
            return {}

    def _get_content_preview(self, content: str, max_length: int = 100) -> str:
        """获取内容预览"""
        if len(content) <= max_length:
            return content
        return content[:max_length] + "..."

    def _calculate_max_depth(self, chapters: List[Dict], current_depth: int = 1) -> int:
        """计算章节层级的最大深度"""
        max_depth = current_depth

        for chapter in chapters:
            if chapter.get("children"):
                child_depth = self._calculate_max_depth(
                    chapter["children"], current_depth + 1
                )
                max_depth = max(max_depth, child_depth)

        return max_depth

    def cleanup_temp_files(self, file_paths: List[str]):
        """清理临时文件"""
        for file_path in file_paths:
            try:
                if os.path.exists(file_path):
                    os.unlink(file_path)
                    logger.debug(f"清理临时播放列表文件: {file_path}")
            except Exception as e:
                logger.warning(f"清理临时文件失败: {str(e)}")
