// 网页内容提取模块
export class WebScraper {
    constructor() {
        this.maxContentLength = 100000; // 最大内容长度
        this.timeout = 10000; // 10秒超时
    }

    /**
     * 从URL获取网页内容
     * @param {string} url - 网页URL
     * @returns {Promise<{title: string, content: string}>} - 提取的标题和内容
     */
    async extractContentFromUrl(url) {
        try {
            console.log(`开始抓取网页: ${url}`);
            
            // 验证URL
            const urlObj = new URL(url);
            if (!['http:', 'https:'].includes(urlObj.protocol)) {
                throw new Error('不支持的协议');
            }

            // 获取网页内容
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'DNT': '1',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1'
                },
                signal: AbortSignal.timeout(this.timeout)
            });

            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
            }

            const contentType = response.headers.get('content-type') || '';
            if (!contentType.includes('text/html')) {
                throw new Error('不是HTML内容');
            }

            const html = await response.text();
            
            // 提取内容
            const extracted = this.parseHtmlContent(html);
            
            console.log(`网页抓取完成: 标题=${extracted.title}, 内容长度=${extracted.content.length}`);
            
            return extracted;

        } catch (error) {
            console.error('网页抓取失败:', error);
            throw error;
        }
    }

    /**
     * 解析HTML内容
     * @param {string} html - HTML内容
     * @returns {Object} - 解析后的标题和内容
     */
    parseHtmlContent(html) {
        try {
            // 提取标题
            const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
            let title = titleMatch ? titleMatch[1].trim() : '无标题';
            
            // 解码HTML实体
            title = this.decodeHtmlEntities(title);

            // 移除脚本和样式标签
            let cleanHtml = html
                .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
                .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '')
                .replace(/<nav\b[^<]*(?:(?!<\/nav>)<[^<]*)*<\/nav>/gi, '')
                .replace(/<footer\b[^<]*(?:(?!<\/footer>)<[^<]*)*<\/footer>/gi, '')
                .replace(/<header\b[^<]*(?:(?!<\/header>)<[^<]*)*<\/header>/gi, '')
                .replace(/<aside\b[^<]*(?:(?!<\/aside>)<[^<]*)*<\/aside>/gi, '');

            // 尝试提取主要内容区域
            const contentSelectors = [
                /<main\b[^>]*>(.*?)<\/main>/gis,
                /<article\b[^>]*>(.*?)<\/article>/gis,
                /<div[^>]*class[^>]*content[^>]*>(.*?)<\/div>/gis,
                /<div[^>]*class[^>]*main[^>]*>(.*?)<\/div>/gis,
                /<div[^>]*id[^>]*content[^>]*>(.*?)<\/div>/gis,
                /<div[^>]*id[^>]*main[^>]*>(.*?)<\/div>/gis
            ];

            let mainContent = '';
            for (const selector of contentSelectors) {
                const matches = [...cleanHtml.matchAll(selector)];
                if (matches.length > 0) {
                    mainContent = matches.map(match => match[1]).join('\n');
                    break;
                }
            }

            // 如果没有找到主要内容区域，提取body内容
            if (!mainContent) {
                const bodyMatch = cleanHtml.match(/<body[^>]*>(.*?)<\/body>/gis);
                mainContent = bodyMatch ? bodyMatch[0] : cleanHtml;
            }

            // 移除所有HTML标签
            let textContent = mainContent.replace(/<[^>]+>/g, ' ');
            
            // 解码HTML实体
            textContent = this.decodeHtmlEntities(textContent);
            
            // 清理文本
            textContent = textContent
                .replace(/\s+/g, ' ')
                .replace(/\n\s*\n/g, '\n')
                .trim();

            // 限制内容长度
            if (textContent.length > this.maxContentLength) {
                textContent = textContent.substring(0, this.maxContentLength) + '...';
            }

            // 验证内容质量
            if (textContent.length < 50) {
                throw new Error('网页内容太少，可能是动态生成的页面');
            }

            return {
                title: title,
                content: textContent
            };

        } catch (error) {
            console.error('HTML解析失败:', error);
            throw new Error('无法解析网页内容');
        }
    }

    /**
     * 解码HTML实体
     * @param {string} text - 包含HTML实体的文本
     * @returns {string} - 解码后的文本
     */
    decodeHtmlEntities(text) {
        const entities = {
            '&amp;': '&',
            '&lt;': '<',
            '&gt;': '>',
            '&quot;': '"',
            '&#39;': "'",
            '&apos;': "'",
            '&nbsp;': ' ',
            '&ndash;': '–',
            '&mdash;': '—',
            '&hellip;': '…',
            '&ldquo;': '"',
            '&rdquo;': '"',
            '&lsquo;': "'",
            '&rsquo;': "'",
            '&copy;': '©',
            '&reg;': '®',
            '&trade;': '™'
        };

        return text.replace(/&[#\w]+;/g, (entity) => {
            // 处理数字实体
            if (entity.startsWith('&#')) {
                const code = entity.slice(2, -1);
                if (code.startsWith('x')) {
                    // 十六进制
                    return String.fromCharCode(parseInt(code.slice(1), 16));
                } else {
                    // 十进制
                    return String.fromCharCode(parseInt(code, 10));
                }
            }
            
            // 处理命名实体
            return entities[entity] || entity;
        });
    }

    /**
     * 验证URL是否可访问
     * @param {string} url - 要验证的URL
     * @returns {Promise<boolean>} - 是否可访问
     */
    async validateUrl(url) {
        try {
            const response = await fetch(url, {
                method: 'HEAD',
                signal: AbortSignal.timeout(5000)
            });
            
            return response.ok;
        } catch (error) {
            console.warn(`URL验证失败: ${url}`, error);
            return false;
        }
    }
} 