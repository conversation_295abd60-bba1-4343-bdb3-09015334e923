"""
任务状态管理服务
负责管理存储在本地存储中的 status.json 文件
基于原始参考代码完整迁移，保持所有算法逻辑不变，只修改存储方式为本地存储
优化版本：实现读取-更新-写入逻辑，支持防抖机制
"""

import json
import asyncio
from typing import Optional, Dict, Any
from datetime import datetime
import tempfile
import os
from pathlib import Path

from app.core.logging import get_logger
from app.models.task import ConversionTask, TaskStatus
from app.core.config import settings

logger = get_logger(__name__)


class StatusService:
    """任务状态管理服务 - 本地存储版本"""

    def __init__(self, storage_service=None):
        """
        初始化状态服务

        Args:
            storage_service: 存储服务实例，如果为None则延迟初始化
        """
        self.storage_service = storage_service
        self._cache = {}  # 简单的内存缓存
        self._cache_ttl = 60  # 缓存TTL（秒）
        self._cache_timestamps = {}

        # 防抖机制相关
        self._pending_updates = {}  # 待处理的更新
        self._update_locks = {}  # 更新锁，防止并发写入
        self._debounce_delay = 2.0  # 防抖延迟（秒）

        # 关键状态节点定义
        self._critical_statuses = {"PROCESSING", "COMPLETED", "FAILED"}

        # 本地存储目录
        self.local_storage_dir = Path(settings.temp_dir) / "local-storage"
        self._ensure_storage_dir()

    def _ensure_storage_dir(self):
        """确保本地存储目录存在"""
        try:
            self.local_storage_dir.mkdir(parents=True, exist_ok=True)
            os.chmod(str(self.local_storage_dir), 0o755)
            logger.debug(f"本地存储目录已准备: {self.local_storage_dir}")
        except Exception as e:
            logger.error(f"创建本地存储目录失败: {e}")

    def _get_storage_service(self):
        """获取存储服务实例（延迟初始化）"""
        if self.storage_service is None:
            from app.services.storage_service import storage_service
            self.storage_service = storage_service
        return self.storage_service

    def _generate_status_path(self, user_id: str, task_id: str) -> str:
        """
        生成状态文件的存储路径
        
        Args:
            user_id: 用户ID
            task_id: 任务ID
            
        Returns:
            str: 状态文件路径
        """
        return f"users/{user_id}/tasks/{task_id}/status.json"

    def _get_local_status_path(self, user_id: str, task_id: str) -> Path:
        """获取本地状态文件的完整路径"""
        status_path = self._generate_status_path(user_id, task_id)
        return self.local_storage_dir / status_path

    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self._cache_timestamps:
            return False
        
        elapsed = (datetime.now() - self._cache_timestamps[cache_key]).total_seconds()
        return elapsed < self._cache_ttl

    def _update_cache(self, cache_key: str, data: Dict[str, Any]):
        """更新缓存"""
        self._cache[cache_key] = data
        self._cache_timestamps[cache_key] = datetime.now()

    def _clear_cache(self, cache_key: str):
        """清除缓存"""
        self._cache.pop(cache_key, None)
        self._cache_timestamps.pop(cache_key, None)

    async def update_status_critical(self, user_id: str, task_id: str, status: str, **kwargs) -> bool:
        """
        更新关键状态节点（立即执行，不使用防抖）

        Args:
            user_id: 用户ID
            task_id: 任务ID
            status: 新状态 (PROCESSING, COMPLETED, FAILED)
            **kwargs: 其他要更新的字段

        Returns:
            bool: 更新是否成功
        """
        if status not in self._critical_statuses:
            logger.warning(f"状态 {status} 不是关键状态节点，建议使用 update_status_debounced")

        return await self._update_status_immediate(user_id, task_id, status, **kwargs)

    async def update_status_debounced(self, user_id: str, task_id: str, status: str, **kwargs) -> bool:
        """
        防抖状态更新（用于非关键状态更新）

        Args:
            user_id: 用户ID
            task_id: 任务ID
            status: 新状态
            **kwargs: 其他要更新的字段

        Returns:
            bool: 更新是否成功
        """
        cache_key = f"{user_id}:{task_id}"

        # 如果是关键状态，立即执行
        if status in self._critical_statuses:
            return await self._update_status_immediate(user_id, task_id, status, **kwargs)

        # 非关键状态使用防抖机制
        update_data = {
            "status": status,
            "updatedAt": datetime.now().isoformat(),
            **kwargs
        }

        # 存储待更新数据
        self._pending_updates[cache_key] = update_data

        # 启动防抖任务
        asyncio.create_task(self._debounced_update(user_id, task_id, cache_key))

        return True

    async def _debounced_update(self, user_id: str, task_id: str, cache_key: str):
        """防抖更新实现"""
        try:
            # 等待防抖延迟
            await asyncio.sleep(self._debounce_delay)

            # 检查是否还有待更新的数据
            if cache_key not in self._pending_updates:
                return

            update_data = self._pending_updates.pop(cache_key)
            await self._update_status_immediate(user_id, task_id, update_data["status"], **update_data)

        except Exception as e:
            logger.error(f"防抖更新失败: {cache_key}, {str(e)}")

    async def _update_status_immediate(self, user_id: str, task_id: str, status: str, **kwargs) -> bool:
        """
        立即更新状态（读取-更新-写入逻辑）

        Args:
            user_id: 用户ID
            task_id: 任务ID
            status: 新状态
            **kwargs: 其他要更新的字段

        Returns:
            bool: 更新是否成功
        """
        cache_key = f"{user_id}:{task_id}"

        # 防止并发写入
        if cache_key in self._update_locks:
            logger.debug(f"状态更新已在进行中，跳过: {cache_key}")
            return False

        self._update_locks[cache_key] = True

        try:
            # 1. 读取现有状态文件
            existing_status = await self.load_status(user_id, task_id, use_cache=False)

            if not existing_status:
                logger.warning(f"未找到现有状态文件，无法更新: {cache_key}")
                return False

            # 2. 更新状态数据
            updated_status = existing_status.copy()
            updated_status["status"] = status
            updated_status["updatedAt"] = datetime.now().isoformat()

            # 根据状态设置特定字段
            if status == "PROCESSING":
                updated_status["startedAt"] = datetime.now().isoformat()
            elif status == "COMPLETED":
                updated_status["completedAt"] = datetime.now().isoformat()
            elif status == "FAILED":
                updated_status["completedAt"] = datetime.now().isoformat()

            # 应用其他更新字段
            for key, value in kwargs.items():
                if key in ["error", "audioPath", "audioSize", "metadata", "urls"]:
                    updated_status[key] = value
                elif key.startswith("metadata."):
                    # 支持嵌套字段更新
                    metadata_key = key.split(".", 1)[1]
                    if "metadata" not in updated_status:
                        updated_status["metadata"] = {}
                    updated_status["metadata"][metadata_key] = value
                elif key.startswith("urls."):
                    # 支持URLs字段更新
                    url_key = key.split(".", 1)[1]
                    if "urls" not in updated_status:
                        updated_status["urls"] = {}
                    updated_status["urls"][url_key] = value
                else:
                    updated_status[key] = value

            # 3. 写入更新后的状态到本地存储
            success = await self._write_status_file(user_id, task_id, updated_status)

            if success:
                # 更新缓存
                self._update_cache(cache_key, updated_status)
                logger.info(f"状态更新成功: {cache_key} -> {status}")

            return success

        except Exception as e:
            logger.error(f"状态更新失败: {cache_key}, {str(e)}")
            return False
        finally:
            # 释放锁
            self._update_locks.pop(cache_key, None)

    async def _write_status_file(self, user_id: str, task_id: str, status_data: Dict[str, Any]) -> bool:
        """写入状态文件到本地存储"""
        try:
            local_file_path = self._get_local_status_path(user_id, task_id)

            # 确保目录存在
            local_file_path.parent.mkdir(parents=True, exist_ok=True)

            # 写入状态文件
            with open(local_file_path, 'w', encoding='utf-8') as f:
                json.dump(status_data, f, ensure_ascii=False, indent=2)

            # 设置文件权限
            os.chmod(str(local_file_path), 0o644)

            logger.debug(f"状态文件写入成功: {local_file_path}")
            return True

        except Exception as e:
            logger.error(f"写入状态文件失败: {str(e)}")
            return False

    async def save_status(self, user_id: str, task_id: str, task: ConversionTask, retry_count: int = 3) -> bool:
        """
        保存任务状态到本地存储 (兼容性方法，建议使用新的update_status_*方法)

        Args:
            user_id: 用户ID
            task_id: 任务ID
            task: 任务对象
            retry_count: 重试次数

        Returns:
            bool: 保存是否成功
        """
        logger.warning("使用了旧的save_status方法，建议迁移到新的update_status_*方法")

        # 将任务状态映射到前端格式
        status_mapping = {
            "pending": "PENDING",
            "queued": "QUEUED",
            "processing": "PROCESSING",
            "completed": "COMPLETED",
            "failed": "FAILED"
        }

        frontend_status = status_mapping.get(task.status.value, task.status.value.upper())

        # 准备更新数据
        update_data = {}

        if task.error_message:
            update_data["error"] = task.error_message

        if hasattr(task, 'audio_files') and task.audio_files:
            # 设置音频相关字段
            if "mp3" in task.audio_files and "chapters" in task.audio_files["mp3"]:
                chapters = task.audio_files["mp3"]["chapters"]
                if chapters:
                    first_chapter = chapters[0]
                    if "url" in first_chapter:
                        update_data["audioPath"] = first_chapter["url"]
                    if "local_path" in first_chapter:
                        update_data["urls.audioUrl"] = first_chapter["url"]

        if hasattr(task, 'metadata') and task.metadata:
            metadata_updates = {}
            if "total_words" in task.metadata:
                metadata_updates["wordCount"] = task.metadata["total_words"]
            if "total_chapters" in task.metadata:
                metadata_updates["totalChapters"] = task.metadata["total_chapters"]

            for key, value in metadata_updates.items():
                update_data[f"metadata.{key}"] = value

        # 使用新的更新方法（带重试）
        for attempt in range(retry_count + 1):
            try:
                result = await self._update_status_immediate(user_id, task_id, frontend_status, **update_data)
                if result:
                    return True

                if attempt < retry_count:
                    logger.info(f"状态保存重试 {attempt + 1}/{retry_count}: {user_id}:{task_id}")
                    await asyncio.sleep(1)

            except Exception as e:
                if attempt < retry_count:
                    logger.warning(f"状态保存重试 {attempt + 1}/{retry_count} 失败: {str(e)}")
                    await asyncio.sleep(1)
                else:
                    logger.error(f"状态保存最终失败: {str(e)}")

        return False

    async def update_task_processing(self, user_id: str, task_id: str) -> bool:
        """
        标记任务开始处理 (已优化：当前流程中不调用)

        优化说明：
        - 为了减少50%的本地存储写入操作，当前流程不再调用此方法
        - 状态流转简化为：PENDING → COMPLETED/FAILED
        - 保留此方法以备将来需要时恢复PROCESSING状态更新
        """
        return await self.update_status_critical(user_id, task_id, "PROCESSING")

    async def update_task_completed(self, user_id: str, task_id: str, audio_url: str = None,
                                  metadata: Dict[str, Any] = None, urls: Dict[str, str] = None) -> bool:
        """标记任务完成"""
        update_data = {}

        if audio_url:
            update_data["audioPath"] = audio_url
            update_data["urls.audioUrl"] = audio_url

        if metadata:
            for key, value in metadata.items():
                update_data[f"metadata.{key}"] = value

        if urls:
            for key, value in urls.items():
                update_data[f"urls.{key}"] = value

        return await self.update_status_critical(user_id, task_id, "COMPLETED", **update_data)

    async def update_task_failed(self, user_id: str, task_id: str, error_message: str) -> bool:
        """标记任务失败"""
        return await self.update_status_critical(user_id, task_id, "FAILED", error=error_message)

    async def load_status(self, user_id: str, task_id: str, use_cache: bool = True) -> Optional[Dict[str, Any]]:
        """
        从本地存储加载任务状态

        Args:
            user_id: 用户ID
            task_id: 任务ID
            use_cache: 是否使用缓存

        Returns:
            Optional[Dict[str, Any]]: 状态数据，如果不存在则返回None
        """
        try:
            cache_key = f"{user_id}:{task_id}"

            # 检查缓存
            if use_cache and self._is_cache_valid(cache_key):
                logger.debug(f"从缓存获取状态: {cache_key}")
                return self._cache[cache_key]

            # 获取本地文件路径
            local_file_path = self._get_local_status_path(user_id, task_id)

            if not local_file_path.exists():
                logger.warning(f"状态文件不存在: {local_file_path}")
                return None

            # 直接读取本地文件
            try:
                with open(local_file_path, 'r', encoding='utf-8') as f:
                    status_data = json.load(f)

                logger.info(f"状态文件加载成功: {local_file_path}")

                # 更新缓存
                if use_cache:
                    self._update_cache(cache_key, status_data)

                return status_data

            except Exception as e:
                logger.error(f"读取本地状态文件失败: {str(e)}")
                return None

        except Exception as e:
            logger.error(f"加载状态文件失败: {str(e)}")
            return None

    async def delete_status(self, user_id: str, task_id: str) -> bool:
        """
        删除任务状态文件

        Args:
            user_id: 用户ID
            task_id: 任务ID

        Returns:
            bool: 删除是否成功
        """
        try:
            local_file_path = self._get_local_status_path(user_id, task_id)

            if local_file_path.exists():
                local_file_path.unlink()
                logger.info(f"状态文件删除成功: {local_file_path}")

                # 清除缓存
                cache_key = f"{user_id}:{task_id}"
                self._clear_cache(cache_key)
                return True
            else:
                logger.warning(f"状态文件不存在: {local_file_path}")
                return False

        except Exception as e:
            logger.error(f"删除状态文件失败: {str(e)}")
            return False

    async def update_task_status(self, user_id: str, task_id: str, status: TaskStatus,
                               progress: int = None, stage: str = None,
                               error_message: str = None) -> bool:
        """
        更新任务状态（便捷方法）

        Args:
            user_id: 用户ID
            task_id: 任务ID
            status: 新状态
            progress: 进度百分比
            stage: 当前阶段描述
            error_message: 错误信息

        Returns:
            bool: 更新是否成功
        """
        # 状态映射
        status_mapping = {
            TaskStatus.PENDING: "PENDING",
            TaskStatus.QUEUED: "QUEUED",
            TaskStatus.PROCESSING: "PROCESSING",
            TaskStatus.COMPLETED: "COMPLETED",
            TaskStatus.FAILED: "FAILED",
            TaskStatus.CANCELLED: "CANCELLED"
        }

        frontend_status = status_mapping.get(status, status.value.upper())

        update_data = {}
        if progress is not None:
            update_data["progress"] = progress
        if stage:
            update_data["currentStage"] = stage
        if error_message:
            update_data["error"] = error_message

        # 根据状态选择更新方法
        if frontend_status in self._critical_statuses:
            return await self.update_status_critical(user_id, task_id, frontend_status, **update_data)
        else:
            return await self.update_status_debounced(user_id, task_id, frontend_status, **update_data)


# 创建全局状态服务实例
status_service = StatusService()
