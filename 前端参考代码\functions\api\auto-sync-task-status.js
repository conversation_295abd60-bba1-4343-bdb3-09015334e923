// 自动同步任务状态 - 检查R2中完成的任务并更新KV状态
import { UserIdResolver } from '../utils/user-id-resolver.js';

// POST方法：手动触发状态同步
export async function onRequestPost({ request, env }) {
    try {
        const { email, taskIds, autoMode = false, checkAll = false } = await request.json();
        
        console.log(`🔄 开始任务状态同步 - 邮箱: ${email}, 自动模式: ${autoMode}`);
        
        if (!email) {
            return Response.json({ error: '邮箱参数不能为空' }, { status: 400 });
        }

        // 验证用户是否存在
        const userKey = `user:${email}`;
        const userData = await env.KV.get(userKey, { type: 'json' });
        
        if (!userData) {
            return Response.json({ error: '用户不存在' }, { status: 404 });
        }

        // 获取需要同步的任务
        let tasksToSync = [];
        if (taskIds && Array.isArray(taskIds)) {
            // 指定特定任务
            tasksToSync = await getSpecificTasks(env, email, taskIds);
        } else {
            // 获取所有可能需要同步的任务
            tasksToSync = await getTasksToSync(env, email, checkAll || autoMode);
        }

        console.log(`📋 找到 ${tasksToSync.length} 个需要同步的任务`);

        const syncResults = [];
        let updatedCount = 0;
        let errorCount = 0;

        // 并行处理多个任务，但限制并发数
        const batchSize = 3; // 限制并发数以避免性能问题
        for (let i = 0; i < tasksToSync.length; i += batchSize) {
            const batch = tasksToSync.slice(i, i + batchSize);
            const batchPromises = batch.map(task => syncSingleTask(env, email, task));
            const batchResults = await Promise.allSettled(batchPromises);

            batchResults.forEach((result, index) => {
                const task = batch[index];
                if (result.status === 'fulfilled') {
                    const syncResult = result.value;
                    syncResults.push(syncResult);
                    if (syncResult.updated) updatedCount++;
                } else {
                    console.error(`同步任务 ${task.id} 失败:`, result.reason);
                    syncResults.push({
                        taskId: task.id,
                        updated: false,
                        error: result.reason.message || '同步失败'
                    });
                    errorCount++;
                }
            });
        }

        const summary = {
            totalTasks: tasksToSync.length,
            updatedTasks: updatedCount,
            errorTasks: errorCount,
            successRate: tasksToSync.length > 0 ? Math.round((updatedCount / tasksToSync.length) * 100) : 0
        };

        console.log(`✅ 任务状态同步完成 - 总数: ${summary.totalTasks}, 更新: ${summary.updatedTasks}, 错误: ${summary.errorTasks}`);

        return Response.json({
            success: true,
            summary,
            results: syncResults,
            message: `状态同步完成，${updatedCount} 个任务已更新`
        });

    } catch (error) {
        console.error('自动同步任务状态失败:', error);
        return Response.json({ 
            error: '同步失败: ' + error.message 
        }, { status: 500 });
    }
}

// GET方法：获取需要同步的任务列表（不执行同步）
export async function onRequestGet({ request, env }) {
    try {
        const url = new URL(request.url);
        const email = url.searchParams.get('email');
        const checkAll = url.searchParams.get('checkAll') === 'true';
        
        if (!email) {
            return Response.json({ error: '邮箱参数不能为空' }, { status: 400 });
        }

        // 验证用户是否存在
        const userKey = `user:${email}`;
        const userData = await env.KV.get(userKey, { type: 'json' });
        
        if (!userData) {
            return Response.json({ error: '用户不存在' }, { status: 404 });
        }

        const tasksToSync = await getTasksToSync(env, email, checkAll);

        return Response.json({
            success: true,
            tasksToSync: tasksToSync.map(task => ({
                taskId: task.id,
                filename: task.filename,
                title: task.title,
                currentStatus: task.status,
                progress: task.progress,
                createdAt: task.createdAt,
                lastUpdate: task.lastProgressUpdate || task.createdAt,
                isEnhanced: task.isEnhanced,
                totalChapters: task.totalChapters
            })),
            total: tasksToSync.length
        });

    } catch (error) {
        console.error('获取同步任务列表失败:', error);
        return Response.json({ 
            error: '获取列表失败: ' + error.message 
        }, { status: 500 });
    }
}

// 获取需要同步的任务
async function getTasksToSync(env, email, checkAll = false) {
    const tasks = [];
    
    try {
        // 获取用户的任务列表
        const userKey = `user:${email}`;
        const userData = await env.KV.get(userKey, { type: 'json' });
        
        let taskIds = [];
        
        if (userData?.taskIds && Array.isArray(userData.taskIds)) {
            // 如果不是检查所有任务，只检查最近的50个
            taskIds = checkAll ? userData.taskIds : userData.taskIds.slice(-50);
        } else {
            // 后备方案：扫描KV中的任务
            const taskList = await env.KV.list({ prefix: 'task:', limit: checkAll ? 1000 : 100 });
            taskIds = taskList.keys
                .map(key => key.name.replace('task:', ''))
                .filter(id => id);
        }

        const now = new Date();

        for (const taskId of taskIds) {
            try {
                const task = await env.KV.get(`task:${taskId}`, { type: 'json' });
                
                if (task && task.email === email) {
                    // 检查是否需要同步：
                    // 1. 状态不是最终状态（SUCCESS, COMPLETED, FAILED）
                    // 2. 在自动模式下，检查所有未完成任务，不仅仅是卡住的任务
                    const isNonFinalStatus = !['SUCCESS', 'COMPLETED', 'FAILED', 'CANCELLED'].includes(task.status);

                    if (checkAll) {
                        // 自动模式：检查所有未完成状态的任务
                        if (isNonFinalStatus) {
                            tasks.push(task);
                        }
                    } else {
                        // 手动模式：只检查卡住的任务
                        const isStuckTask = isTaskStuck(task, now, 15); // 15分钟超时
                        if (isNonFinalStatus || isStuckTask) {
                            tasks.push(task);
                        }
                    }
                }
            } catch (error) {
                console.warn(`检查任务 ${taskId} 失败:`, error);
            }
        }
    } catch (error) {
        console.error('获取任务列表失败:', error);
    }
    
    return tasks;
}

// 获取指定的任务
async function getSpecificTasks(env, email, taskIds) {
    const tasks = [];
    
    for (const taskId of taskIds) {
        try {
            const task = await env.KV.get(`task:${taskId}`, { type: 'json' });
            
            if (task && task.email === email) {
                tasks.push(task);
            }
        } catch (error) {
            console.warn(`获取任务 ${taskId} 失败:`, error);
        }
    }
    
    return tasks;
}

// 判断任务是否卡住
function isTaskStuck(task, now, timeoutMinutes = 15) {
    const lastUpdate = task.lastProgressUpdate ? 
        new Date(task.lastProgressUpdate) : 
        new Date(task.createdAt);
    
    const timeDiff = now - lastUpdate;
    const timeoutMs = timeoutMinutes * 60 * 1000;
    
    return timeDiff > timeoutMs;
}

// 同步单个任务
async function syncSingleTask(env, email, task) {
    const result = {
        taskId: task.id,
        originalStatus: task.status,
        newStatus: null,
        updated: false,
        audioFound: false,
        filesFound: [],
        error: null
    };
    
    try {
        console.log(`🔄 同步任务 ${task.id} - 当前状态: ${task.status}`);
        
        // 如果任务已经是最终成功状态，跳过
        if (task.status === 'SUCCESS' || task.status === 'COMPLETED') {
            result.newStatus = task.status;
            return result;
        }
        
        // 检查R2中是否存在音频文件
        const audioCheckResult = await checkAudioFilesInR2(env, email, task);
        result.audioFound = audioCheckResult.found;
        result.filesFound = audioCheckResult.files;
        
        if (audioCheckResult.found) {
            // 找到音频文件，更新任务状态
            const updatedTask = {
                ...task,
                status: 'SUCCESS',
                progress: 100,
                completedAt: new Date().toISOString(),
                lastProgressUpdate: new Date().toISOString()
            };
            
            // 设置音频URL
            if (!updatedTask.audioUrl) {
                if (updatedTask.isEnhanced || updatedTask.totalChapters > 1) {
                    updatedTask.audioUrl = `/api/audio/playlist/${task.id}`;
                    updatedTask.playlistUrl = `/api/audio/playlist/${task.id}`;
                    updatedTask.metadataUrl = `/api/audio/metadata/${task.id}`;
                } else {
                    updatedTask.audioUrl = `/api/audio/${task.id}.mp3`;
                }
            }
            
            // 保存更新的任务
            await env.KV.put(`task:${task.id}`, JSON.stringify(updatedTask));
            
            result.updated = true;
            result.newStatus = 'SUCCESS';
            
            console.log(`✅ 任务 ${task.id} 状态已同步为 SUCCESS`);
        } else {
            // 检查是否应该标记为失败（使用更合理的超时判断）
            const createdAt = new Date(task.createdAt);
            const now = new Date();
            const hoursSinceCreated = (now - createdAt) / (1000 * 60 * 60);

            // 更智能的超时判断：
            // 1. 基础超时时间：3小时（给复杂任务更多时间）
            // 2. 如果有进度更新记录，从最后更新时间开始计算
            // 3. 如果任务有错误记录，可能是真正的失败
            let timeoutHours = 3; // 基础超时时间改为3小时
            let timeReference = createdAt;

            // 如果有最后进度更新时间，使用该时间作为参考
            if (task.lastProgressUpdate) {
                timeReference = new Date(task.lastProgressUpdate);
                timeoutHours = 2; // 如果有进度更新，超时时间为2小时
            }

            const hoursSinceReference = (now - timeReference) / (1000 * 60 * 60);
            const shouldTimeout = hoursSinceReference > timeoutHours &&
                                 (task.status === 'PROCESSING' || task.status === 'PENDING') &&
                                 !task.error; // 如果已有错误信息，不重复标记超时

            if (shouldTimeout) {
                // 标记为失败
                const updatedTask = {
                    ...task,
                    status: 'FAILED',
                    progress: 0,
                    failedAt: new Date().toISOString(),
                    error: `任务超时：超过${timeoutHours}小时未生成音频文件`,
                    lastProgressUpdate: new Date().toISOString()
                };

                await env.KV.put(`task:${task.id}`, JSON.stringify(updatedTask));

                result.updated = true;
                result.newStatus = 'FAILED';
                result.error = '任务超时';

                console.log(`⏰ 任务 ${task.id} 因超时标记为失败 (${timeoutHours}小时无响应)`);
            } else {
                result.newStatus = task.status;
                const remainingTime = timeoutHours - hoursSinceReference;
                console.log(`⏳ 任务 ${task.id} 暂未完成，剩余时间: ${remainingTime.toFixed(1)}小时`);
            }
        }
        
    } catch (error) {
        console.error(`同步任务 ${task.id} 失败:`, error);
        result.error = error.message;
    }
    
    return result;
}

// 检查R2中的音频文件（优化版本，更准确识别完成任务）
async function checkAudioFilesInR2(env, email, task) {
    const result = {
        found: false,
        files: [],
        details: {
            audioFiles: 0,
            playlistFiles: 0,
            metadataFiles: 0,
            totalSize: 0,
            pathsChecked: []
        }
    };

    try {
        // 使用用户ID解析器
        const resolver = new UserIdResolver(env);
        const userIdResult = await resolver.resolveUserId(email, task.id);

        if (!userIdResult.userId) {
            console.error(`❌ 无法解析用户ID: ${email}`);
            return result;
        }

        const userId = userIdResult.userId;

        // 扩展路径模式检查，包含更多可能的路径
        // 特别针对任务 1750323014568_x71zllk0y 的问题，优先检查 audios 路径
        const pathPatterns = [
            // 旧的audios路径格式（提高优先级）
            `users/${userId}/audios/${task.id}/`,
            // 新的audiobooks路径格式
            `users/${userId}/audiobooks/${task.id}/`,
            // 可能的其他路径格式
            `users/${userId}/audio/${task.id}/`,
            `audiobooks/${userId}/${task.id}/`,
            `audios/${userId}/${task.id}/`
        ];

        // 如果用户ID解析失败，尝试使用已知的用户ID模式
        if (!userId || userId === 'unknown') {
            console.warn(`⚠️ 用户ID解析失败，尝试使用备用方法检查任务 ${task.id}`);
            return await fallbackR2Check(env, email, task);
        }

        console.log(`🔍 检查任务 ${task.id} 的音频文件，用户ID: ${userId}`);

        // 检查每个路径模式
        for (const pattern of pathPatterns) {
            try {
                result.details.pathsChecked.push(pattern);

                // 尝试多个R2绑定
                const r2Bindings = [
                    { name: 'R2', bucket: env.R2 },
                    { name: 'STORAGE', bucket: env.STORAGE },
                    { name: 'AUDIO_BUCKET', bucket: env.AUDIO_BUCKET }
                ].filter(binding => binding.bucket);

                let listResult = null;
                for (const binding of r2Bindings) {
                    try {
                        listResult = await binding.bucket.list({
                            prefix: pattern,
                            limit: 100 // 增加限制以获取更多文件
                        });
                        if (listResult && listResult.objects && listResult.objects.length > 0) {
                            console.log(`✅ 使用绑定 ${binding.name} 找到文件`);
                            break;
                        }
                    } catch (bindingError) {
                        console.warn(`绑定 ${binding.name} 失败:`, bindingError.message);
                        continue;
                    }
                }

                if (!listResult) {
                    throw new Error('所有R2绑定都失败');
                }

                if (listResult.objects && listResult.objects.length > 0) {
                    console.log(`📁 在 ${pattern} 找到 ${listResult.objects.length} 个文件`);

                    // 更精确的文件分类
                    const audioFiles = listResult.objects.filter(obj => {
                        const key = obj.key.toLowerCase();
                        return key.endsWith('.mp3') ||
                               key.endsWith('.wav') ||
                               key.endsWith('.m4a') ||
                               key.endsWith('.aac') ||
                               key.endsWith('.ogg') ||
                               key.endsWith('.flac');
                    });

                    const playlistFiles = listResult.objects.filter(obj => {
                        const key = obj.key.toLowerCase();
                        return key.endsWith('.json') ||
                               key.endsWith('.m3u8') ||
                               key.endsWith('.m3u') ||
                               key.includes('playlist');
                    });

                    const metadataFiles = listResult.objects.filter(obj => {
                        const key = obj.key.toLowerCase();
                        return key.includes('metadata') ||
                               key.includes('info') ||
                               key.endsWith('.txt') ||
                               key.endsWith('.xml');
                    });

                    // 计算总大小
                    const totalSize = listResult.objects.reduce((sum, obj) => sum + (obj.size || 0), 0);

                    // 更新统计信息
                    result.details.audioFiles += audioFiles.length;
                    result.details.playlistFiles += playlistFiles.length;
                    result.details.metadataFiles += metadataFiles.length;
                    result.details.totalSize += totalSize;

                    // 添加文件信息
                    result.files.push(...listResult.objects.map(obj => ({
                        key: obj.key,
                        size: obj.size,
                        lastModified: obj.lastModified,
                        type: obj.key.toLowerCase().endsWith('.mp3') || obj.key.toLowerCase().endsWith('.wav') || obj.key.toLowerCase().endsWith('.m4a') ? 'audio' :
                              obj.key.toLowerCase().endsWith('.json') || obj.key.toLowerCase().includes('playlist') ? 'playlist' :
                              obj.key.toLowerCase().includes('metadata') ? 'metadata' : 'other',
                        path: pattern
                    })));

                    // 更严格的完成判断条件
                    const hasValidAudio = audioFiles.length > 0 && audioFiles.some(f => f.size > 1024); // 至少1KB
                    const hasValidPlaylist = playlistFiles.length > 0;
                    const hasMinimumSize = totalSize > 10240; // 至少10KB

                    // 根据任务类型判断是否完成
                    let isCompleted = false;
                    if (task.isEnhanced || task.totalChapters > 1) {
                        // 增强版任务：需要播放列表文件或多个音频文件
                        isCompleted = hasValidPlaylist || (audioFiles.length > 1 && hasMinimumSize);
                    } else {
                        // 普通任务：需要至少一个有效音频文件
                        isCompleted = hasValidAudio && hasMinimumSize;
                    }

                    if (isCompleted) {
                        result.found = true;
                        console.log(`✅ 任务 ${task.id} 在 ${pattern} 找到完成文件 - 音频: ${audioFiles.length}, 播放列表: ${playlistFiles.length}, 总大小: ${Math.round(totalSize/1024)}KB`);
                        break;
                    } else {
                        console.log(`⚠️ 任务 ${task.id} 在 ${pattern} 找到文件但不满足完成条件 - 音频: ${audioFiles.length}, 播放列表: ${playlistFiles.length}, 总大小: ${Math.round(totalSize/1024)}KB`);
                    }
                }
            } catch (error) {
                console.warn(`检查路径 ${pattern} 失败:`, error);
            }
        }

        // 记录检查结果
        console.log(`📊 任务 ${task.id} 文件检查完成 - 找到: ${result.found}, 音频: ${result.details.audioFiles}, 播放列表: ${result.details.playlistFiles}, 总大小: ${Math.round(result.details.totalSize/1024)}KB`);

    } catch (error) {
        console.error('检查R2音频文件失败:', error);
    }

    return result;
}

// 通用备用R2检查方法 - 当用户ID解析失败时使用
async function fallbackR2Check(env, email, task) {
    const result = {
        found: false,
        files: [],
        details: {
            audioFiles: 0,
            playlistFiles: 0,
            metadataFiles: 0,
            totalSize: 0,
            pathsChecked: [],
            fallbackMethod: true,
            discoveredUserId: null
        }
    };

    try {
        console.log(`🔄 使用通用备用方法检查任务 ${task.id} 的R2文件`);

        // 方法1：智能用户ID生成策略
        const potentialUserIds = await generatePotentialUserIds(env, email, task);
        console.log(`🔍 生成了 ${potentialUserIds.length} 个潜在用户ID进行检查`);

        for (const userIdCandidate of potentialUserIds) {
            const pathPatterns = [
                `users/${userIdCandidate.id}/audios/${task.id}/`,
                `users/${userIdCandidate.id}/audiobooks/${task.id}/`,
                `users/${userIdCandidate.id}/audio/${task.id}/`,
            ];

            for (const pattern of pathPatterns) {
                try {
                    result.details.pathsChecked.push(pattern);

                    // 尝试多个R2绑定
                    const r2Bindings = [
                        { name: 'R2', bucket: env.R2 },
                        { name: 'STORAGE', bucket: env.STORAGE },
                        { name: 'AUDIO_BUCKET', bucket: env.AUDIO_BUCKET }
                    ].filter(binding => binding.bucket);

                    let listResult = null;
                    for (const binding of r2Bindings) {
                        try {
                            listResult = await binding.bucket.list({
                                prefix: pattern,
                                limit: 20
                            });
                            if (listResult && listResult.objects && listResult.objects.length > 0) {
                                console.log(`✅ 使用绑定 ${binding.name} 找到文件`);
                                break;
                            }
                        } catch (bindingError) {
                            console.warn(`绑定 ${binding.name} 失败:`, bindingError.message);
                            continue;
                        }
                    }

                    if (!listResult) {
                        throw new Error('所有R2绑定都失败');
                    }

                    if (listResult.objects && listResult.objects.length > 0) {
                        console.log(`✅ 备用方法在 ${pattern} 找到 ${listResult.objects.length} 个文件 (方法: ${userIdCandidate.method})`);

                        // 分析文件
                        const audioFiles = listResult.objects.filter(obj => {
                            const key = obj.key.toLowerCase();
                            return key.endsWith('.mp3') || key.endsWith('.wav') || key.endsWith('.m4a');
                        });

                        const metadataFiles = listResult.objects.filter(obj => {
                            const key = obj.key.toLowerCase();
                            return key.includes('metadata') || key.endsWith('.json');
                        });

                        result.details.audioFiles += audioFiles.length;
                        result.details.metadataFiles += metadataFiles.length;
                        result.details.discoveredUserId = userIdCandidate.id;
                        result.details.discoveryMethod = userIdCandidate.method;

                        result.files.push(...listResult.objects.map(obj => ({
                            key: obj.key,
                            size: obj.size,
                            lastModified: obj.lastModified,
                            type: obj.key.toLowerCase().endsWith('.mp3') ? 'audio' :
                                  obj.key.toLowerCase().includes('metadata') ? 'metadata' : 'other',
                            path: pattern,
                            foundByFallback: true,
                            discoveryMethod: userIdCandidate.method
                        })));

                        // 如果找到音频文件，认为任务完成
                        if (audioFiles.length > 0) {
                            result.found = true;
                            console.log(`🎉 备用方法成功找到任务 ${task.id} 的完成文件！用户ID: ${userIdCandidate.id} (方法: ${userIdCandidate.method})`);

                            // 更新用户数据，记录发现的用户ID
                            await updateUserDataWithDiscoveredId(env, email, userIdCandidate.id, userIdCandidate.method);

                            return result;
                        }
                    }
                } catch (error) {
                    console.warn(`备用方法检查路径 ${pattern} 失败:`, error);
                }
            }
        }

        // 方法2：如果上述方法都失败，尝试模糊搜索
        if (!result.found) {
            console.log(`🔍 尝试模糊搜索任务 ${task.id}`);
            try {
                // 尝试多个R2绑定进行模糊搜索
                const r2Bindings = [
                    { name: 'R2', bucket: env.R2 },
                    { name: 'STORAGE', bucket: env.STORAGE },
                    { name: 'AUDIO_BUCKET', bucket: env.AUDIO_BUCKET }
                ].filter(binding => binding.bucket);

                let fuzzyResult = null;
                for (const binding of r2Bindings) {
                    try {
                        fuzzyResult = await binding.bucket.list({
                            prefix: 'users/',
                            limit: 1000
                        });
                        if (fuzzyResult && fuzzyResult.objects && fuzzyResult.objects.length > 0) {
                            console.log(`✅ 使用绑定 ${binding.name} 进行模糊搜索`);
                            break;
                        }
                    } catch (bindingError) {
                        console.warn(`绑定 ${binding.name} 模糊搜索失败:`, bindingError.message);
                        continue;
                    }
                }

                if (!fuzzyResult) {
                    throw new Error('所有R2绑定的模糊搜索都失败');
                }

                if (fuzzyResult.objects) {
                    const matchingFiles = fuzzyResult.objects.filter(obj =>
                        obj.key.includes(task.id)
                    );

                    if (matchingFiles.length > 0) {
                        console.log(`🎯 模糊搜索找到 ${matchingFiles.length} 个匹配文件`);
                        result.files.push(...matchingFiles.map(obj => ({
                            key: obj.key,
                            size: obj.size,
                            lastModified: obj.lastModified,
                            type: 'fuzzy_match',
                            foundByFuzzySearch: true
                        })));

                        const audioFiles = matchingFiles.filter(obj =>
                            obj.key.toLowerCase().endsWith('.mp3') ||
                            obj.key.toLowerCase().endsWith('.wav') ||
                            obj.key.toLowerCase().endsWith('.m4a')
                        );

                        if (audioFiles.length > 0) {
                            result.found = true;
                            result.details.audioFiles = audioFiles.length;
                            console.log(`🎉 模糊搜索成功找到任务 ${task.id} 的音频文件！`);
                        }
                    }
                }
            } catch (error) {
                console.warn('模糊搜索失败:', error);
            }
        }

    } catch (error) {
        console.error('备用R2检查失败:', error);
    }

    return result;
}

// 生成潜在的用户ID列表 - 通用方法
async function generatePotentialUserIds(env, email, task) {
    const candidates = [];

    try {
        // 方法1：标准邮箱哈希（多种变体）
        const emailVariants = [
            email.trim().toLowerCase(),
            email.trim(),
            email.toLowerCase(),
            email
        ];

        for (const variant of emailVariants) {
            try {
                const hash = hashEmailVariant(variant);
                candidates.push({
                    id: hash,
                    method: `emailHash_${variant === email ? 'original' : 'normalized'}`,
                    confidence: variant === email.trim().toLowerCase() ? 90 : 70
                });
            } catch (error) {
                // 继续尝试其他变体
            }
        }

        // 方法2：从任务ID中提取可能的用户ID
        if (task.id.includes('_')) {
            const parts = task.id.split('_');
            if (parts.length > 1) {
                const potentialId = parts[1];
                if (potentialId && potentialId.length >= 3) {
                    candidates.push({
                        id: potentialId,
                        method: 'taskId_extraction',
                        confidence: 60
                    });
                }
            }
        }

        // 方法3：从邮箱用户名部分生成
        const emailParts = email.split('@');
        if (emailParts.length > 0) {
            const username = emailParts[0];

            // 直接使用用户名
            if (username.match(/^[a-z0-9]+$/i) && username.length >= 3) {
                candidates.push({
                    id: username.toLowerCase(),
                    method: 'email_username',
                    confidence: 40
                });
            }

            // 用户名的前6位
            if (username.length >= 6) {
                candidates.push({
                    id: username.substring(0, 6).toLowerCase(),
                    method: 'email_username_prefix',
                    confidence: 35
                });
            }
        }

        // 方法4：从用户的其他任务中推断
        const inferredIds = await inferUserIdFromOtherTasks(env, email);
        candidates.push(...inferredIds);

        // 方法5：从R2存储中反向搜索
        const r2DiscoveredIds = await discoverUserIdFromR2(env, task.id);
        candidates.push(...r2DiscoveredIds);

        // 去重并按置信度排序
        const uniqueCandidates = [];
        const seenIds = new Set();

        for (const candidate of candidates) {
            if (!seenIds.has(candidate.id)) {
                seenIds.add(candidate.id);
                uniqueCandidates.push(candidate);
            }
        }

        // 按置信度降序排序
        uniqueCandidates.sort((a, b) => b.confidence - a.confidence);

        console.log(`📊 生成的用户ID候选列表: ${uniqueCandidates.map(c => `${c.id}(${c.confidence}%)`).join(', ')}`);

    } catch (error) {
        console.error('生成潜在用户ID失败:', error);
    }

    return candidates;
}

// 邮箱哈希变体算法
function hashEmailVariant(email) {
    if (!email || typeof email !== 'string') {
        throw new Error('邮箱地址无效');
    }

    // 使用与系统一致的哈希算法
    let hash = 0;
    for (let i = 0; i < email.length; i++) {
        const char = email.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // 转换为32位整数
    }

    return Math.abs(hash).toString(36);
}

// 从用户的其他任务中推断用户ID
async function inferUserIdFromOtherTasks(env, email) {
    const candidates = [];

    try {
        // 获取用户的任务列表
        const userKey = `user:${email}`;
        const userData = await env.KV.get(userKey, { type: 'json' });

        if (userData?.taskIds && Array.isArray(userData.taskIds)) {
            // 检查最近的几个任务
            const recentTasks = userData.taskIds.slice(-10);

            for (const taskId of recentTasks) {
                try {
                    const task = await env.KV.get(`task:${taskId}`, { type: 'json' });
                    if (task && task.audioUrl) {
                        // 从audioUrl中提取用户ID
                        const match = task.audioUrl.match(/users\/([^\/]+)\/(audios|audiobooks)/);
                        if (match) {
                            const userId = match[1];
                            candidates.push({
                                id: userId,
                                method: 'task_inference',
                                confidence: 80,
                                sourceTask: taskId
                            });
                        }
                    }
                } catch (error) {
                    // 继续检查其他任务
                }
            }
        }
    } catch (error) {
        console.warn('从任务推断用户ID失败:', error);
    }

    return candidates;
}

// 从R2存储中发现用户ID
async function discoverUserIdFromR2(env, taskId) {
    const candidates = [];

    try {
        // 搜索包含任务ID的文件
        // 尝试多个R2绑定进行搜索
        const r2Bindings = [
            { name: 'R2', bucket: env.R2 },
            { name: 'STORAGE', bucket: env.STORAGE },
            { name: 'AUDIO_BUCKET', bucket: env.AUDIO_BUCKET }
        ].filter(binding => binding.bucket);

        let searchResult = null;
        for (const binding of r2Bindings) {
            try {
                searchResult = await binding.bucket.list({
                    prefix: 'users/',
                    limit: 1000
                });
                if (searchResult && searchResult.objects && searchResult.objects.length > 0) {
                    console.log(`✅ 使用绑定 ${binding.name} 进行搜索`);
                    break;
                }
            } catch (bindingError) {
                console.warn(`绑定 ${binding.name} 搜索失败:`, bindingError.message);
                continue;
            }
        }

        if (!searchResult) {
            throw new Error('所有R2绑定的搜索都失败');
        }

        if (searchResult.objects) {
            const matchingFiles = searchResult.objects.filter(obj =>
                obj.key.includes(taskId)
            );

            for (const file of matchingFiles) {
                // 从文件路径中提取用户ID
                const match = file.key.match(/users\/([^\/]+)\//);
                if (match) {
                    const userId = match[1];
                    candidates.push({
                        id: userId,
                        method: 'r2_discovery',
                        confidence: 95,
                        sourceFile: file.key
                    });
                }
            }
        }
    } catch (error) {
        console.warn('从R2发现用户ID失败:', error);
    }

    return candidates;
}

// 更新用户数据，记录发现的用户ID
async function updateUserDataWithDiscoveredId(env, email, userId, method) {
    try {
        const userKey = `user:${email}`;
        const userData = await env.KV.get(userKey, { type: 'json' });

        if (userData) {
            userData.userId = userId;
            userData.userIdDiscoveryMethod = method;
            userData.userIdDiscoveredAt = new Date().toISOString();

            await env.KV.put(userKey, JSON.stringify(userData));
            console.log(`✅ 已更新用户 ${email} 的用户ID为 ${userId} (方法: ${method})`);
        }
    } catch (error) {
        console.warn('更新用户数据失败:', error);
    }
}

// OPTIONS方法处理CORS
export async function onRequestOptions() {
    return new Response(null, {
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type',
            'Access-Control-Max-Age': '86400'
        }
    });
} 