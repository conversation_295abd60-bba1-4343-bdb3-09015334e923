// 音频库查询 API - 专门用于获取有音频的任务
export async function onRequestGet({ request, env }) {
    try {
        const url = new URL(request.url);
        const email = url.searchParams.get('email');
        
        if (!email) {
            return Response.json({ error: '邮箱参数不能为空' }, { status: 400 });
        }

        // 验证用户是否存在
        const userKey = `user:${email}`;
        const userData = await env.KV.get(userKey, { type: 'json' });
        
        if (!userData) {
            return Response.json({ error: '用户不存在' }, { status: 404 });
        }

        // 获取用户所有任务
        const userTasks = [];
        
        // 如果用户数据中有任务ID列表，直接使用
        if (userData.taskIds && Array.isArray(userData.taskIds)) {
            console.log(`音频库查询：用户 ${email} 有 ${userData.taskIds.length} 个任务ID记录`);
            
            // 获取所有任务ID（不限制数量）
            for (const taskId of userData.taskIds) {
                try {
                    const task = await env.KV.get(`task:${taskId}`, { type: 'json' });
                    if (task && task.email === email) {
                        userTasks.push(task);
                    }
                } catch (error) {
                    console.warn(`读取任务 ${taskId} 失败:`, error);
                    // 继续处理其他任务
                }
            }
        } else {
            // 后备方案：扫描所有任务
            console.log(`音频库查询：用户 ${email} 没有任务ID列表，使用后备查询方法`);
            
            const taskList = await env.KV.list({ prefix: 'task:', limit: 1000 });
            let processedCount = 0;
            const maxProcess = 500; // 提高处理数量限制
            
            for (const key of taskList.keys) {
                if (processedCount >= maxProcess) break;
                
                try {
                    const task = await env.KV.get(key.name, { type: 'json' });
                    if (task && task.email === email) {
                        userTasks.push(task);
                    }
                    processedCount++;
                } catch (error) {
                    console.warn(`读取任务 ${key.name} 失败:`, error);
                    processedCount++;
                }
            }
        }

        // 过滤出成功的任务
        const successTasks = userTasks.filter(task => task.status === 'SUCCESS');
        
        // 过滤出有音频的任务（放宽条件，包含更多可能的音频字段）
        const audioTasks = successTasks.filter(task => {
            const hasAudioUrl = !!task.audioUrl;
            const hasPlaylistUrl = !!task.playlistUrl;
            const hasAudioPath = !!task.audioPath;
            const isEnhanced = task.isEnhanced || task.totalChapters > 1;
            
            // 如果是增强版，只要有playlistUrl就算有音频
            if (isEnhanced && hasPlaylistUrl) {
                return true;
            }
            
            // 普通任务需要有audioUrl或audioPath
            return hasAudioUrl || hasAudioPath;
        });

        // 检查可能缺少audioUrl的任务（状态为SUCCESS但没有audioUrl）
        const potentialAudioTasks = successTasks.filter(task => {
            const hasAudioUrl = !!task.audioUrl;
            const hasPlaylistUrl = !!task.playlistUrl;
            const hasAudioPath = !!task.audioPath;
            const isEnhanced = task.isEnhanced || task.totalChapters > 1;
            
            // 如果是增强版且没有audioUrl但有其他音频相关字段，可能需要修复
            if (isEnhanced && !hasAudioUrl && (hasPlaylistUrl || task.totalChapters > 0)) {
                return true;
            }
            
            // 如果有audioPath但没有audioUrl，也可能需要修复
            if (hasAudioPath && !hasAudioUrl) {
                return true;
            }
            
            return false;
        });

        // 按创建时间倒序排列
        audioTasks.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        console.log(`音频库查询结果：用户 ${email} 共有 ${userTasks.length} 个任务，${successTasks.length} 个成功任务，${audioTasks.length} 个音频任务，${potentialAudioTasks.length} 个可能需要修复的任务`);

        return Response.json({
            success: true,
            audioBooks: audioTasks,
            total: audioTasks.length,
            statistics: {
                totalTasks: userTasks.length,
                successTasks: successTasks.length,
                audioTasks: audioTasks.length,
                potentialFixTasks: potentialAudioTasks.length
            },
            potentialFixTasks: potentialAudioTasks.map(task => ({
                id: task.id,
                filename: task.filename,
                title: task.title,
                isEnhanced: task.isEnhanced,
                hasPlaylistUrl: !!task.playlistUrl,
                hasAudioPath: !!task.audioPath,
                totalChapters: task.totalChapters
            }))
        });

    } catch (error) {
        console.error('获取音频库错误:', error);
        return Response.json({ error: '服务器内部错误' }, { status: 500 });
    }
} 