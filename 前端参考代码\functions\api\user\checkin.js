// 用户签到 API
export async function onRequestPost({ request, env }) {
    try {
        const { email } = await request.json();
        
        if (!email) {
            return Response.json({ error: '邮箱不能为空' }, { status: 400 });
        }

        // 获取用户数据
        const userKey = `user:${email}`;
        const userData = await env.KV.get(userKey, { type: 'json' });
        
        if (!userData) {
            return Response.json({ error: '用户不存在' }, { status: 404 });
        }

        // 检查是否已经签到
        const now = new Date();
        const today = now.toDateString();
        
        if (userData.lastCheckin) {
            const lastCheckinDate = new Date(userData.lastCheckin).toDateString();
            if (lastCheckinDate === today) {
                return Response.json({ error: '今日已签到，请明天再来' }, { status: 400 });
            }
        }

        // 随机奖励积分 (5-10)
        const reward = Math.floor(Math.random() * 6) + 5;
        
        // 更新用户数据
        userData.points += reward;
        userData.lastCheckin = now.toISOString();
        
        // 保存更新后的用户数据
        await env.KV.put(userKey, JSON.stringify(userData));

        // 创建积分获得记录
        const pointsRecordId = `points_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const pointsRecord = {
            id: pointsRecordId,
            email: email,
            type: 'EARN', // 获得
            amount: reward,
            balance: userData.points,
            description: '每日签到奖励',
            createdAt: new Date().toISOString()
        };

        const recordKey = `points_record:${email}:${pointsRecordId}`;
        await env.KV.put(recordKey, JSON.stringify(pointsRecord));
        
        return Response.json({
            success: true,
            points: userData.points,
            reward: reward,
            message: `签到成功！获得 ${reward} 积分`
        });

    } catch (error) {
        console.error('签到错误:', error);
        return Response.json({ error: '服务器内部错误' }, { status: 500 });
    }
} 