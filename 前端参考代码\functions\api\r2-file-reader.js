/**
 * R2文件读取API端点
 * 用于前端直接读取R2存储中的文件内容
 */

import { R2StorageManager } from '../utils/r2-storage.js';

export async function onRequestPost({ request, env }) {
    try {
        // 解析请求体
        const { filePath } = await request.json();
        
        if (!filePath) {
            return new Response(JSON.stringify({
                success: false,
                error: '文件路径不能为空'
            }), {
                status: 400,
                headers: { 'Content-Type': 'application/json' }
            });
        }

        console.log(`📁 读取R2文件: ${filePath}`);

        // 初始化R2存储管理器
        const r2Storage = new R2StorageManager(env.R2);

        // 直接读取文件
        const fileObject = await r2Storage.getFile(filePath);
        
        if (!fileObject) {
            return new Response(JSON.stringify({
                success: false,
                error: '文件不存在'
            }), {
                status: 404,
                headers: { 'Content-Type': 'application/json' }
            });
        }

        // 根据文件扩展名决定读取方式
        let fileContent;
        const isJsonFile = filePath.toLowerCase().endsWith('.json');
        
        if (isJsonFile) {
            const textContent = await fileObject.text();
            try {
                fileContent = JSON.parse(textContent);
            } catch (parseError) {
                console.error('JSON解析失败:', parseError);
                return new Response(JSON.stringify({
                    success: false,
                    error: 'JSON文件格式错误'
                }), {
                    status: 400,
                    headers: { 'Content-Type': 'application/json' }
                });
            }
        } else {
            fileContent = await fileObject.text();
        }

        console.log(`✅ 成功读取R2文件: ${filePath}`);

        return new Response(JSON.stringify({
            success: true,
            data: fileContent,
            filePath: filePath,
            contentType: isJsonFile ? 'application/json' : 'text/plain',
            size: fileObject.size || 0,
            lastModified: fileObject.uploaded || null
        }), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
        });

    } catch (error) {
        console.error('R2文件读取失败:', error);
        
        return new Response(JSON.stringify({
            success: false,
            error: error.message || '读取文件时发生未知错误'
        }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        });
    }
}

// 支持GET请求用于简单的文件存在性检查
export async function onRequestGet({ request, env }) {
    try {
        const url = new URL(request.url);
        const filePath = url.searchParams.get('filePath');
        
        if (!filePath) {
            return new Response(JSON.stringify({
                success: false,
                error: '文件路径不能为空'
            }), {
                status: 400,
                headers: { 'Content-Type': 'application/json' }
            });
        }

        console.log(`🔍 检查R2文件存在性: ${filePath}`);

        // 初始化R2存储管理器
        const r2Storage = new R2StorageManager(env.R2);

        // 检查文件是否存在
        const fileObject = await r2Storage.getFile(filePath);
        
        const exists = !!fileObject;
        
        return new Response(JSON.stringify({
            success: true,
            exists: exists,
            filePath: filePath,
            size: exists ? (fileObject.size || 0) : 0,
            lastModified: exists ? (fileObject.uploaded || null) : null
        }), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
        });

    } catch (error) {
        console.error('R2文件检查失败:', error);
        
        return new Response(JSON.stringify({
            success: false,
            error: error.message || '检查文件时发生未知错误'
        }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        });
    }
}
