// 任务查询 API - 优化版 + 自动状态同步
import { UserIdResolver } from '../utils/user-id-resolver.js';
import { formatDateTime } from '../utils/time-formatter.js';

export async function onRequestGet({ request, env }) {
    try {
        const url = new URL(request.url);
        const email = url.searchParams.get('email');
        const showAll = url.searchParams.get('all') === 'true'; // 新增：显示所有任务的参数
        const autoSync = url.searchParams.get('autoSync') !== 'false'; // 默认启用自动同步
        
        if (!email) {
            return Response.json({ error: '邮箱参数不能为空' }, { status: 400 });
        }

        // 验证用户是否存在
        const userKey = `user:${email}`;
        const userData = await env.KV.get(userKey, { type: 'json' });
        
        if (!userData) {
            return Response.json({ error: '用户不存在' }, { status: 404 });
        }

        // 自动状态同步（在获取任务列表前）
        let syncSummary = null;
        if (autoSync) {
            try {
                console.log(`🔄 自动同步用户 ${email} 的任务状态...`);
                syncSummary = await autoSyncTaskStatus(env, email);
                console.log(`✅ 自动同步完成: 更新了 ${syncSummary.updatedTasks} 个任务`);
            } catch (syncError) {
                console.warn('自动状态同步失败:', syncError);
                // 同步失败不影响任务列表的返回
            }
        }

        // 优化版：直接从用户数据中获取任务ID列表，减少KV读取
        const userTasks = [];
        
        // 如果用户数据中有任务ID列表，直接使用
        if (userData.taskIds && Array.isArray(userData.taskIds)) {
            console.log(`用户 ${email} 有 ${userData.taskIds.length} 个任务ID记录`);
            
            // 根据参数决定获取的任务数量
            const taskIdsToProcess = showAll ? userData.taskIds : userData.taskIds.slice(-50); // 增加到50个，如果要显示全部则不限制
            
            for (const taskId of taskIdsToProcess) {
                try {
                    const task = await env.KV.get(`task:${taskId}`, { type: 'json' });
                    if (task && task.email === email) {
                        // 添加格式化字段
                        task.typeDisplay = task.type === 'file' ? '文件上传' : '网页链接';
                        task.createdAtFormatted = formatDateTime(task.createdAt);
                        if (task.completedAt) {
                            task.completedAtFormatted = formatDateTime(task.completedAt);
                        }
                        if (task.audioSize) {
                            task.audioSizeFormatted = formatFileSize(task.audioSize);
                        }
                        userTasks.push(task);
                    }
                } catch (error) {
                    console.warn(`读取任务 ${taskId} 失败:`, error);
                    // 继续处理其他任务
                }
            }
        } else {
            // 后备方案：使用原来的方法，但限制返回数量
            console.log(`用户 ${email} 没有任务ID列表，使用后备查询方法`);
            
            const taskList = await env.KV.list({ prefix: 'task:', limit: showAll ? 1000 : 200 }); // 根据参数调整限制
            let processedCount = 0;
            const maxProcess = showAll ? 200 : 100; // 根据参数调整处理数量
            
            for (const key of taskList.keys) {
                if (processedCount >= maxProcess) break;
                
                try {
                    const task = await env.KV.get(key.name, { type: 'json' });
                    if (task && task.email === email) {
                        // 添加格式化字段
                        task.typeDisplay = task.type === 'file' ? '文件上传' : '网页链接';
                        task.createdAtFormatted = formatDateTime(task.createdAt);
                        if (task.completedAt) {
                            task.completedAtFormatted = formatDateTime(task.completedAt);
                        }
                        if (task.audioSize) {
                            task.audioSizeFormatted = formatFileSize(task.audioSize);
                        }
                        userTasks.push(task);
                    }
                    processedCount++;
                } catch (error) {
                    console.warn(`读取任务 ${key.name} 失败:`, error);
                    processedCount++;
                }
            }
        }

        // 按创建时间倒序排列
        userTasks.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
        
        // 根据参数决定返回数量
        const limitedTasks = showAll ? userTasks : userTasks.slice(0, 50); // 增加到50个

        console.log(`为用户 ${email} 返回 ${limitedTasks.length} 个任务 (总共有 ${userTasks.length} 个任务)`);

        // 构建响应
        const response = {
            success: true,
            tasks: limitedTasks,
            total: limitedTasks.length,
            totalFound: userTasks.length,
            message: !showAll && limitedTasks.length === 50 ? '只显示最近50个任务，如需查看全部请添加参数 ?all=true' : undefined
        };

        // 如果启用了自动同步，包含同步信息
        if (autoSync && syncSummary) {
            response.syncInfo = {
                enabled: true,
                updatedTasks: syncSummary.updatedTasks,
                checkedTasks: syncSummary.totalTasks,
                message: syncSummary.updatedTasks > 0 ? 
                    `已自动更新 ${syncSummary.updatedTasks} 个任务状态` : 
                    '所有任务状态已是最新'
            };
        }

        return Response.json(response);

    } catch (error) {
        console.error('获取任务列表错误:', error);
        return Response.json({ error: '服务器内部错误' }, { status: 500 });
    }
}

// 自动状态同步函数
async function autoSyncTaskStatus(env, email) {
    const syncSummary = {
        totalTasks: 0,
        updatedTasks: 0,
        errorTasks: 0
    };

    try {
        // 获取需要同步的任务（最近创建或更新的未完成任务）
        const tasksToSync = await getTasksNeedingSync(env, email);
        syncSummary.totalTasks = tasksToSync.length;

        if (tasksToSync.length === 0) {
            return syncSummary;
        }

        console.log(`🔍 找到 ${tasksToSync.length} 个需要检查状态的任务`);

        // 限制并发处理数量
        const batchSize = 3;
        for (let i = 0; i < tasksToSync.length; i += batchSize) {
            const batch = tasksToSync.slice(i, i + batchSize);
            const batchPromises = batch.map(task => quickSyncTask(env, email, task));
            const batchResults = await Promise.allSettled(batchPromises);

            batchResults.forEach((result) => {
                if (result.status === 'fulfilled' && result.value.updated) {
                    syncSummary.updatedTasks++;
                } else if (result.status === 'rejected') {
                    syncSummary.errorTasks++;
                }
            });
        }

    } catch (error) {
        console.error('自动状态同步失败:', error);
        syncSummary.errorTasks++;
    }

    return syncSummary;
}

// 获取需要同步的任务（优化版本，扩大检查范围以修复状态不一致问题）
async function getTasksNeedingSync(env, email) {
    const tasks = [];

    try {
        const userKey = `user:${email}`;
        const userData = await env.KV.get(userKey, { type: 'json' });

        if (!userData?.taskIds || !Array.isArray(userData.taskIds)) {
            return tasks;
        }

        // 扩大检查范围：检查最近的30个任务，以捕获更多可能的状态不一致问题
        const recentTaskIds = userData.taskIds.slice(-30);
        const now = new Date();

        for (const taskId of recentTaskIds) {
            try {
                const task = await env.KV.get(`task:${taskId}`, { type: 'json' });

                if (task && task.email === email) {
                    // 扩大同步条件，包含更多可能需要修复的任务：
                    // 1. 状态为 PROCESSING 或 PENDING（原有条件）
                    // 2. 任务创建超过3分钟（降低门槛，更早检查）
                    // 3. 扩大时间范围到24小时（捕获更多历史问题）
                    const createdAt = new Date(task.createdAt);
                    const ageMinutes = (now - createdAt) / (1000 * 60);

                    const shouldSync =
                        (task.status === 'PROCESSING' || task.status === 'PENDING') &&
                        ageMinutes >= 3 &&
                        ageMinutes <= 1440; // 24小时 = 1440分钟

                    if (shouldSync) {
                        tasks.push(task);
                        console.log(`🔍 添加需要同步的任务: ${taskId}, 状态: ${task.status}, 年龄: ${Math.round(ageMinutes)}分钟`);
                    }
                }
            } catch (error) {
                console.warn(`检查任务 ${taskId} 失败:`, error);
            }
        }

        console.log(`📊 用户 ${email} 共检查了 ${recentTaskIds.length} 个任务，找到 ${tasks.length} 个需要同步的任务`);
    } catch (error) {
        console.error('获取需要同步的任务失败:', error);
    }

    return tasks;
}

// 快速同步单个任务
async function quickSyncTask(env, email, task) {
    const result = { taskId: task.id, updated: false };
    
    try {
        // 快速检查R2中是否有音频文件
        const hasAudio = await quickCheckAudioExists(env, email, task);
        
        if (hasAudio) {
            // 更新任务状态
            const updatedTask = {
                ...task,
                status: 'SUCCESS',
                progress: 100,
                completedAt: new Date().toISOString(),
                lastProgressUpdate: new Date().toISOString()
            };
            
            // 设置音频URL
            if (!updatedTask.audioUrl) {
                if (updatedTask.isEnhanced || updatedTask.totalChapters > 1) {
                    updatedTask.audioUrl = `/api/audio/playlist/${task.id}`;
                    updatedTask.playlistUrl = `/api/audio/playlist/${task.id}`;
                    updatedTask.metadataUrl = `/api/audio/metadata/${task.id}`;
                } else {
                    updatedTask.audioUrl = `/api/audio/${task.id}.mp3`;
                }
            }
            
            await env.KV.put(`task:${task.id}`, JSON.stringify(updatedTask));
            result.updated = true;
            
            console.log(`✅ 快速同步: 任务 ${task.id} 已更新为 SUCCESS`);
        }
    } catch (error) {
        console.error(`快速同步任务 ${task.id} 失败:`, error);
    }
    
    return result;
}

// 快速检查音频是否存在
async function quickCheckAudioExists(env, email, task) {
    try {
        // 使用用户ID解析器
        const resolver = new UserIdResolver(env);
        const userIdResult = await resolver.resolveUserId(email, task.id);
        
        if (!userIdResult.userId) {
            return false;
        }
        
        const userId = userIdResult.userId;
        
        // 优先检查最可能的路径
        const priorityPaths = [
            `users/${userId}/audiobooks/${task.id}/`,   // 增强版音频
            `users/${userId}/audios/${task.id}/`,       // 标准音频
            `users/${userId}/tasks/${task.id}/`         // 新的统一任务路径
        ];
        
        for (const path of priorityPaths) {
            try {
                // 只列出前几个文件，快速检查
                const listResult = await env.R2.list({
                    prefix: path,
                    limit: 5
                });
                
                if (listResult.objects && listResult.objects.length > 0) {
                    // 检查是否有音频或播放列表文件
                    const hasRelevantFiles = listResult.objects.some(obj => 
                        obj.key.endsWith('.mp3') || 
                        obj.key.endsWith('.json') ||
                        obj.key.endsWith('.wav') ||
                        obj.key.endsWith('.m4a')
                    );
                    
                    if (hasRelevantFiles) {
                        return true;
                    }
                }
            } catch (error) {
                continue;
            }
        }
        
        return false;
    } catch (error) {
        console.error('快速检查音频文件失败:', error);
        return false;
    }
}

// 获取单个任务详情
export async function onRequestPost({ request, env }) {
    try {
        const { taskId, email } = await request.json();
        
        if (!taskId || !email) {
            return Response.json({ error: '任务ID和邮箱不能为空' }, { status: 400 });
        }

        // 验证用户是否存在
        const userKey = `user:${email}`;
        const userData = await env.KV.get(userKey, { type: 'json' });
        
        if (!userData) {
            return Response.json({ error: '用户不存在' }, { status: 404 });
        }

        // 获取任务详情
        const taskKey = `task:${taskId}`;
        const task = await env.KV.get(taskKey, { type: 'json' });
        
        if (!task) {
            return Response.json({ error: '任务不存在' }, { status: 404 });
        }

        // 验证任务是否属于该用户
        if (task.email !== email) {
            return Response.json({ error: '无权访问该任务' }, { status: 403 });
        }

        // 如果任务状态不是最终状态，尝试快速同步
        if (!['SUCCESS', 'COMPLETED', 'FAILED', 'CANCELLED'].includes(task.status)) {
            try {
                const syncResult = await quickSyncTask(env, email, task);
                if (syncResult.updated) {
                    // 重新获取更新后的任务
                    const updatedTask = await env.KV.get(taskKey, { type: 'json' });
                    return Response.json({
                        success: true,
                        task: updatedTask,
                        syncInfo: {
                            updated: true,
                            message: '任务状态已自动更新'
                        }
                    });
                }
            } catch (syncError) {
                console.warn('单个任务快速同步失败:', syncError);
            }
        }

        return Response.json({
            success: true,
            task: task
        });

    } catch (error) {
        console.error('获取任务详情错误:', error);
        return Response.json({ error: '服务器内部错误' }, { status: 500 });
    }
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}