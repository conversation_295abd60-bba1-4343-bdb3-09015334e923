// 数据迁移工具 - 将KV存储的文件迁移到R2

export async function onRequestPost({ request, env }) {
    try {
        const { adminKey } = await request.json();
        
        if (adminKey !== 'migrate_admin_2025') {
            return Response.json({ error: '无权限访问' }, { status: 403 });
        }

        // 检查R2是否可用
        const hasR2 = !!env.R2;
        const hasKV = !!env.KV;

        if (!hasKV) {
            return Response.json({ error: 'KV存储不可用' }, { status: 500 });
        }

        const result = {
            success: true,
            timestamp: new Date().toISOString(),
            hasR2,
            hasKV,
            migrationStrategy: hasR2 ? 'R2' : 'KV_OPTIMIZED'
        };

        if (hasR2) {
            // R2可用，执行真正的迁移
            result.message = '正在迁移到R2存储...';
            
            try {
                // 获取所有现有的audiobook数据
                const audiobookList = await env.KV.list({ prefix: 'audiobook:' });
                result.totalBooks = audiobookList.keys.length;
                result.migratedBooks = 0;

                for (const key of audiobookList.keys) {
                    try {
                        const data = await env.KV.get(key.name);
                        if (data) {
                            const audiobook = JSON.parse(data);
                            
                            // 如果有音频文件，迁移到R2
                            if (audiobook.audioFiles && audiobook.audioFiles.length > 0) {
                                for (let i = 0; i < audiobook.audioFiles.length; i++) {
                                    const audioData = audiobook.audioFiles[i];
                                    if (audioData.data) {
                                        // 创建R2对象键
                                        const r2Key = `audiobooks/${audiobook.id}/chapter_${i + 1}.mp3`;
                                        
                                        // 上传到R2
                                        await env.R2.put(r2Key, audioData.data, {
                                            httpMetadata: {
                                                contentType: 'audio/mpeg'
                                            }
                                        });
                                        
                                        // 更新audiobook数据，保存R2路径而不是数据
                                        audiobook.audioFiles[i] = {
                                            chapterTitle: audioData.chapterTitle,
                                            r2Key: r2Key,
                                            size: audioData.data.length
                                        };
                                    }
                                }
                                
                                // 更新KV中的metadata
                                await env.KV.put(key.name, JSON.stringify(audiobook));
                                result.migratedBooks++;
                            }
                        }
                    } catch (error) {
                        console.error(`迁移 ${key.name} 失败:`, error);
                    }
                }
                
                result.status = 'completed';
                result.message = `成功迁移 ${result.migratedBooks} 本电子书到R2存储`;
                
            } catch (error) {
                result.error = error.message;
                result.status = 'failed';
            }
            
        } else {
            // R2不可用，优化KV存储结构
            result.message = '正在优化KV存储结构...';
            
            try {
                // 获取所有现有的audiobook数据
                const audiobookList = await env.KV.list({ prefix: 'audiobook:' });
                result.totalBooks = audiobookList.keys.length;
                result.optimizedBooks = 0;

                for (const key of audiobookList.keys) {
                    try {
                        const data = await env.KV.get(key.name);
                        if (data) {
                            const audiobook = JSON.parse(data);
                            
                            // 将大的音频文件分解成更小的块存储
                            if (audiobook.audioFiles && audiobook.audioFiles.length > 0) {
                                for (let i = 0; i < audiobook.audioFiles.length; i++) {
                                    const audioData = audiobook.audioFiles[i];
                                    if (audioData.data && audioData.data.length > 20 * 1024) { // 大于20KB的文件
                                        const chunkKey = `audio_chunk:${audiobook.id}:${i}`;
                                        
                                        // 将音频数据存储为单独的KV条目
                                        await env.KV.put(chunkKey, audioData.data);
                                        
                                        // 更新audiobook数据，引用chunk而不是直接存储数据
                                        audiobook.audioFiles[i] = {
                                            chapterTitle: audioData.chapterTitle,
                                            chunkKey: chunkKey,
                                            size: audioData.data.length
                                        };
                                    }
                                }
                                
                                // 更新KV中的metadata
                                await env.KV.put(key.name, JSON.stringify(audiobook));
                                result.optimizedBooks++;
                            }
                        }
                    } catch (error) {
                        console.error(`优化 ${key.name} 失败:`, error);
                    }
                }
                
                result.status = 'completed';
                result.message = `成功优化 ${result.optimizedBooks} 本电子书的KV存储结构`;
                
            } catch (error) {
                result.error = error.message;
                result.status = 'failed';
            }
        }

        return Response.json(result);

    } catch (error) {
        console.error('迁移失败:', error);
        return Response.json({ 
            error: error.message,
            stack: error.stack,
            timestamp: new Date().toISOString()
        }, { status: 500 });
    }
}

// 根据文件名获取内容类型
function getContentType(filename) {
    const ext = filename.split('.').pop().toLowerCase();
    const contentTypes = {
        'txt': 'text/plain',
        'pdf': 'application/pdf',
        'doc': 'application/msword',
        'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'epub': 'application/epub+zip',
        'mp3': 'audio/mpeg',
        'wav': 'audio/wav'
    };
    return contentTypes[ext] || 'application/octet-stream';
}

// 清理KV中的旧文件数据（可选）
export async function onRequestDelete({ request, env }) {
    try {
        const { adminKey, confirm } = await request.json();
        
        if (adminKey !== 'migrate_admin_2025' || confirm !== 'DELETE_OLD_FILES') {
            return Response.json({ error: '无权限或未确认删除' }, { status: 403 });
        }

        const result = await cleanupOldKVFiles(env);
        
        return Response.json({
            success: true,
            message: 'KV清理完成',
            ...result
        });

    } catch (error) {
        console.error('清理失败:', error);
        return Response.json({ error: error.message }, { status: 500 });
    }
}

async function cleanupOldKVFiles(env) {
    const results = {
        deletedFiles: 0,
        deletedAudios: 0,
        errors: []
    };

    // 获取所有文件键
    const fileList = await env.KV.list({ prefix: 'file:' });
    const audioList = await env.KV.list({ prefix: 'audio:' });

    console.log(`准备清理 ${fileList.keys.length} 个文件和 ${audioList.keys.length} 个音频...`);

    // 删除文件
    for (const key of fileList.keys) {
        try {
            await env.KV.delete(key.name);
            results.deletedFiles++;
        } catch (error) {
            results.errors.push(`删除文件失败 ${key.name}: ${error.message}`);
        }
    }

    // 删除音频
    for (const key of audioList.keys) {
        try {
            await env.KV.delete(key.name);
            results.deletedAudios++;
        } catch (error) {
            results.errors.push(`删除音频失败 ${key.name}: ${error.message}`);
        }
    }

    console.log(`清理完成: 文件 ${results.deletedFiles} 个, 音频 ${results.deletedAudios} 个`);
    
    return results;
} 