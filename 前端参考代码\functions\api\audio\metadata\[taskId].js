// 书籍元数据 API
import { EnhancedR2StorageManager } from '../../../utils/enhanced-r2-storage.js';

export async function onRequestGet({ request, env, params }) {
    try {
        const { taskId } = params;
        
        if (!taskId) {
            return Response.json({ error: '缺少任务ID' }, { status: 400 });
        }

        // 从请求头获取用户邮箱
        const email = request.headers.get('X-User-Email');
        if (!email) {
            return Response.json({ error: '未授权访问' }, { status: 401 });
        }

        // 初始化存储管理器
        const r2Storage = new EnhancedR2StorageManager(env.R2);

        // 获取书籍元数据
        const metadata = await r2Storage.getBookMetadata(email, taskId);
        
        if (!metadata) {
            return Response.json({ error: '书籍元数据不存在' }, { status: 404 });
        }

        // 增强元数据，添加统计信息
        const enhancedMetadata = {
            ...metadata,
            statistics: {
                totalDurationFormatted: formatDuration(metadata.totalDuration),
                totalSizeFormatted: formatFileSize(metadata.totalSize),
                averageChapterDuration: Math.ceil(metadata.totalDuration / metadata.totalChapters),
                averageChapterSize: Math.ceil(metadata.totalSize / metadata.totalChapters),
                averageWordsPerChapter: Math.ceil(metadata.totalWordCount / metadata.totalChapters)
            },
            chapterSummary: metadata.chapters.map(chapter => ({
                index: chapter.index,
                title: chapter.title,
                durationFormatted: formatDuration(chapter.duration),
                sizeFormatted: formatFileSize(chapter.size),
                wordCount: chapter.wordCount,
                isPartial: chapter.isPartial,
                originalTitle: chapter.originalTitle,
                partInfo: chapter.isPartial ? `第${chapter.partIndex}部分，共${chapter.totalParts}部分` : null
            }))
        };

        return Response.json(enhancedMetadata, {
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, OPTIONS',
                'Access-Control-Allow-Headers': 'X-User-Email',
                'Cache-Control': 'public, max-age=3600' // 1小时缓存
            }
        });

    } catch (error) {
        console.error('获取书籍元数据失败:', error);
        return Response.json({ error: '服务器内部错误' }, { status: 500 });
    }
}

// 格式化时长（秒转换为时分秒）
function formatDuration(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;
    
    if (hours > 0) {
        return `${hours}小时${minutes}分钟${remainingSeconds}秒`;
    } else if (minutes > 0) {
        return `${minutes}分钟${remainingSeconds}秒`;
    } else {
        return `${remainingSeconds}秒`;
    }
}

// 格式化文件大小
function formatFileSize(bytes) {
    const sizes = ['B', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 B';
    
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    const size = (bytes / Math.pow(1024, i)).toFixed(2);
    
    return `${size} ${sizes[i]}`;
}

// 处理OPTIONS请求（CORS预检）
export async function onRequestOptions() {
    return new Response(null, {
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, OPTIONS',
            'Access-Control-Allow-Headers': 'X-User-Email',
            'Access-Control-Max-Age': '86400'
        }
    });
} 