/**
 * 增强版任务状态同步API
 * 使用混合方案：检查R2完成标记文件来同步KV状态
 */

import { R2StorageManager } from '../utils/r2-storage.js';
import { UserIdResolver } from '../utils/user-id-resolver.js';

export async function onRequestPost({ request, env }) {
    try {
        const { email, taskIds, autoMode = false } = await request.json();
        
        if (!email) {
            return Response.json({ error: '邮箱参数不能为空' }, { status: 400 });
        }

        console.log(`🔄 开始增强版状态同步: ${email}, 任务数: ${taskIds?.length || '全部'}`);

        const r2Storage = new R2StorageManager(env.R2);
        const userIdResolver = new UserIdResolver(env);
        
        // 获取需要检查的任务列表
        let tasksToCheck = [];
        
        if (taskIds && taskIds.length > 0) {
            // 检查指定任务
            for (const taskId of taskIds) {
                const taskKey = `task:${taskId}`;
                const task = await env.KV.get(taskKey, { type: 'json' });
                if (task && task.email === email) {
                    tasksToCheck.push(task);
                }
            }
        } else {
            // 获取用户所有未完成任务
            const allTasksResponse = await getAllUserTasks(env, email);
            tasksToCheck = allTasksResponse.tasks.filter(task => 
                task.status !== 'SUCCESS' && 
                task.status !== 'FAILED' && 
                task.status !== 'CANCELLED'
            );
        }

        console.log(`📋 需要检查的任务数量: ${tasksToCheck.length}`);

        if (tasksToCheck.length === 0) {
            return Response.json({
                success: true,
                message: '没有需要同步的任务',
                summary: {
                    checkedTasks: 0,
                    updatedTasks: 0,
                    completedTasks: [],
                    errors: []
                }
            });
        }

        // 批量检查完成标记
        const taskIdsToCheck = tasksToCheck.map(task => task.id);
        const completionMarkers = await r2Storage.batchCheckCompletionMarkers(email, taskIdsToCheck);
        
        const summary = {
            checkedTasks: tasksToCheck.length,
            updatedTasks: 0,
            completedTasks: [],
            errors: []
        };

        // 处理每个有完成标记的任务
        for (const marker of completionMarkers) {
            try {
                const task = tasksToCheck.find(t => t.id === marker.taskId);
                if (!task) continue;

                console.log(`✅ 发现完成标记，同步任务状态: ${marker.taskId}`);

                // 验证音频文件确实存在
                const audioExists = await verifyAudioFiles(r2Storage, userIdResolver, email, task);
                
                if (audioExists.exists) {
                    // 更新KV中的任务状态
                    const updatedTask = {
                        ...task,
                        status: 'SUCCESS',
                        progress: 100,
                        completedAt: marker.markerData.completedAt,
                        lastProgressUpdate: new Date().toISOString(),
                        syncedAt: new Date().toISOString(),
                        syncMethod: 'completion-marker'
                    };

                    // 设置音频URL
                    if (!updatedTask.audioUrl) {
                        if (updatedTask.isEnhanced || updatedTask.totalChapters > 1) {
                            updatedTask.audioUrl = `/api/audio/playlist/${task.id}`;
                            updatedTask.playlistUrl = `/api/audio/playlist/${task.id}`;
                            updatedTask.metadataUrl = `/api/audio/metadata/${task.id}`;
                        } else {
                            updatedTask.audioUrl = `/api/audio/${task.id}.mp3`;
                        }
                    }

                    // 保存更新后的任务状态
                    const taskKey = `task:${task.id}`;
                    await env.KV.put(taskKey, JSON.stringify(updatedTask));

                    // 删除完成标记文件（清理）
                    await r2Storage.removeCompletionMarker(email, marker.taskId);

                    summary.updatedTasks++;
                    summary.completedTasks.push({
                        taskId: marker.taskId,
                        filename: task.filename,
                        completedAt: marker.markerData.completedAt,
                        audioInfo: audioExists
                    });

                    console.log(`🎉 任务状态已同步: ${marker.taskId} -> SUCCESS`);
                } else {
                    console.warn(`⚠️ 完成标记存在但音频文件不存在: ${marker.taskId}`);
                    summary.errors.push({
                        taskId: marker.taskId,
                        error: '完成标记存在但音频文件不存在'
                    });
                }
            } catch (error) {
                console.error(`处理任务 ${marker.taskId} 时出错:`, error);
                summary.errors.push({
                    taskId: marker.taskId,
                    error: error.message
                });
            }
        }

        const message = summary.updatedTasks > 0 
            ? `成功同步了 ${summary.updatedTasks} 个任务的状态`
            : '没有发现需要同步的任务';

        return Response.json({
            success: true,
            message: message,
            summary: summary,
            autoMode: autoMode
        });

    } catch (error) {
        console.error('增强版状态同步失败:', error);
        return Response.json({ 
            error: '状态同步失败', 
            details: error.message 
        }, { status: 500 });
    }
}

/**
 * 获取用户所有任务
 */
async function getAllUserTasks(env, email) {
    try {
        // 使用现有的任务API逻辑
        const { list } = await env.KV.list({ prefix: 'task:' });
        const tasks = [];

        for (const key of list.keys) {
            try {
                const task = await env.KV.get(key.name, { type: 'json' });
                if (task && task.email === email) {
                    tasks.push(task);
                }
            } catch (error) {
                console.warn(`跳过无效任务: ${key.name}`, error);
            }
        }

        return { tasks };
    } catch (error) {
        console.error('获取用户任务失败:', error);
        return { tasks: [] };
    }
}

/**
 * 验证音频文件是否真实存在
 */
async function verifyAudioFiles(r2Storage, userIdResolver, email, task) {
    try {
        const userIdResult = await userIdResolver.resolveUserId(email, task.id);
        if (!userIdResult.userId) {
            return { exists: false, reason: '无法解析用户ID' };
        }

        const userId = userIdResult.userId;
        
        // 检查不同可能的音频文件路径
        const possiblePaths = [
            `users/${userId}/audios/${task.id}/audio.mp3`,      // 标准音频路径
            `users/${userId}/audiobooks/${task.id}/`,           // 增强版音频目录
            `users/${userId}/audio/${task.id}.mp3`,             // 旧格式1
            `users/${userId}/audio/${task.id}/audio.mp3`,       // 旧格式2
            `users/${userId}/tasks/${task.id}/audio.mp3`,       // 任务目录中的音频
            `users/${userId}/uploads/${task.id}/audio.mp3`      // 上传目录中的音频（兼容）
        ];

        for (const path of possiblePaths) {
            const exists = await r2Storage.fileExists(path);
            if (exists) {
                return {
                    exists: true,
                    audioPath: path,
                    pathType: 'single-file'
                };
            }
        }

        // 检查增强版音频（播放列表）
        const playlistPaths = [
            `users/${userId}/audio/${task.id}/playlist.m3u8`,
            `users/${userId}/tasks/${task.id}/playlist.m3u8`
        ];

        for (const path of playlistPaths) {
            const exists = await r2Storage.fileExists(path);
            if (exists) {
                return {
                    exists: true,
                    playlistPath: path,
                    pathType: 'enhanced'
                };
            }
        }

        return { exists: false, reason: '未找到音频文件' };
    } catch (error) {
        console.error('验证音频文件失败:', error);
        return { exists: false, reason: error.message };
    }
}
