// 优化版任务日志记录工具 - 减少KV写入操作
import { LoggingConfig, shouldLogToKV, isConsoleOnly, cleanLogData } from '../config/logging-config.js';

export class OptimizedTaskLogger {
    constructor(taskId, env) {
        this.taskId = taskId;
        this.env = env;
        this.logKey = `task_log:${taskId}`;
        this.config = LoggingConfig;
        
        // 内存中缓存日志，批量写入KV
        this.logBuffer = [];
        this.lastKVWrite = 0;
        this.writeInterval = 30000; // 30秒批量写入一次
        
        // 只有这些级别的日志才会存储到KV
        this.kvStorageLevels = ['CRITICAL', 'ERROR', 'TASK_START', 'TASK_COMPLETE', 'TASK_FAILED'];
    }

    /**
     * 优化的日志记录 - 大幅减少KV写入
     */
    async log(level, message, data = {}, context = '') {
        const timestamp = new Date().toISOString();
        
        // 始终输出到控制台（不占用KV存储）
        console.log(`[${level}] ${this.taskId}: ${message}`, data);

        // 只有关键级别才存储到KV
        if (this.kvStorageLevels.includes(level)) {
            const logEntry = {
                timestamp,
                level,
                message,
                data: this.simplifyData(data, level),
                taskId: this.taskId
            };

            // 立即写入关键日志
            if (level === 'CRITICAL' || level === 'ERROR' || level === 'TASK_FAILED') {
                await this.writeToKV(logEntry);
            } else {
                // 其他日志加入缓存，批量写入
                this.logBuffer.push(logEntry);
                await this.flushIfNeeded();
            }
        }
    }

    /**
     * 简化数据，减少存储空间
     */
    simplifyData(data, level) {
        if (level === 'ERROR' || level === 'CRITICAL') {
            return {
                error: data.error || data.message,
                code: data.code,
                step: data.stepName
            };
        }
        
        if (level === 'TASK_COMPLETE') {
            return {
                duration: data.duration,
                fileSize: data.fileSize,
                audioLength: data.audioLength
            };
        }

        // 其他情况只保留最基本信息
        return {
            step: data.stepName,
            status: data.status
        };
    }

    /**
     * 写入KV存储
     */
    async writeToKV(logEntry) {
        try {
            const existingLogs = await this.env.KV.get(this.logKey, { type: 'json' }) || [];
            existingLogs.push(logEntry);
            
            // 只保留最近50条记录
            if (existingLogs.length > 50) {
                existingLogs.splice(0, existingLogs.length - 50);
            }
            
            await this.env.KV.put(this.logKey, JSON.stringify(existingLogs));
            this.lastKVWrite = Date.now();
        } catch (error) {
            console.error('写入KV日志失败:', error);
        }
    }

    /**
     * 批量刷新缓存到KV
     */
    async flushIfNeeded() {
        const now = Date.now();
        if (this.logBuffer.length > 0 && (now - this.lastKVWrite > this.writeInterval || this.logBuffer.length >= 10)) {
            await this.flushBuffer();
        }
    }

    /**
     * 强制刷新缓存
     */
    async flushBuffer() {
        if (this.logBuffer.length === 0) return;

        try {
            const existingLogs = await this.env.KV.get(this.logKey, { type: 'json' }) || [];
            existingLogs.push(...this.logBuffer);
            
            // 只保留最近50条记录
            if (existingLogs.length > 50) {
                existingLogs.splice(0, existingLogs.length - 50);
            }
            
            await this.env.KV.put(this.logKey, JSON.stringify(existingLogs));
            this.logBuffer = [];
            this.lastKVWrite = Date.now();
        } catch (error) {
            console.error('批量写入KV日志失败:', error);
        }
    }

    // 简化的方法 - 大部分只输出到控制台
    async info(message, data = {}) {
        console.log(`[INFO] ${this.taskId}: ${message}`, data);
    }

    async warn(message, data = {}) {
        console.log(`[WARN] ${this.taskId}: ${message}`, data);
    }

    async error(message, data = {}) {
        await this.log('ERROR', message, data);
    }

    async debug(message, data = {}) {
        console.log(`[DEBUG] ${this.taskId}: ${message}`, data);
    }

    // 进度更新 - 只输出到控制台，不存储到KV
    async progress(progress, message, data = {}) {
        console.log(`[PROGRESS] ${this.taskId}: ${progress}% - ${message}`, data);
    }

    // 步骤记录 - 只输出到控制台
    async stepStart(stepName, description = '') {
        console.log(`[STEP_START] ${this.taskId}: ${stepName}`, { description });
    }

    async stepComplete(stepName, duration = null, data = {}) {
        console.log(`[STEP_COMPLETE] ${this.taskId}: ${stepName}`, { duration, ...data });
    }

    async stepFailed(stepName, error, data = {}) {
        await this.log('ERROR', `步骤失败: ${stepName}`, { 
            stepName, 
            error: error.message,
            ...data 
        });
    }

    // 关键事件 - 存储到KV
    async critical(message, data = {}) {
        await this.log('CRITICAL', message, data);
    }

    async taskStart(taskInfo) {
        await this.log('TASK_START', '任务开始', {
            filename: taskInfo.filename,
            email: taskInfo.email,
            startTime: new Date().toISOString()
        });
    }

    async taskComplete(taskInfo) {
        await this.flushBuffer(); // 确保所有日志都写入
        await this.log('TASK_COMPLETE', '任务完成', taskInfo);
    }

    async taskFailed(error, taskInfo = {}) {
        await this.flushBuffer(); // 确保所有日志都写入
        await this.log('TASK_FAILED', '任务失败', {
            error: error.message,
            ...taskInfo
        });
    }

    // 获取日志 - 保持兼容性
    async getLogs() {
        try {
            return await this.env.KV.get(this.logKey, { type: 'json' }) || [];
        } catch (error) {
            console.error('获取日志失败:', error);
            return [];
        }
    }

    async clearLogs() {
        try {
            await this.env.KV.delete(this.logKey);
            this.logBuffer = [];
        } catch (error) {
            console.error('清除日志失败:', error);
        }
    }
} 